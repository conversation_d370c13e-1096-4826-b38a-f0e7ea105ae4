import prisma from "@/lib/prisma";
import { emailService } from "@/lib/services";
import { adminLogger } from "@/lib/services/admin-log.service";
import { JobLifecycleService } from "@/lib/services/job-lifecycle.service";
import { smsService } from "@/lib/services/sms.service";

jest.mock("@/lib/services", () => ({
	emailService: {
		send: jest.fn(),
		batchSend: jest.fn(),
	},
}));

jest.mock("@/lib/services/sms.service", () => ({
	smsService: {
		send: jest.fn(),
	},
}));

jest.mock("@/lib/services/admin-log.service", () => ({
	adminLogger: {
		log: jest.fn(),
	},
}));

describe("JobLifecycleService - Provider Reminders", () => {
	let jobLifecycleService: JobLifecycleService;

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();
		jobLifecycleService = new JobLifecycleService(emailService, smsService);
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	describe("send24HourProviderReminders", () => {
		it("should return early when functionality is disabled", async () => {
			// Call the service method
			await jobLifecycleService.send24HourProviderReminders();

			// Verify that the method returns early and doesn't perform any operations
			expect(prisma.quote.findMany).not.toHaveBeenCalled();
			expect(emailService.batchSend).not.toHaveBeenCalled();
			expect(smsService.sendToProvider).not.toHaveBeenCalled();
			expect(prisma.quote.update).not.toHaveBeenCalled();
		});

		it("should not log any messages when disabled", async () => {
			// Call the service method
			await jobLifecycleService.send24HourProviderReminders();

			// Verify that no logging occurs
			expect(adminLogger.log).not.toHaveBeenCalled();
		});

		it("should not throw any errors when disabled", async () => {
			// Call the service method and expect it to complete without errors
			await expect(jobLifecycleService.send24HourProviderReminders()).resolves.not.toThrow();
		});
	});
});
