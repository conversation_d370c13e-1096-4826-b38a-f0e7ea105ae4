generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
    id            String  @id @default(cuid())
    member_number Int?
    email         String  @unique
    phone         String?
    first_name    String
    last_name     String

    newsletter_subscribed <PERSON>olean @default(false)

    latitude  Float?
    longitude Float?

    password   String?
    role       String  @default("USER")
    avatar     String?
    rv_details Json?

    email_verified_at       DateTime?
    onboarding_completed_at DateTime?
    membership_level        RVHelpMembershipLevel @default(FREE)
    welcome_email_sent      <PERSON><PERSON><PERSON>               @default(false)

    created_at  DateTime  @default(now())
    updated_at  DateTime  @updatedAt
    last_login  DateTime?
    mrr_user_id String?


    // Relations
    stripe_connection             StripeConnection?
    payouts                       Payout[]
    reviews                       Review[]
    verification_tokens           VerificationToken[]
    verification_requests         VerificationRequest[]
    interactions                  ListingInteraction[]
    owner_id                      String?
    owned_listings                Listing[]                    @relation("ListingOwner")
    user_listings                 UserListing[]                @relation("ListingManager")
    listing_interactions_sessions ListingInteractionSession[]
    notification_preferences      UserNotificationPreferences?
    notifications                 Notification[]
    user_terms_acceptance         UserTermsAcceptance[]
    audit_logs                    AuditLog[]
    articles                      Article[]
    moderated_reviews             Review[]                     @relation("ModeratedReviews")
    read_articles                 ArticleRead[]
    troubleshooting_requests      TroubleshootingRequest[]
    virtual_diagnostic_requests   VirtualDiagnosticRequest[]
    user_journey_sessions         UserJourneySession[]
    user_journey_events           UserJourneyEvent[]
    favorites                     UserFavorite[]
    jobs                          Job[]
    membership                    Membership?
    timeline_updates              TimelineUpdate[]
    devices                       UserDevice[]
    membership_offers             MembershipOffer[]

    // These are for tracking referrals
    // Referred by
    referred_by_id String?
    referred_by    User?   @relation("ReferralChain", fields: [referred_by_id], references: [id])

    // Referral Code
    referral_code String @unique @default(nanoid(8))

    // Referral Stats
    referral_visits  Int @default(0)
    referral_signups Int @default(0)

    // Monthly Referral Stats (stored as JSON: { "YYYY-MM": { visits: number, signups: number } })
    monthly_referral_stats Json @default("{}")

    // Referred Users
    referred_users User[] @relation("ReferralChain")

    transactions Transaction[]

    // OEM portal
    company_id                   String?
    company                      Company?          @relation("CompanyUsers", fields: [company_id], references: [id])
    customer_warranty_requests   WarrantyRequest[] @relation("CustomerWarrantyRequests")
    oem_warranty_requests        WarrantyRequest[] @relation("OEMWarrantyRequests")
    certification_records        ProviderCertificationRecord[] @relation("UserCertifications")

    // Admin notes for internal use only
    admin_notes                  String?       @db.Text

    @@map("users")
}

model Listing {
  id   String @id @default(cuid())
  slug String @unique

  // Personal Information
  first_name        String  @db.VarChar(255)
  last_name         String  @db.VarChar(255)
  profile_image     String? @db.VarChar(255)
  short_description String? @db.VarChar(170)
  long_description  String? @db.Text

  // Business Information
  business_name    String  @db.VarChar(255)
  logo             String? @db.VarChar(255)
  year_established String?

  // Contact Information
  phone   String? @db.VarChar(255)
  email   String? @db.VarChar(255)
  website String? @db.VarChar(255)

  // Categories
  categories Json?

  // Verification & Status
  profile_completed          Boolean                 @default(false)
  rv_help_verification_level RVHelpVerificationLevel @default(NONE)
  verified_member_number     Int?
  certified_member_number    Int?
  has_insurance              Boolean                 @default(false)
  claim_requested            Boolean                 @default(false)
  claim_status               String                  @default("unclaimed")
  stripe_enabled             Boolean                 @default(false)

  // Ratings & Reviews
  rating      Float?
  num_reviews Int    @default(0)

  // Pricing Settings
  pricing_settings Json?

  // Communication Preferences
  phone_available        Boolean @default(true)
  email_available        Boolean @default(true)
  require_lead_for_phone Boolean @default(false)

  // Settings fields
  settings_messaging_enabled               Boolean   @default(false)
  settings_display_pricing_info            Boolean   @default(false)
  settings_virtual_diagnosis               Boolean   @default(false)
  settings_virtual_diagnosis_notifications Boolean   @default(true)
  settings_enable_instant_booking          Boolean   @default(false)
  settings_dispatch_emails_opt_out          Boolean   @default(false)

  last_virtual_diagnosis_prompt_at         DateTime?

  // Pro Discount Settings
  discount_dispatch_fee Boolean @default(false)
  discount_hourly_rate  Boolean @default(false)

  // Notification Contact Information
  notification_sms   String?
  notification_email String?

  is_active Boolean @default(true)

  // Status & Deletion Tracking
  status          ListingStatus @default(ACTIVE)
  deleted_at      DateTime?
  deletion_reason String?
  admin_notes     String?       @db.Text

  // Certifications & Professional Info
  certifications              Json?
  manual_certifications       Json?
  partner_certifications      String[]  @default([])
  rvtaa_member_id             String?
  rvtaa_member_ids            String[]  @default([])
  rvtaa_members               Json?
  rvtaa_technician_level      Int?
  rvtaa_technician_id         String?
  rvtaa_technician_issue_date DateTime?
  nrvia_inspector_id          String?
  nrvia_inspector_level       Int?
  nrvia_inspector_issue_date  DateTime?

  // MY RV RESOURCE
  mrr_user_id     String?
  mrr_member_slug String?

  // Availability
  business_hours Json?

  // Location
  locations Location[]

  // Address - this is the physical address of the business, not used for search
  address_line_1 String?
  address_line_2 String?
  city           String?
  state          String?
  postcode       String?
  country        String?

  // Relations
  google_place_id      String? @default("")
  google_rating        Float?
  google_reviews_count Int?

  users                         UserListing[]               @relation("ListingManager")
  owner_id                      String?
  owner                         User?                       @relation("ListingOwner", fields: [owner_id], references: [id])
  reviews                       Review[]
  verification_requests         VerificationRequest[]
  qr_code_scans                 QRCodeScan[]
  verification_celebrations     VerificationCelebration[]
  interactions                  ListingInteraction[]
  listing_interactions_sessions ListingInteractionSession[]
  favorited_by                  UserFavorite[]
  quotes                        Quote[]
  invoices                      Invoice[]
  troubleshooting_requests      TroubleshootingRequest[]
  warranty_requests WarrantyRequest[] @relation("ListingWarrantyRequests")
  provider_stats                ProviderStats?
  certification_records         ProviderCertificationRecord[] @relation("ListingCertifications")

  // Timestamps
  created_at     DateTime  @default(now())
  updated_at     DateTime  @updatedAt
  published_at   DateTime?
  invite_sent_at DateTime?

  email_verified_at DateTime?
  sms_verified_at   DateTime?

  last_google_sync DateTime?

  views              Int @default(0)
  views_last_30_days Int @default(0)

  vacation_mode Json?
  provider_transactions Transaction[] @relation("ProviderTransactions")


  // Provider Performance
  is_highly_responsive Boolean @default(false)

  @@map("listings")
}

model Location {
  id                String    @id @default(cuid())
  address_line_1    String?
  address_line_2    String?
  city              String?
  state             String?
  postcode          String?
  country           String?   @default("US")
  latitude          Float
  longitude         Float
  formatted_address String?
  radius            Float? // in miles
  emergency_radius  Float?
  inspection_radius Float?
  default           Boolean?  @default(false)
  start_date        DateTime?
  end_date          DateTime?
  description       String?

  listing    Listing? @relation(fields: [listing_id], references: [id], onDelete: Cascade)
  listing_id String?

  @@map("locations")
}

// Add this new model
model Review {
  id String @id @default(cuid())

  // Review Information
  title            String
  content          String
  source           String  @default("rvhelp")
  google_review_id String? @unique
  google_place_id  String?
  mrr_review_id    String? @unique
  profile_photo    String?
  language         String?
  author_name      String?
  service_category String?

  // Rating Information
  overall        Float
  service        Float
  responsiveness Float
  expertise      Float
  results        Float
  communication  Float

  // User Information
  first_name String?
  last_name  String?
  email      String?
  reply      String?

  // Verification Information
  verified           Boolean @default(false) // Whether this review is verified (connected to a service request)
  service_request_id String? // ID of the connected service request (Job, TroubleshootingRequest, VirtualDiagnosticRequest, or WarrantyRequest)

  // Moderation Information
  status                String    @default("draft")
  moderated_by_id       String?
  moderated_by          User?     @relation("ModeratedReviews", fields: [moderated_by_id], references: [id])
  moderation_notes      String?
  moderated_at          DateTime?
  moderation_attachment String?

  // Relations
  listing_id String
  listing    Listing @relation(fields: [listing_id], references: [id])
  user_id    String?
  user       User?   @relation(fields: [user_id], references: [id])

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([service_request_id])
  @@index([verified])
  @@map("reviews")
}

model QRCodeScan {
  id         String   @id @default(cuid())
  listing_id String
  scanned_at DateTime @default(now())
  user_agent String? // Browser/device info
  ip_address String? // Location tracking
  referrer   String? // Where the scan came from

  listing Listing @relation(fields: [listing_id], references: [id])

  @@index([listing_id])
  @@index([scanned_at])
  @@map("qr_code_scans")
}

model VerificationToken {
  id           String   @id @default(cuid())
  token        String   @unique
  expires      DateTime
  admin_id     String?
  user_id      String
  user         User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  type         String // "email_verification", "password_reset", etc.
  redirect_url String?
  client_type  String?
  created_at   DateTime @default(now())

  @@index([token])
  @@index([user_id])
  @@map("verification_tokens")
}

model Payout {
  id               String   @id @default(cuid())
  user_id          String
  user             User     @relation(fields: [user_id], references: [id])
  amount           Int
  currency         String   @default("usd")
  stripe_payout_id String   @unique
  status           String // pending, paid, failed, canceled
  failure_reason   String?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@index([user_id])
  @@index([status])
  @@map("payouts")
}

model VerificationRequest {
  id                String    @id @default(cuid())
  business_name     String
  listing_id        String
  listing           Listing   @relation(fields: [listing_id], references: [id], onDelete: Cascade)
  user_id           String?
  user              User?     @relation(fields: [user_id], references: [id])
  submitted_at      DateTime
  documents         Json?
  verification_type String
  status            String // Let's make this an enum
  notes             String?
  reviewed_by       String?
  reviewed_at       DateTime?
  audit_log         Json? // Add this for tracking all actions
  rejection_reason  String? // Add this for storing rejection reasons

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("verification_requests")
}

model VerificationCelebration {
  id         String                  @id @default(cuid())
  listing_id String
  listing    Listing                 @relation(fields: [listing_id], references: [id])
  level      RVHelpVerificationLevel
  message    String
  seen       Boolean                 @default(false)
  created_at DateTime                @default(now())

  @@map("verification_celebrations")
}

model StripeConnection {
  id                    String   @id @default(cuid())
  user_id               String   @unique
  user                  User     @relation(fields: [user_id], references: [id])
  stripe_account_id     String   @unique
  stripe_account_status String
  payments_enabled      Boolean  @default(false)
  details_submitted     Boolean  @default(false)
  payouts_enabled       Boolean  @default(false)
  needs_info_update     Boolean  @default(false)
  is_verified           Boolean  @default(false)
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt


  @@map("stripe_connections")
}

model Notification {
  id         String    @id @default(cuid())
  type       String
  data       Json
  read_at    DateTime?
  created_at DateTime  @default(now())
  user_id    String
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum RVHelpVerificationLevel {
  NONE
  PROFILE_COMPLETE
  VERIFIED
  CERTIFIED_PRO
}

enum RequestStatus {
  PENDING
  MATCHING
  ACCEPTED
  QUOTED
  COMPLETED
  CANCELLED
}

enum UrgencyLevel {
  REGULAR
  PRIORITY
  EMERGENCY
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  RESPONDED
}

enum ResponseType {
  VIEW_DETAILS
  NOT_AVAILABLE
  NEED_INFO
  SUBMIT_QUOTE
  SCHEDULE_CALL
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  RETRYING
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ServiceCategory {
  RV_REPAIR
  RV_INSPECTION
  RV_MAINTENANCE
  RV_WINTERIZATION
  RV_DETAILING
  RV_UPGRADES
  RV_EMERGENCY
  RV_OTHER
}

enum TokenType {
  EMAIL_VERIFICATION
  PASSWORD_RESET
}

enum RVTAInspectorLevel {
  NONE
  CERTIFIED
  MASTER
}

enum RVTATechnicianLevel {
  NONE
  REGISTERED
  CERTIFIED
  MASTER
}

enum Role {
  USER
  PROVIDER
  ADMIN
  OEM
}

enum ListingStatus {
    ACTIVE
    INELIGIBLE
    DELETED
    BANNED
    CLOSED
    SUSPENDED
}



enum ServicePreference {
  MOBILE
  SHOP
  EITHER
}

model UserNotificationPreferences {
  id      String @id @default(cuid())
  user_id String @unique
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  messages             Json      @default("{ \"email\": true, \"sms\": false }")
  requests             Json      @default("{ \"email\": true, \"sms\": false }")
  quotes               Json      @default("{ \"email\": true, \"sms\": false }")
  last_notification_at DateTime?

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("user_notification_preferences")
}

model ImportFailure {
  id             String   @id @default(cuid())
  import_type    String
  failed_records Json
  timestamp      DateTime @default(now())

  @@map("import_failures")
}

model EmailOutbox {
  id                 String   @id @default(cuid())
  to_email           String   @map("to_email") @db.VarChar(1000)
  from_email         String   @map("from_email") @db.VarChar(255)
  subject            String   @db.VarChar(1000)
  html               String   @db.Text
  text               String   @db.Text
  digest             String   @db.Text
  status             String   @db.VarChar(15)
  response           String   @db.VarChar(1000)
  params             String   @db.Text
  send_date          DateTime @map("send_date") @db.Timestamp
  revision_timestamp DateTime @default(now()) @updatedAt @map("revision_timestamp") @db.Timestamp

  @@map("email_outbox")
}

model NonDuplicatePair {
  id          String   @id @default(cuid())
  listing1_id String
  listing2_id String
  created_at  DateTime @default(now())

  @@unique([listing1_id, listing2_id])
  @@map("non_duplicate_pairs")
}

model UserListing {
  user_id     String
  listing_id  String
  role        UserRole
  permissions Json

  user    User    @relation("ListingManager", fields: [user_id], references: [id])
  listing Listing @relation("ListingManager", fields: [listing_id], references: [id])

  @@id([user_id, listing_id])
  @@map("user_listings")
}

enum UserRole {
  OWNER
  MANAGER
  STAFF
}

model SafelistEntry {
  id         String       @id @default(cuid())
  type       SafelistType
  value      String       @unique
  created_at DateTime     @default(now())
  created_by String?
  notes      String?      @db.Text
  expires_at DateTime?
  is_active  Boolean      @default(true)

  @@index([type, value])
  @@map("safelist_entries")
}

model ListingInteraction {
  id           String          @id @default(cuid())
  listing_id   String
  user_id      String? // Optional since visitors won't have a user_id
  session_id   String // To track unique visitors
  action_type  InteractionType
  funnel_stage Int
  created_at   DateTime        @default(now())
  metadata     Json? // For storing additional context

  listing Listing                    @relation(fields: [listing_id], references: [id])
  user    User?                      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  session ListingInteractionSession? @relation(fields: [session_id], references: [id])

  @@index([listing_id])
  @@index([user_id])
  @@index([session_id])
  @@map("listing_interactions")
}

model UserTermsAcceptance {
  id          String   @id @default(cuid())
  user_id     String?
  ip_address  String
  document    String
  version     String
  accepted_at DateTime @default(now())
  user        User?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("user_terms_acceptances")
}

enum InteractionType {
  SHOWN_IN_SEARCH
  PROFILE_VIEW
  CONTACT_INFO_VIEW
  WEBSITE_CLICK
  EMAIL_CLICK
  PHONE_CLICK
}

enum SafelistType {
  EMAIL
  DOMAIN
  PHONE
}

enum ReferralStatus {
  PENDING
  REGISTERED
  CONVERTED
}

model AdminLog {
  id         String   @id @default(cuid())
  message    String   @db.Text
  meta       Json?
  created_by String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([created_at])
  @@map("admin_logs")
}

model SystemLog {
  id         String   @id @default(cuid())
  message    String   @db.Text
  meta       Json?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([created_at])
  @@map("system_logs")
}

model AuditLog {
  id          String   @id @default(cuid())
  action      String // e.g., "listing.update", "review.delete"
  entity_type String // e.g., "Listing", "Review"
  entity_id   String // ID of the affected record
  user_id     String // Who performed the action
  changes     Json? // Before/after state
  ip_address  String?
  created_at  DateTime @default(now())

  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([entity_type, entity_id])
  @@index([user_id])
  @@index([created_at])
  @@map("audit_logs")
}

model ListingInteractionSession {
  id                   String   @id @default(cuid())
  session_id           String // From visitor_session_id in localStorage
  listing_id           String
  user_id              String? // Optional - user may start anonymous and convert later
  source               String // 'search', 'direct', 'referral', 'qr'
  first_interaction    DateTime @default(now())
  last_interaction     DateTime @updatedAt
  initial_funnel_stage Int
  current_funnel_stage Int
  converted_to_user    Boolean  @default(false)

  listing      Listing              @relation(fields: [listing_id], references: [id])
  user         User?                @relation(fields: [user_id], references: [id])
  interactions ListingInteraction[]

  @@index([session_id])
  @@index([listing_id])
  @@index([user_id])
  @@map("listing_interaction_sessions")
}

model UserJourneySession {
  id                String   @id @default(cuid())
  session_id        String // From visitor_session_id in localStorage
  user_id           String? // Optional - user may start anonymous and convert later
  referrer          String? // Initial referrer URL
  initial_page      String // First page visited
  first_interaction DateTime @default(now())
  last_interaction  DateTime @updatedAt
  converted_to_user Boolean  @default(false)

  user   User?              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  events UserJourneyEvent[]

  @@index([session_id])
  @@index([user_id])
  @@map("user_journey_sessions")
}

model UserJourneyEvent {
  id         String   @id @default(cuid())
  session_id String
  user_id    String?
  event_type String // FIRST_VISIT, SEARCH_PERFORMED, VIEWED_LISTING, etc.
  metadata   Json? // Additional context about the event
  created_at DateTime @default(now())

  session UserJourneySession @relation(fields: [session_id], references: [id], onDelete: Cascade)
  user    User?              @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([session_id])
  @@index([user_id])
  @@index([event_type])
  @@map("user_journey_events")
}

model Article {
    id                   String        @id @default(cuid())
    slug                 String        @unique
    title                String
    content              String        @db.Text
    description          String?       @db.Text
    type                 String // "press-release", "blog-post", "job-posting"
    status               String        @default("draft") // "draft", "published"
    pinned               Boolean       @default(false) // Whether the article should be pinned to the top
    is_featured_provider Boolean       @default(false) // Whether the blog post is featured on homepage
    author_id            String?
    author               User?         @relation(fields: [author_id], references: [id])
    published_at         DateTime?
    created_at           DateTime      @default(now())
    updated_at           DateTime      @updatedAt
    meta_image           String? // For social sharing/OG images
    tags                 String[] // Array of tags/categories
    zoom_url             String? // For provider training zoom links
    youtube_url          String? // For provider training YouTube links
    read_by              ArticleRead[] // Add relation to ArticleRead

    // Lead Magnet Integration
    lead_magnet_id String?
    lead_magnet    LeadMagnet? @relation(fields: [lead_magnet_id], references: [id])

    @@index([type])
    @@index([status])
    @@index([author_id])
    @@index([pinned])
    @@index([is_featured_provider])
    @@index([lead_magnet_id])
    @@map("articles")
}

// Add this new model for tracking read articles
model ArticleRead {
  id         String   @id @default(cuid())
  article_id String
  user_id    String
  read_at    DateTime @default(now())

  article Article @relation(fields: [article_id], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([article_id, user_id])
  @@index([article_id])
  @@index([user_id])
  @@map("article_reads")
}

// Add this new model for favorites
model UserFavorite {
  id         String   @id @default(cuid())
  user_id    String
  listing_id String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [user_id], references: [id], onDelete: Cascade)
  listing Listing @relation(fields: [listing_id], references: [id], onDelete: Cascade)

  @@unique([user_id, listing_id])
  @@index([user_id])
  @@index([listing_id])
  @@map("user_favorites")
}

model Job {
  id      String @id @default(cuid())
  user_id String
  user    User   @relation(fields: [user_id], references: [id])

  // Contact Info
  first_name         String
  last_name          String
  email              String
  phone              String?
  contact_preference String  @default("sms") // sms or email
  sms_opt_in         Boolean @default(true) // SMS opt-in for A2P compliance

  // Location
  location Json?

  // Job Details
  message  String @db.Text
  category String
  source   String @default("web")

  // RV Details
  rv_year  String?
  rv_make  String?
  rv_model String?
  rv_type  String?

  // Status
  status            JobStatus @default(OPEN) // open, in_progress, completed, cancelled, expired
  sent_at           DateTime?
  error_message     String?
  viewed_at         DateTime? // When the user viewed the job details page
  is_premium        Boolean   @default(false)
  flagged_for_fraud Boolean   @default(false) // Flag for potentially fraudulent activity

  // Cron Job Fields - Customer Reminders
  offer_reminder_sent_at     DateTime?
  offer_reminder_48h_sent_at DateTime? // 48-hour offer reminder sent to customer
  follow_up_sent_at          DateTime?
  reminder_48h_sent_at       DateTime? // 48-hour reminder sent to customer
  reminder_1w_sent_at        DateTime? // 1-week reminder sent to customer
  expired_at                 DateTime? // When job was marked as expired

  // Relations
  quotes           Quote[] // All quotes received for this job
  transaction_id   String?
  transaction      Transaction?     @relation("JobTransaction", fields: [transaction_id], references: [id])
  timeline_updates TimelineUpdate[] // Timeline updates for this job

  // Invoice relationship (new structure)
  invoice_id String?  @unique
  invoice    Invoice? @relation(fields: [invoice_id], references: [id])

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  warranty_request_id String?          @unique
  warranty_request    WarrantyRequest? @relation(fields: [warranty_request_id], references: [id])

  accepted_quote_id String? @unique
  accepted_quote    Quote?  @relation("AcceptedQuote", fields: [accepted_quote_id], references: [id])

  @@unique([transaction_id])
  @@index([user_id])
  @@index([transaction_id])
  @@index([accepted_quote_id])
  @@index([flagged_for_fraud])
  @@index([invoice_id])
  @@map("jobs")
}

enum JobStatus {
  OPEN             // Accepting quotes
  ASSIGNED         // Quote accepted, work not started
  IN_PROGRESS      // Work actively happening  
  COMPLETED        // Work finished
  CANCELLED        // Job cancelled
  EXPIRED          // Job expired due to no responses
}

enum QuoteStatus {
  PENDING          // Provider invited, hasn't responded
  ACCEPTED         // Provider accepted lead
  REJECTED         // Provider rejected/declined
  CUSTOMER_REJECTED // Customer rejected provider's proposal
  IN_PROGRESS      // Work actively happening  
  COMPLETED        // Work finished
  WITHDRAWN        // Provider withdrew
  EXPIRED          // Timed out
}

enum ResolutionStatus {
  COMPLETED
  CANCELLED
  NO_RESPONSE
  NOT_VIABLE
  REFERRED
  OTHER
}

enum RejectionReasonType {
  TOO_BUSY
  NOT_A_GOOD_FIT
  SCHEDULE_CONFLICT
  OUTSIDE_TRAVEL_AREA
  OTHER
}

model Quote {
  id         String @id @default(cuid())
  job_id     String
  listing_id String

  // Status and Response
  status     QuoteStatus    @default(PENDING)
  invited_at DateTime  @default(now())
  responded_at  DateTime?  // renamed from quoted_at
  accepted_at   DateTime?  // when customer accepts  
  reviewed_at   DateTime?  // when customer submits review

  // price
  display_pricing     Boolean @default(true)
  discount_hourly_rate  Boolean @default(false)
  discount_dispatch_fee Boolean @default(false)
  hourly_rate         Float?
  dispatch_fee        Float?
  first_hour_included Boolean @default(false)

  // provider notes
  provider_notes       String? @db.Text

  // Rejection Reason
  rejection_reason         RejectionReasonType?
  rejection_reason_details String?

  // Communication
  messages         QuoteMessage[]
  reminder_sent_at DateTime? // Legacy 24-hour reminder
  reminder_72h_sent_at DateTime? // 72-hour reminder sent to provider
  reminder_1w_sent_at  DateTime? // 1-week reminder sent to provider
  last_viewed_at   DateTime?
  provider_viewed_at DateTime?

  // Review Request Fields
  review_requested    Boolean   @default(false)
  review_delay_hours  Int?
  review_requested_at DateTime?
  review_sent_at      DateTime?

  // Completion
  completed_at        DateTime?

  // Resolution Fields (from old leads)
  resolution_status ResolutionStatus?
  resolution_notes  String? @db.Text

  // Distance in miles between job and provider
  distance_miles Float?

  // Warranty terms acceptance
  warranty_terms_accepted_at DateTime?

  // Relations
  job        Job      @relation(fields: [job_id], references: [id], onDelete: Cascade)
  listing    Listing  @relation(fields: [listing_id], references: [id])
  accepted_job Job?   @relation("AcceptedQuote")
  // Timestamps
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([job_id, listing_id])
  @@index([job_id])
  @@index([listing_id])
  @@index([status])
  @@map("quotes")
}

model QuoteMessage {
  id       String             @id @default(cuid())
  quote_id String
  content  String             @db.Text
  status   QuoteMessageStatus @default(PENDING)
  type     QuoteMessageType   @default(TEXT)

  // For attachments
  attachments String[] // URLs to stored files
  metadata    Json?

  created_at   DateTime  @default(now())
  sent_at      DateTime?
  delivered_at DateTime?
  read_at      DateTime?

  // Clear sender/recipient pattern - explicit and unambiguous
  sender_id     String
  sender_type   QuoteMessageParticipantType
  recipient_id  String
  recipient_type QuoteMessageParticipantType

  // Relations
  quote Quote @relation(fields: [quote_id], references: [id])

  @@index([quote_id])
  @@index([sender_id])
  @@index([recipient_id])
  @@index([created_at])
  @@map("quote_messages")
}

model ProviderStats {
  id String @id @default(cuid())
  listing_id String @unique
  listing Listing @relation(fields: [listing_id], references: [id], onDelete: Cascade)
  
  // RESPONSE METRICS
  // 30-day rolling stats
  total_leads_30d Int @default(0)
  responded_leads_30d Int @default(0)
  response_rate_30d Float @default(0)
  avg_response_time_30d Float @default(0)
  
  // 90-day rolling stats  
  total_leads_90d Int @default(0)
  responded_leads_90d Int @default(0)
  response_rate_90d Float @default(0)
  avg_response_time_90d Float @default(0)
  
  // All-time stats
  total_leads_all_time Int @default(0)
  responded_leads_all_time Int @default(0)
  response_rate_all_time Float @default(0)
  avg_response_time_all_time Float @default(0)
  
  // COMPLETION METRICS
  // 30-day stats
  accepted_jobs_30d Int @default(0)
  completed_jobs_30d Int @default(0) 
  completion_rate_30d Float @default(0)
  avg_completion_time_30d Float @default(0)
  reviewed_jobs_30d Int @default(0)
  review_completion_rate_30d Float @default(0)
  
  // 90-day stats
  accepted_jobs_90d Int @default(0)
  completed_jobs_90d Int @default(0)
  completion_rate_90d Float @default(0) 
  avg_completion_time_90d Float @default(0)
  reviewed_jobs_90d Int @default(0)
  review_completion_rate_90d Float @default(0)
  
  // All-time stats
  accepted_jobs_all_time Int @default(0)
  completed_jobs_all_time Int @default(0)
  completion_rate_all_time Float @default(0)
  avg_completion_time_all_time Float @default(0)
  reviewed_jobs_all_time Int @default(0)
  review_completion_rate_all_time Float @default(0)
  
  // FAILURE METRICS
  // 30-day stats
  non_response_count_30d Int @default(0)
  non_response_rate_30d Float @default(0)
  abandonment_count_30d Int @default(0)
  abandonment_rate_30d Float @default(0)
  
  // 90-day stats  
  non_response_count_90d Int @default(0)
  non_response_rate_90d Float @default(0)
  abandonment_count_90d Int @default(0)
  abandonment_rate_90d Float @default(0)
  
  // All-time stats
  non_response_count_all_time Int @default(0)
  non_response_rate_all_time Float @default(0)
  abandonment_count_all_time Int @default(0)
  abandonment_rate_all_time Float @default(0)
  
  // Last updated
  last_calculated_at DateTime @default(now())
  
  // Timestamps
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([listing_id])
  @@map("provider_stats")
}

enum QuoteMessageStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  READ
}

enum QuoteMessageType {
  TEXT
  IMAGE
  DOCUMENT
  SYSTEM // For system-generated messages like status updates
}

enum QuoteMessageParticipantType {
  USER
  PROVIDER
}

model Invoice {
  id                  String           @id @default(cuid())
  invoice_number      Int              @unique @default(autoincrement())
  provider_id         String?
  provider            Listing?         @relation(fields: [provider_id], references: [id])
  notes               String?
  amount              Int // amount in cents
  currency            String           @default("usd")
  status              InvoiceStatus    @default(DRAFT)
  due_date            DateTime?
  customer_name       String
  customer_email      String
  customer_phone      String?

  // New relationships
  job                       Job? // Job relation through Job.invoice_id
  warranty_provider_request WarrantyRequest? @relation("WarrantyProviderInvoice")
  warranty_platform_request WarrantyRequest? @relation("WarrantyPlatformInvoice")

  items               InvoiceItem[]
  payment_transfer_id String?       @unique
  payment_intent_id   String?       @unique
  created_at          DateTime      @default(now())
  updated_at          DateTime      @updatedAt

  @@index([provider_id])
  @@map("invoices")
}


enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoice_id  String
  description String
  quantity    Float      @default(1)
  unit_price  Int // in cents
  amount      Int // in cents (unit_price * quantity)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  invoice     Invoice  @relation(fields: [invoice_id], references: [id], onDelete: Cascade)

  @@index([invoice_id])
  @@map("invoice_items")
}


model Transaction {
  id                    String    @id @default(cuid())
  created_at            DateTime  @default(now())
  updated_at            DateTime  @updatedAt
  amount                Float // Amount in dollars
  amount_in_cents       Int   // Amount in cents (for Stripe)
  currency              String    @default("usd")
  status                String // pending, completed, failed, refunded
  stripe_payment_id     String? // Stripe payment intent ID
  stripe_customer_id    String? // Stripe customer ID
  payment_method        String // card, ach, etc.
  payment_method_type   String? // credit_card, debit_card, etc.
  payment_method_last4  String? // Last 4 digits of card
  payment_method_brand  String? // Visa, Mastercard, etc.
  payment_method_expiry String? // MM/YY format
  description           String?
  metadata              Json? // Additional payment metadata
  error_message         String? // If payment failed
  refund_reason         String? // If refunded
  refunded_at           DateTime? // When refund was issued
  refund_amount         Float? // Amount refunded in dollars
  refund_amount_cents   Int? // Amount refunded in cents

  // Relations
  user_id       String
  user          User        @relation(fields: [user_id], references: [id])
  job_id        String?
  job           Job?        @relation("JobTransaction")
  membership_id String?
  membership    Membership? @relation(fields: [membership_id], references: [id])
  provider_id   String?
  provider      Listing?    @relation("ProviderTransactions", fields: [provider_id], references: [id])

  @@index([user_id])
  @@index([job_id])
  @@index([membership_id])
  @@index([provider_id])
  @@index([stripe_payment_id])
  @@index([created_at])
  @@map("transactions")
}

model SiteSettings {
  id         Int      @id @default(autoincrement())
  key        String   @unique
  value      Json
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("site_settings")
}

model EmptySearchResult {
  id            String   @id @default(cuid())
  search_params Json // Store all search parameters as JSON
  email         String?
  created_at    DateTime @default(now())

  @@map("empty_search_results")
}

enum RVHelpMembershipLevel {
    FREE
    STANDARD
    PREMIUM
}

model Membership {
    id      String @id @default(cuid())
    user_id String @unique
    user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

    // Membership details
    level         RVHelpMembershipLevel
    member_number Int?

    // Payment details
    stripe_session_id      String?
    stripe_subscription_id String?
    amount_paid            Int? // in cents
    currency               String? @default("usd")

    // Status
    is_active    Boolean   @default(true)
    cancelled_at DateTime?

    // Timestamps
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    transactions Transaction[]

    @@index([level])
    @@index([is_active])
    @@index([created_at])
    @@map("memberships")
}

model MembershipOffer {
    id          String   @id @default(cuid())
    user_id     String?  // Optional - for users who haven't registered yet
    email       String   // Required - to track offers for non-registered users
    
    // Offer details
    offer_type  String   // e.g., "ANNUAL_50_OFF", "WELCOME_25_OFF", "RETURNING_30_OFF"
    discount_percentage Int? // Percentage discount (e.g., 50 for 50% off)
    discount_amount     Int? // Fixed amount discount in cents (e.g., 5000 for $50 off)
    description String?  // Human-readable description
    
    // Status
    is_active   Boolean  @default(true)
    used_at     DateTime?
    expires_at  DateTime?
    
    // Tracking
    created_at  DateTime @default(now())
    updated_at  DateTime @updatedAt
    
    // Relations
    user User? @relation(fields: [user_id], references: [id], onDelete: Cascade)

    @@index([user_id])
    @@index([email])
    @@index([offer_type])
    @@index([is_active])
    @@index([expires_at])
    @@map("membership_offers")
}

model TroubleshootingRequest {
    id String @id @default(cuid())

    // Customer Information
    user_id           String
    user              User   @relation(fields: [user_id], references: [id], onDelete: Cascade)
    issue_description String @db.Text

    // Location Information
    location Json?

    // Customer Information
    first_name String
    last_name  String
    email      String
    phone      String

    // Status
    status            String    @default("pending") // pending, accepted, rejected, completed
    provider_notes    Json? // Array of { content: string, created_at: DateTime, type: "accept" | "reject" | "complete" }
    provider_response String?   @db.Text // The provider's response message to be included in the email
    scheduled_for     DateTime?
    completed_at      DateTime?

    // Review Request Fields
    review_requested    Boolean   @default(false)
    review_delay_hours  Int? // Delay in hours before sending review request
    review_requested_at DateTime? // When the review was requested
    review_sent_at      DateTime? // When the review email was actually sent

    // Relations
    listing_id String?
    listing    Listing? @relation(fields: [listing_id], references: [id])

    // Timestamps
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    @@index([listing_id, status])
    @@index([status])
    @@index([review_requested, review_sent_at])
    @@map("troubleshooting_requests")
}

model VirtualDiagnosticRequest {
    id String @id @default(cuid())

    // Customer Information
    user_id    String?
    user       User?   @relation(fields: [user_id], references: [id], onDelete: Cascade)
    first_name String
    last_name  String
    email      String
    phone      String

    // RV Details
    rv_type  String
    rv_make  String
    rv_year  String
    rv_model String

    // Issue Details
    symptoms         String  @db.Text
    when_started     String  @db.Text
    systems_affected String  @db.Text
    tried_fixes      String  @db.Text
    error_messages   String? @db.Text
    uploaded_files   Json?

    // Status
    status         String    @default("pending") // pending, scheduled, completed, cancelled
    scheduled_for  DateTime?
    completed_at   DateTime?
    provider_notes Json? // Array of { content: string, created_at: DateTime, type: "schedule" | "complete" | "cancel" }

    // Timestamps
    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    @@index([status])
    @@index([user_id])
    @@map("virtual_diagnostic_requests")
}

model Component {
    id String @id @default(cuid())
    type String
    manufacturer String
    notes String?
    attachments Json?
    company Company @relation(fields: [company_id], references: [id])
    company_id String
    warranty_requests WarrantyRequest[]
    @@map("components")
}


model Document {
  id String @id @default(cuid())
  original_id String?
  url String
  type String
  title String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  form_fields Json?

  @@map("documents")
}

model Company {
  id                String            @id @default(cuid())
  name              String
  brand_color       String?
  logo_url          String?
  logo_url_alt      String?
  logo_class_name   String?
  website           String?
  created_at        DateTime          @default(now())
  updated_at        DateTime          @updatedAt
  users             User[]            @relation("CompanyUsers")
  warranty_requests WarrantyRequest[]
  abbreviation      String?
  search_params     Json? // Store all search parameters as JSON
  components Component[]
  support_email String?
  support_phone String?

  @@map("companies")
}

model WarrantyRequest {
  id     String                @id @default(cuid())
  uuid   String                @unique
  status WarrantyRequestStatus @default(REQUEST_CREATED)

  // Request Details
  complaint          String                    @db.Text
  cause              String?                   @db.Text
  correction         String?                   @db.Text
  estimated_hours    Float?
  approved_hours     Float?
  actual_hours       Float?
  authorization_type WarrantyAuthorizationType @default(SPECIFIC)
  notes_to_provider  String?                   @db.Text

  // Contact Info
  first_name         String
  last_name          String
  email              String
  phone              String?
  contact_preference String? // sms or email   

  // Location
  location Json?

  // RV Details
  rv_vin   String
  rv_year  String?
  rv_make  String?
  rv_model String?
  rv_type  String?

  // Relations
  company_id String
  company    Company  @relation(fields: [company_id], references: [id])
  provider_invoice    Invoice? @relation("WarrantyProviderInvoice", fields: [provider_invoice_id], references: [id])
  provider_invoice_id String? @unique
  platform_invoice    Invoice? @relation("WarrantyPlatformInvoice", fields: [platform_invoice_id], references: [id])
  platform_invoice_id String? @unique

  oem_user_id String
  oem_user    User     @relation("OEMWarrantyRequests", fields: [oem_user_id], references: [id])
  listing_id  String?
  listing     Listing? @relation("ListingWarrantyRequests", fields: [listing_id], references: [id])
  customer_id String?
  customer    User?    @relation("CustomerWarrantyRequests", fields: [customer_id], references: [id])

  // Dates
  created_at              DateTime  @default(now())
  updated_at              DateTime  @updatedAt
  email_sent_at           DateTime?
  onboarding_completed_at DateTime?

  attachments              Json?
  attachments_acknowledged Boolean @default(false)
  requires_return          Boolean @default(false)  
  admin_notes String? @db.Text

  job_id String? @unique
  job    Job?

  component        Component?       @relation(fields: [component_id], references: [id])
  component_id     String?
  timeline_updates TimelineUpdate[]

  @@index([company_id])
  @@index([listing_id])
  @@index([customer_id])
  @@index([oem_user_id])
  @@index([component_id])
  @@index([provider_invoice_id])
  @@index([platform_invoice_id])
  @@map("warranty_requests")
}

model TimelineUpdate {
  id                  String                @id @default(cuid())
  warranty_request_id String?
  job_id              String?
  date                DateTime              @default(now())
  updated_by_id       String?
  updated_by          User?                  @relation(fields: [updated_by_id], references: [id]) // Added relation
  event_type          TimelineEventType
  details             Json? // Store all details as JSON

  warranty_request WarrantyRequest? @relation(fields: [warranty_request_id], references: [id])
  job              Job?             @relation(fields: [job_id], references: [id])

  @@index([warranty_request_id])
  @@index([job_id])
  @@map("timeline_updates")
}

enum WarrantyAuthorizationType {
  SPECIFIC
  GENERAL
}

enum WarrantyRequestStatus {
  REQUEST_CREATED
  REQUEST_APPROVED
  REQUEST_REJECTED
  JOB_REGISTERED
  JOB_REQUESTED
  JOB_ACCEPTED
  JOB_STARTED
  JOB_COMPLETED
  JOB_CANCELLED
  AUTHORIZATION_REQUESTED
  AUTHORIZATION_APPROVED
  AUTHORIZATION_REJECTED
  AUTHORIZATION_FEEDBACK
  PARTS_ORDERED
  INVOICE_CREATED
  INVOICE_PAID
}

enum TimelineEventType {
  PREAUTHORIZATION_APPROVED
  PREAUTHORIZATION_REJECTED
  CUSTOMER_REGISTERED
  TECHNICIAN_INVITED
  TECHNICIAN_ACCEPTED
  TECHNICIAN_REJECTED
  TECHNICIAN_REQUESTED_INFO
  TECHNICIAN_WITHDRAWN
  CUSTOMER_ACCEPTED
  CUSTOMER_REJECTED
  AUTHORIZATION_REQUESTED
  AUTHORIZATION_APPROVED
  AUTHORIZATION_REJECTED
  AUTHORIZATION_FEEDBACK
  PARTS_ORDERED
  TECHNICIAN_UPDATED
  JOB_STARTED
  JOB_COMPLETED
  JOB_CANCELLED
  JOB_PAUSED
  INVOICE_CREATED
  INVOICE_PAID
  ISSUE_RESOLVED
  REVIEW_REQUESTED
  REVIEW_LEFT
}

model CityContent {
    id    String @id @default(uuid())
    city  String @unique
    state String

    // Category-specific content stored as JSON
    // Structure: { "rv-repair": { title: string, content: string }, "rv-inspection": { title: string, content: string } }
    category_content Json? @default("{}")

    created_at DateTime @default(now())
    updated_at DateTime @updatedAt

    reviews Json? // Array of { text: string, provider: string, title: string }

    @@map("city_content")
}

model CityCanada {
    id             String  @id @default(cuid())
    city           String?
    alternate_name String?
    province_id    String?
    province_name  String?
    lat            String?
    lng            String?

    @@map("cities_canada")
}

model CityUnitedStates {
    id             String  @id @default(cuid())
    city           String?
    alternate_name String?
    state_id       String?
    state_name     String?
    lat            String?
    lng            String?

    @@map("cities_us")
}

enum DispatchEmailStatus {
    DRAFT
    SCHEDULED
    SENT
    FAILED
}


model DispatchEmail {
    id            String              @id @default(cuid())
    title         String
    subject       String
    body          String              @db.Text
    status        DispatchEmailStatus @default(DRAFT)
    sent_at       DateTime?
    scheduled_for DateTime?
    campaign_url  String?
    category      String? // Category to target (e.g., "rv-repair", "rv-inspection")
    recipients    Json? // Array of { listingId: string, email: string, sentAt: DateTime, status: "sent" | "failed", errorMessage?: string }
    created_at    DateTime            @default(now())
    updated_at    DateTime            @updatedAt

    @@index([status])
    @@index([scheduled_for])
    @@index([category])
    @@map("dispatch_emails")
}

model LeadMagnet {
    id              String   @id @default(cuid())
    title           String
    description     String   @db.Text
    image           String?
    newsletter_tags String[] @default([]) // Tags to add to newsletter subscribers
    status          String   @default("active") // "active", "inactive"
    created_at      DateTime @default(now())
    updated_at      DateTime @updatedAt

    // Relations
    articles Article[]

    @@index([status])
    @@map("lead_magnets")
}

enum BlacklistType {
    EMAIL
    DOMAIN
    USER_ID
    IP_ADDRESS
}

model BlacklistEntry {
    id         String        @id @default(cuid())
    type       BlacklistType
    value      String        @unique
    reason     String        @db.Text
    message    String?       @db.Text // Custom message to show to banned users
    created_at DateTime      @default(now())
    created_by String?
    expires_at DateTime?
    is_active  Boolean       @default(true)

    @@index([type, value])
    @@map("blacklist_entries")
}

model UserDevice {
  id           String   @id @default(cuid())
  user_id      String?  // Optional - for anonymous users
  device_token String   @unique
  platform     String   // "ios", "android"
  
  last_used_at DateTime @default(now())
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  user User? @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([device_token])
  @@map("user_devices")
}

enum MarketingCampaignStatus {
  DRAFT
  ACTIVE
  PAUSED
  EXPIRED
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

model MarketingCampaign {
  id          String   @id @default(cuid())
  slug        String   @unique
  title       String
  description String   @db.Text
  
  // Discount details
  discount_type   DiscountType
  discount_value  Float        // Percentage (0-100) or dollar amount
  
  // Coupon code
  coupon_code     String?      // Custom coupon code (optional, will auto-generate if not provided)
  
  // Campaign settings
  status      MarketingCampaignStatus @default(DRAFT)
  expires_at  DateTime?
  
  // Landing page customization
  page_title          String?
  page_subtitle       String?
  page_description    String?   @db.Text
  button_text         String?   @default("Get My Discount")
  success_message     String?   @db.Text
  
  // Email settings
  email_subject       String?
  email_template      String?   @db.Text
  
  // Visual assets
  background_image    String?
  logo                String?
  
  // Tracking
  views_count         Int       @default(0)
  leads_count         Int       @default(0)
  conversions_count   Int       @default(0)
  
  // Relations
  leads              MarketingCampaignLead[]
  
  // Timestamps
  created_at         DateTime  @default(now())
  updated_at         DateTime  @updatedAt
  
  @@index([slug])
  @@index([status])
  @@index([expires_at])
  @@map("marketing_campaigns")
}

model MarketingCampaignLead {
  id                    String   @id @default(cuid())
  campaign_id           String
  email                 String
  first_name            String?
  last_name             String?
  
  // Coupon details
  coupon_code           String   @unique
  coupon_sent_at        DateTime?
  coupon_used_at        DateTime?
  stripe_coupon_id      String?
  
  // Tracking
  ip_address            String?
  user_agent            String?
  referrer              String?
  utm_source            String?
  utm_medium            String?
  utm_campaign          String?
  
  // Relations
  campaign              MarketingCampaign @relation(fields: [campaign_id], references: [id], onDelete: Cascade)
  
  // Timestamps
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
  
  @@index([campaign_id])
  @@index([email])
  @@index([coupon_code])
  @@index([coupon_used_at])
  @@map("marketing_campaign_leads")
}

// Provider Certification System
model ProviderCertification {
  id                String                @id @default(cuid())
  name              String                @unique
  display_name      String
  description       String?               @db.Text
  training_content  Json?                 // Training module content
  terms_conditions  String?               @db.Text
  is_active         Boolean               @default(true)
  created_at        DateTime              @default(now())
  updated_at        DateTime              @updatedAt

  // Relations
  provider_certifications ProviderCertificationRecord[]

  @@map("provider_certifications")
}

model ProviderCertificationRecord {
  id                    String                @id @default(cuid())
  listing_id            String
  listing               Listing               @relation("ListingCertifications", fields: [listing_id], references: [id])
  certification_id      String
  certification         ProviderCertification @relation(fields: [certification_id], references: [id])
  user_id               String
  user                  User                  @relation("UserCertifications", fields: [user_id], references: [id])
  
  // Status tracking
  status                CertificationStatus   @default(PENDING)
  opted_out             Boolean               @default(false)
  opted_out_at          DateTime?
  opted_out_reason      String?               @db.Text
  
  // Training completion
  training_completed_at DateTime?
  terms_accepted_at     DateTime?
  terms_accepted_by     String?               // User ID who accepted terms
  
  // Metadata
  training_progress     Json?                 // Track progress through training modules
  notes                 String?               @db.Text
  
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt

  @@unique([listing_id, certification_id])
  @@index([listing_id])
  @@index([certification_id])
  @@index([user_id])
  @@index([status])
  @@map("provider_certification_records")
}

enum CertificationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  EXPIRED
  REVOKED
}

