import config from "@/config";
import prisma from "@/lib/prisma";
import { formatToE164 } from "@/lib/utils";
import { SafelistService, SafelistServiceConfig } from "@rvhelp/services";
import { Twi<PERSON> } from "twilio";
import type { MessageInstance } from "twilio/lib/rest/api/v2010/account/message";
import { adminLogger } from "./admin-log.service";

type DevModeResponse = {
	success: boolean;
	blocked: boolean;
	error: string;
};

export interface SmsService {
	send(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
	sendToProvider(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
	sendToUser(to: string, message: string): Promise<MessageInstance | DevModeResponse>;
}

export class SMSService implements SmsService {
	public client: Twilio | null = null;
	private safelistService: SafelistService;

	constructor() {
		if (config.twilio.accountSid && config.twilio.authToken) {
			this.client = new Twilio(
				config.twilio.accountSid,
				config.twilio.authToken
			);
		}

		// Initialize safelist service
		const safelistConfig: SafelistServiceConfig = {
			isDevelopment: config.isDevelopment,
			formatToE164: formatToE164
		};
		this.safelistService = new SafelistService(prisma, safelistConfig);
	}

	// General send method - defaults to provider sends for backward compatibility
	async send(
		to: string,
		message: string
	): Promise<MessageInstance | DevModeResponse> {
		return this.sendToProvider(to, message);
	}

	// Send SMS to providers (businesses/service providers)
	async sendToProvider(
		to: string,
		message: string
	): Promise<MessageInstance | DevModeResponse> {
		return this._sendSMS(to, message, config.twilio.providerFromNumber);
	}

	// Send SMS to users (customers/end users)
	async sendToUser(
		to: string,
		message: string
	): Promise<MessageInstance | DevModeResponse> {
		// Use user-specific phone number if configured, otherwise fall back to provider number
		const fromNumber = config.twilio.userFromNumber;
		return this._sendSMS(to, message, fromNumber);
	}

	// Private method to handle the actual SMS sending logic
	private async _sendSMS(
		to: string,
		message: string,
		fromNumber: string
	): Promise<MessageInstance | DevModeResponse> {
		if (!this.client) {
			throw new Error("SMS service not configured");
		}

		const formattedNumber = formatToE164(to);

		// Check safelist in development
		if (config.isDevelopment) {
			const isAllowed = await this.safelistService.isAllowed(
				"PHONE",
				formattedNumber
			);
			if (!isAllowed) {
				console.warn(
					`SMS sending blocked in development mode. Number not in safelist: ${formattedNumber}`
				);
				adminLogger.log("SMS not sent to number not in safelist in development mode: number: " + formattedNumber + " message: " + message);
				return {
					success: false,
					blocked: true,
					error:
						"SMS blocked - number not in safelist for development environment"
				};
			}
		}

		const twilio = this.client;
		if (!twilio) {
			throw new Error("SMS service not configured");
		}

		return twilio.messages.create({
			to: formattedNumber,
			from: fromNumber,
			body: message
		});
	}
}

export const smsService = new SMSService();
