import { InvoiceEmail } from "@/components/email-templates/InvoiceEmail";
import config from "@/config";
import prisma from "@/lib/prisma";
import { emailService, invoiceService } from "@/lib/services";
import { generateInvoicePDF } from "@/lib/utils/pdf";
import { Invoice } from "@rvhelp/database";
import React from "react";
import { smsService } from "./sms.service";

interface InvoiceWithBusiness extends Invoice {
	business: {
		name: string;
	};
	items: {
		description: string;
		quantity: number;
		unit_price: number;
		amount: number;
	}[];
}

export class InvoiceDeliveryService {
	/**
	 * Gets the URL for an invoice
	 */
	static getInvoiceURL(invoiceId: string) {
		// Use the app's base URL from environment variable, falling back to localhost in development
		const baseUrl = process.env.NEXT_PUBLIC_APP_URL;
		return `${baseUrl}/invoices/${invoiceId}`;
	}

	/**
	 * Sends an invoice via SMS using our SMS service
	 */
	static async sendInvoiceViaSMS(invoiceId: string, phoneNumber: string) {
		const invoice = await invoiceService.getInvoiceById(invoiceId);
		if (!invoice) {
			throw new Error("Invoice not found");
		}

		// Get the provider's name
		const provider = await prisma.user.findUnique({
			where: { id: invoice.provider_id }
		});

		if (!provider) {
			throw new Error("Provider not found");
		}

		const providerName = `${provider.first_name} ${provider.last_name}`;

		// Get the payment URL
		const paymentUrl = this.getInvoiceURL(invoiceId);

		// Send SMS with payment link
		await smsService.sendToProvider(
			phoneNumber,
			`You have a new invoice from ${providerName} for $${(invoice.amount / 100).toFixed(2)}. View and pay here: ${paymentUrl}`
		);

		return true;
	}

	/**
	 * Sends an invoice via email using our email service
	 */
	static async sendInvoiceViaEmail(invoiceId: string) {
		const invoice = await invoiceService.getInvoiceById(invoiceId);
		if (!invoice) {
			throw new Error("Invoice not found");
		}

		// Get the provider's name
		const provider = await prisma.listing.findUnique({
			where: { id: invoice.provider_id }
		});

		if (!provider) {
			throw new Error("Provider not found");
		}

		const providerName = `${provider.first_name} ${provider.last_name}`;

		// Create a simple email content with the invoice details
		const emailContent = React.createElement(InvoiceEmail, {
			invoice, // Include invoice details in the email as fallback
			providerName,
			invoiceUrl: `${config.appUrl}/pay/${invoice.id}`
		});

		// Prepare email options
		const emailOptions: any = {
			to: invoice.customer_email,
			subject: `Invoice #${invoice.invoice_number} from ${providerName}`,
			react: emailContent
		};

		// Try to generate the PDF invoice, but don't fail if it doesn't work
		try {
			// Get invoice items
			const invoiceItems = await prisma.invoiceItem.findMany({
				where: { invoice_id: invoice.id }
			});

			// Get listing information with owner
			const listing = await prisma.listing.findUnique({
				where: { id: invoice.provider_id },
				include: {
					owner: true
				}
			});

			if (!listing) {
				throw new Error("Listing not found");
			}

			const pdfBuffer = await generateInvoicePDF(
				invoice,
				invoiceItems,
				listing
			);
			emailOptions.attachments = [
				{
					filename: `invoice-${invoice.invoice_number}.pdf`,
					content: pdfBuffer,
					contentType: "application/pdf"
				}
			];
		} catch (error) {
			console.error("Error generating PDF invoice:", error);
			// Continue without the PDF attachment
		}

		// Send the email
		await emailService.send(emailOptions);

		return true;
	}
}
