import prisma from "@/lib/prisma";
import { adminLogger } from "@/lib/services/admin-log.service";
import { smsService } from "@/lib/services/sms.service";

export interface ProviderNotificationPayload {
    providers: string[]; // Array of provider IDs
    notificationType: 'virtual-troubleshooting';
    location?: {
        latitude: number;
        longitude: number;
        address: string;
    };
}

export async function processProviderNotification(payload: ProviderNotificationPayload) {
    try {
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

        // Fetch full provider details
        const providers = await prisma.listing.findMany({
            where: {
                id: { in: payload.providers },
                settings_virtual_diagnosis: false,
                settings_virtual_diagnosis_notifications: {
                    not: false // null or true
                },
                OR: [
                    { last_virtual_diagnosis_prompt_at: null },
                    { last_virtual_diagnosis_prompt_at: { lt: twentyFourHoursAgo } }
                ]
            }
        });

        // Send notifications to each provider
        for (const provider of providers) {
            try {
                const message = `RV Help has received a new request in your area.
                        
                    An RV Owner in your area has requested a 10 minute pre-service troubleshooting call but your listing is not set up to receive these requests.
            
                    Click here to update your listing to receive these requests: https://rvhelp.com/provider/${provider.id}/settings/virtual-troubleshooting/enable
            
                    If you do not want to receive these notifications, click here: https://rvhelp.com/provider/${provider.id}/settings/virtual-troubleshooting/disable-notifications
                `.trim();

                await smsService.sendToProvider(
                    provider.phone,
                    message
                );

                // Update the last prompt timestamp
                await prisma.listing.update({
                    where: { id: provider.id },
                    data: { last_virtual_diagnosis_prompt_at: new Date() }
                });

                adminLogger.log(`Sent notification to provider ${provider.id} prompting them to enable virtual troubleshooting`);

            } catch (error) {
                console.error(`Failed to send notification to provider ${provider.id}:`, error);
                adminLogger.log(`Failed to send notification to provider ${provider.id} prompting them to enable virtual troubleshooting: ${error}`);
            }
        }
    } catch (error) {
        console.error('Error processing provider notifications:', error);
        throw error;
    }
} 