import prisma from "@/lib/prisma";
import { ArrowRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const dynamic = "force-dynamic";

async function getArticles() {
	const articles = await prisma.article.findMany({
		where: {
			type: "blog-post",
			status: "published"
		},
		orderBy: {
			published_at: "desc"
		}
	});
	return articles;
}

export async function generateMetadata(): Promise<Metadata> {
	const articles = await getArticles();
	const latestArticle = articles[0];
	const ogImage =
		latestArticle?.meta_image || "https://rvhelp.com/blog-image.jpg";

	return {
		title: "RV Help Blog",
		description:
			"Insights, tips, and industry updates from RV Help - Your trusted source for RV maintenance and service information.",
		openGraph: {
			title: "RV Help Blog - RV Maintenance Tips & Industry Updates",
			description:
				"Insights, tips, and industry updates from RV Help - Your trusted source for RV maintenance and service information.",
			type: "website",
			url: "https://rvhelp.com/blog",
			images: [
				{
					url: ogImage,
					width: 1200,
					height: 630,
					alt: latestArticle?.title || "RV Help Blog"
				}
			]
		},
		twitter: {
			card: "summary_large_image",
			title: "RV Help Blog - RV Maintenance Tips & Industry Updates",
			description:
				"Insights, tips, and industry updates from RV Help - Your trusted source for RV maintenance and service information.",
			images: [ogImage]
		}
	};
}

export default async function BlogPage() {
	const articles = await getArticles();

	return (
		<div className="min-h-screen">
			<header className="bg-[#43806c] text-white py-8 sm:py-12 md:py-20 px-4">
				<div className="container mx-auto text-center">
					<h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 sm:mb-4">
						RV Help Blog
					</h1>
					<p className="text-base sm:text-lg md:text-xl max-w-2xl mx-auto px-2">
						Insights, tips, and industry updates from the RV Help team
					</p>
				</div>
			</header>

			<main className="container mx-auto py-8 sm:py-12 md:py-16 px-4">
				<div className="max-w-4xl mx-auto">
					<div className="grid gap-4 sm:gap-6">
						{articles.map((article) => (
							<div
								key={article.id}
								className="border rounded-lg p-4 sm:p-6 hover:border-[#43806c] transition-colors"
							>
								<Link href={`/blog/${article.slug}`} className="block">
									{/* Mobile-first layout: Stack vertically on mobile, horizontal on larger screens */}
									<div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
										{article.meta_image && (
											<div className="flex-shrink-0 w-full sm:w-48 h-48 sm:h-32">
												<img
													src={article.meta_image}
													alt={article.title}
													className="w-full h-full object-cover rounded-lg"
												/>
											</div>
										)}
										<div className="flex-1 min-w-0">
											<div className="flex flex-col gap-2">
												<p className="text-sm text-gray-500 mb-1 sm:mb-2">
													{article.published_at?.toLocaleDateString()}
												</p>
												<h2 className="text-lg sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 line-clamp-2">
													{article.title}
												</h2>
												<p className="text-gray-600 text-sm sm:text-base line-clamp-3 sm:line-clamp-2">
													{article.description}
												</p>
												{article.tags && article.tags.length > 0 && (
													<div className="flex flex-wrap gap-2 mt-2 sm:mt-3">
														{article.tags.map((tag) => (
															<span
																key={tag}
																className="text-xs sm:text-sm bg-gray-100 text-gray-600 px-2 py-1 rounded"
															>
																{tag}
															</span>
														))}
													</div>
												)}
											</div>
										</div>
										{/* Arrow icon - hidden on mobile, visible on larger screens */}
										<ArrowRight className="text-[#43806c] flex-shrink-0 hidden sm:block" />
									</div>
									{/* Mobile arrow indicator */}
									<div className="flex justify-end mt-3 sm:hidden">
										<ArrowRight className="text-[#43806c] w-4 h-4" />
									</div>
								</Link>
							</div>
						))}
					</div>
				</div>
			</main>
		</div>
	);
}
