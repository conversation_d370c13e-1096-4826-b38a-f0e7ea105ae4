import AdminEditContentButton from "@/components/AdminEditContentButton";
import { BlogNewsletterForm } from "@/components/BlogNewsletterForm";
import { LeadMagnetForm } from "@/components/LeadMagnetForm";
import { Button } from "@/components/ui/button";
import prisma from "@/lib/prisma";
import { ArrowLeft } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";

async function getArticleContent(slug: string) {
	const article = await prisma.article.findUnique({
		where: { slug, type: "blog-post" },
		include: {
			lead_magnet: true
		}
	});

	return article;
}

export async function generateMetadata({
	params
}: {
	params: { slug: string };
}): Promise<Metadata> {
	const article = await getArticleContent(params.slug);

	if (!article) {
		return {
			title: "Blog Post Not Found - RV Help Blog",
			description: "This blog post could not be found"
		};
	}

	const ogImage = article.meta_image || "https://rvhelp.com/blog-image.jpg";
	const ogTitle = `${article.title} - RV Help Blog`;
	const ogDescription =
		article.description || "Read this article on RV Help Blog";

	return {
		title: ogTitle,
		description: ogDescription,
		openGraph: {
			title: ogTitle,
			description: ogDescription,
			type: "article",
			url: `https://rvhelp.com/blog/${article.slug}`,
			images: [
				{
					url: ogImage,
					width: 1200,
					height: 630,
					alt: article.title
				}
			],
			publishedTime: article.published_at?.toISOString(),
			modifiedTime: article.updated_at?.toISOString()
		},
		twitter: {
			card: "summary_large_image",
			title: ogTitle,
			description: ogDescription,
			images: [ogImage]
		}
	};
}

export default async function BlogPostPage({
	params
}: {
	params: { slug: string };
}) {
	const article = await getArticleContent(params.slug);

	if (!article) {
		return notFound();
	}

	const leadMagnet = article.lead_magnet && article.lead_magnet.status === 'active'
		? article.lead_magnet
		: null;

	return (
		<div className="min-h-screen bg-gray-50">
			<AdminEditContentButton contentId={article.id} />
			<header className="bg-[#43806c] text-white py-12 px-4">
				<div className="container mx-auto">
					<div className="max-w-4xl mx-auto">
						<Link href="/blog">
							<Button variant="ghost" className="text-white mb-4">
								<ArrowLeft className="mr-2 h-4 w-4" />
								Back to Blog
							</Button>
						</Link>
						<h1 className="text-4xl font-bold mb-4">{article.title}</h1>
						<p className="text-lg opacity-90">{article.description}</p>
						<div className="mt-4 flex items-center gap-4">
							<p className="text-sm opacity-80">
								{article.published_at?.toLocaleDateString("en-US", {
									year: "numeric",
									month: "long",
									day: "numeric"
								})}
							</p>
							{article.tags && article.tags.length > 0 && (
								<div className="flex gap-2">
									{article.tags.map((tag) => (
										<span
											key={tag}
											className="text-sm bg-white/10 px-2 py-1 rounded"
										>
											{tag}
										</span>
									))}
								</div>
							)}
						</div>
					</div>
				</div>
			</header>

			<main className="container mx-auto py-16 px-4">
				<div className="max-w-6xl mx-auto">
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
						{/* Main Content */}
						<div className="lg:col-span-2">
							<div className="bg-white rounded-xl p-8 shadow-sm prose prose-lg max-w-none">
								{article.meta_image && (
									<img
										src={article.meta_image}
										alt={article.title}
										className="w-full h-auto rounded-lg mb-8"
									/>
								)}
								<div dangerouslySetInnerHTML={{ __html: article.content }} />
							</div>

							{/* Lead Magnet or Newsletter Form After Content */}
							<div className="mt-12">
								{leadMagnet ? (
									<LeadMagnetForm leadMagnet={leadMagnet} variant="compact" />
								) : (
									<>
										<h2 className="text-2xl font-bold text-center mb-8">
											Stay in the Loop
										</h2>
										<BlogNewsletterForm
											variant="compact"
											title="Never Miss an Update"
											description="Get the latest RV tips, maintenance guides, and industry insights delivered straight to your inbox."
										/>
									</>
								)}
							</div>
						</div>

						{/* Sidebar */}
						<div className="lg:col-span-1">
							{leadMagnet ? (
								<div className="sticky top-8">
									<LeadMagnetForm
										leadMagnet={leadMagnet}
										variant="sidebar"
										className="mb-8"
									/>
								</div>
							) : (
								<div className="sticky top-8">
									<BlogNewsletterForm
										variant="sidebar"
										title="RV Tips & Guides"
										description="Join thousands of RVers getting our weekly newsletter with maintenance tips and industry insights."
										className="mb-8"
									/>
								</div>
							)}
						</div>
					</div>
				</div>
			</main>
		</div>
	);
}
