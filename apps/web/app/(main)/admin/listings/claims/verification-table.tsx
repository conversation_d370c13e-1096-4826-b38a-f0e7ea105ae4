// @ts-nocheck
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { VerificationRequest } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import { Check, FileText, X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { VerificationActions } from "./verification-actions";
import { VerificationStatus } from "./verification-status";

export const VerificationTable = ({ status }: { status: string }) => {
	const [requests, setRequests] = useState<VerificationRequest[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectedRequest, setSelectedRequest] =
		useState<VerificationRequest | null>(null);
	const [showDocumentPreview, setShowDocumentPreview] = useState(false);

	const fetchRequests = async () => {
		try {
			const response = await fetch(
				`/api/admin/verification-requests?status=${status}`
			);
			const data = await response.json();
			setRequests(data.requests);
		} catch (error) {
			console.error("Error fetching requests:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchRequests();
	}, [status]);

	const handleAction = async (
		requestId: string,
		action: "approve" | "reject"
	) => {
		try {
			const response = await fetch(
				`/api/admin/verification-requests/${requestId}`,
				{
					method: "PATCH",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ action })
				}
			);

			if (!response.ok) throw new Error("Action failed");

			if (action === "approve") {
				toast.success(`Request approved successfully`);
			} else {
				toast.success(`Request rejected successfully`);
			}
			fetchRequests();
			setSelectedRequest(null);
		} catch (error) {
			console.error("Error:", error);
			toast.error(`Failed to ${action} request`);
		}
	};

	if (loading) {
		return <div>Loading...</div>;
	}

	return (
		<>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Business Name</TableHead>
						<TableHead>Submitted</TableHead>
						<TableHead>Type</TableHead>
						<TableHead>Status</TableHead>
						<TableHead className="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{requests.map((request) => (
						<TableRow
							key={request.id}
							className="cursor-pointer hover:bg-gray-50"
							onClick={() => setSelectedRequest(request)}
						>
							<TableCell className="font-medium">
								{request.business_name}
							</TableCell>
							<TableCell>
								{formatDistanceToNow(new Date(request.submitted_at))}
							</TableCell>
							<TableCell>{request.verification_type}</TableCell>
							<TableCell>
								<VerificationStatus status={request.status} />
							</TableCell>
							<TableCell className="text-right">
								<VerificationActions
									requestId={request.id}
									onActionComplete={fetchRequests}
								/>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>

			<Dialog
				open={!!selectedRequest}
				onOpenChange={() => setSelectedRequest(null)}
			>
				<DialogContent className="max-w-3xl">
					<DialogHeader>
						<DialogTitle>Claim Request Details</DialogTitle>
					</DialogHeader>
					{selectedRequest && (
						<>
							<div className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div>
										<h3 className="font-medium">Business Information</h3>
										<p>Name: {selectedRequest.business_name}</p>
										<p>
											Submitted:{" "}
											{formatDistanceToNow(
												new Date(selectedRequest.submitted_at)
											)}{" "}
											ago
										</p>
									</div>
									<div>
										<h3 className="font-medium">Contact Information</h3>
										<p>
											Name: {selectedRequest.documents?.first_name}{" "}
											{selectedRequest.documents?.last_name}
										</p>
										<p>Email: {selectedRequest.documents?.email}</p>
										<p>Phone: {selectedRequest.documents?.phone}</p>
									</div>
								</div>

								{selectedRequest.documents?.message && (
									<div>
										<h3 className="font-medium">Message</h3>
										<p>{selectedRequest.documents.message}</p>
									</div>
								)}

								{selectedRequest.documents?.business_license && (
									<div>
										<h3 className="font-medium">Documentation</h3>
										<Button
											variant="outline"
											onClick={() => setShowDocumentPreview(true)}
											className="mt-2"
										>
											<FileText className="w-4 h-4 mr-2" />
											View Document
										</Button>
									</div>
								)}
							</div>

							<div className="flex justify-end gap-2 mt-6 pt-4 border-t">
								<Button
									variant="destructive"
									onClick={() =>
										selectedRequest &&
										handleAction(selectedRequest.id, "reject")
									}
									data-testid="modal-reject-button"
								>
									<X className="w-4 h-4 mr-2" />
									Reject
								</Button>
								<Button
									variant="default"
									onClick={() =>
										selectedRequest &&
										handleAction(selectedRequest.id, "approve")
									}
									data-testid="modal-approve-button"
								>
									<Check className="w-4 h-4 mr-2" />
									Approve
								</Button>
							</div>
						</>
					)}
				</DialogContent>
			</Dialog>

			<Dialog open={showDocumentPreview} onOpenChange={setShowDocumentPreview}>
				<DialogContent className="max-w-4xl max-h-[90vh]">
					<DialogHeader>
						<DialogTitle>Document Preview</DialogTitle>
					</DialogHeader>
					{selectedRequest?.documents?.business_license && (
						<div className="overflow-auto max-h-[70vh]">
							<Image
								src={`data:${selectedRequest.documents?.business_license.type};base64,${selectedRequest.documents?.business_license.data}`}
								alt="Business License"
								width={1000}
								height={1000}
							/>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</>
	);
};
