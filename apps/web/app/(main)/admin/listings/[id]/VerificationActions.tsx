"use client";

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger
} from "@/components/ui/alert-dialog";

import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { ListingWithReviewsAndDistance } from "@/types/directory";
import { RVHelpVerificationLevel } from "@rvhelp/database";
import { toast } from "react-hot-toast";

export function VerificationActions({
	listing
}: {
	listing: ListingWithReviewsAndDistance;
}) {
	const { user } = useAuth();
	const isNotVerified =
		listing.rv_help_verification_level < RVHelpVerificationLevel.VERIFIED;

	if (!user || user.role !== "ADMIN" || isNotVerified) {
		return null;
	}

	const handleDowngrade = async () => {
		const confirmed = window.confirm(
			"This will downgrade the listing to &quot;Profile Complete&quot; status and send a notification email to the owner. Are you sure you want to continue?"
		);

		if (!confirmed) {
			return;
		}

		try {
			const response = await fetch(
				`/api/listings/${listing.id}/downgrade-verification`,
				{
					method: "POST"
				}
			);

			if (!response.ok) {
				throw new Error("Failed to downgrade verification");
			}

			toast.success("Verification level updated successfully");
		} catch (error) {
			toast.error("Failed to update verification level");
		}
	};

	return (
		<AlertDialog>
			<AlertDialogTrigger asChild>
				<Button size="sm" variant="destructive">
					Remove Verified Status
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Downgrade Verification Level</AlertDialogTitle>
					<AlertDialogDescription>
						This will downgrade the listing to &quot;Profile Complete&quot;
						status and send a notification email to the owner. Are you sure you
						want to continue?
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancel</AlertDialogCancel>
					<AlertDialogAction onClick={handleDowngrade}>
						Confirm Downgrade
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}
