"use client";

import AdminNotesDialog from "@/components/admin/AdminNotesDialog";
import ListingActivityFeed from "@/components/admin/ListingActivityFeed";
import Loader from "@/components/Loader";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { ListingWithLocation } from "@/types/global";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { RVHelpVerificationLevel } from "@rvhelp/database";
import debounce from "lodash/debounce";
import { Activity, Edit, Eye, StickyNote, Trash2 } from "lucide-react";
import Link from "next/link";
import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import { toast } from "react-hot-toast";
import DeleteListingDialog from "./DeleteListingDialog";
import MergeDialog from "./MergeDialog";
import QuickListingDialog from "./QuickListingDialog";

// Status Badge Component
function StatusBadge({ status }: { status: string }) {
	const getStatusConfig = (status: string) => {
		switch (status) {
			case "ACTIVE":
				return {
					label: "Active",
					className: "bg-green-100 text-green-800 border-green-200"
				};
			case "DELETED":
				return {
					label: "Deleted",
					className: "bg-red-100 text-red-800 border-red-200"
				};
			case "BANNED":
				return {
					label: "Banned",
					className: "bg-red-100 text-red-800 border-red-200"
				};
			case "CLOSED":
				return {
					label: "Closed",
					className: "bg-gray-100 text-gray-800 border-gray-200"
				};
			case "SUSPENDED":
				return {
					label: "Suspended",
					className: "bg-yellow-100 text-yellow-800 border-yellow-200"
				};
			default:
				return {
					label: status,
					className: "bg-gray-100 text-gray-800 border-gray-200"
				};
		}
	};

	const config = getStatusConfig(status);

	return (
		<span
			className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}
		>
			{config.label}
		</span>
	);
}

// Verification Badge Component
function VerificationBadge({ level }: { level: RVHelpVerificationLevel }) {
	const getVerificationConfig = (level: RVHelpVerificationLevel) => {
		switch (level) {
			case "NONE":
				return {
					label: "None",
					className: "bg-gray-100 text-gray-800 border-gray-200"
				};
			case "PROFILE_COMPLETE":
				return {
					label: "Profile Complete",
					className: "bg-blue-100 text-blue-800 border-blue-200"
				};
			case "VERIFIED":
				return {
					label: "Verified",
					className: "bg-green-100 text-green-800 border-green-200"
				};
			case "CERTIFIED_PRO":
				return {
					label: "Certified Pro",
					className: "bg-purple-100 text-purple-800 border-purple-200"
				};
		}
	};

	const config = getVerificationConfig(level);

	return (
		<span
			className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}
		>
			{config.label}
		</span>
	);
}

export default function ListingManagementClient() {
	const [listings, setListings] = useState<ListingWithLocation[]>([]);
	const [search, setSearch] = useState("");
	const [verificationFilter, setVerificationFilter] = useState<string>("all");
	const [isLoading, setIsLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [totalResults, setTotalResults] = useState(0);
	const [selectedListings, setSelectedListings] = useState<string[]>([]);
	const [showQuickCreateDialog, setShowQuickCreateDialog] = useState(false);
	const [showMergeDialog, setShowMergeDialog] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [listingToDelete, setListingToDelete] = useState<{
		id: string;
		name: string;
	} | null>(null);
	const [showAdminNotesDialog, setShowAdminNotesDialog] = useState(false);
	const [selectedListingForNotes, setSelectedListingForNotes] =
		useState<ListingWithLocation | null>(null);
	const [showActivityFeed, setShowActivityFeed] = useState(false);
	const [selectedListingForActivity, setSelectedListingForActivity] =
		useState<ListingWithLocation | null>(null);

	// Slug editing state
	const [editingSlug, setEditingSlug] = useState<string | null>(null);
	const [slugValue, setSlugValue] = useState<string>("");
	const [isUpdatingSlug, setIsUpdatingSlug] = useState(false);

	const fetchListings = useCallback(
		async (searchTerm: string, page: number, verification: string) => {
			setIsLoading(true);
			try {
				const params = new URLSearchParams({
					search: searchTerm,
					page: page.toString(),
					limit: "10"
				});

				if (verification !== "all") {
					params.append("verification", verification);
				}

				const response = await fetch(`/api/admin/listings?${params}`);
				if (response.ok) {
					const data = await response.json();
					setListings(data.listings);
					setTotalPages(data.totalPages);
					setTotalResults(data.totalResults || 0);
				} else {
					toast.error("Failed to fetch listings");
				}
			} catch (error) {
				console.error("Error fetching listings:", error);
				toast.error("An error occurred while fetching listings");
			} finally {
				setIsLoading(false);
			}
		},
		[]
	);

	const debouncedFetchListings = useMemo(
		() =>
			debounce(
				(searchTerm: string, page: number, verification: string) =>
					fetchListings(searchTerm, page, verification),
				300
			),
		[fetchListings]
	);

	useEffect(() => {
		debouncedFetchListings(search, currentPage, verificationFilter);
	}, [search, currentPage, verificationFilter, debouncedFetchListings]);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value);
		setCurrentPage(1);
	};

	const handleVerificationFilterChange = (value: string) => {
		setVerificationFilter(value);
		setCurrentPage(1);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleListingSelect = (listingId: string) => {
		setSelectedListings((prev) =>
			prev.includes(listingId)
				? prev.filter((id) => id !== listingId)
				: [...prev, listingId]
		);
	};

	const handleDeleteClick = (listingId: string, listingName: string) => {
		setListingToDelete({ id: listingId, name: listingName });
		setShowDeleteDialog(true);
	};

	const handleViewAdminNotes = (listing: ListingWithLocation) => {
		setSelectedListingForNotes(listing);
		setShowAdminNotesDialog(true);
	};

	const handleViewActivityFeed = (listing: ListingWithLocation) => {
		setSelectedListingForActivity(listing);
		setShowActivityFeed(true);
	};

	const handleSlugEdit = (listingId: string, currentSlug: string) => {
		setEditingSlug(listingId);
		setSlugValue(currentSlug);
	};

	const handleSlugCancel = () => {
		setEditingSlug(null);
		setSlugValue("");
	};

	const handleSlugSave = async (listingId: string) => {
		if (!slugValue.trim()) {
			toast.error("Slug cannot be empty");
			return;
		}

		setIsUpdatingSlug(true);
		try {
			const response = await fetch(`/api/admin/listings/${listingId}/slug`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({ slug: slugValue.trim() })
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to update slug");
			}

			// Update the listing in the state
			setListings((prev) =>
				prev.map((listing) =>
					listing.id === listingId
						? { ...listing, slug: data.listing.slug }
						: listing
				)
			);

			toast.success("Slug updated successfully");
			setEditingSlug(null);
			setSlugValue("");
		} catch (error) {
			console.error("Error updating slug:", error);
			toast.error(error.message || "Failed to update slug");
		} finally {
			setIsUpdatingSlug(false);
		}
	};

	const handleExportListings = async (
		type: "current" | "verified" | "unverified" | "all"
	) => {
		setIsLoading(true);
		try {
			const params = new URLSearchParams();

			// Always include the type parameter
			params.append("type", type);

			if (type === "current") {
				params.append("search", search);
				if (verificationFilter !== "all") {
					params.append("verification", verificationFilter);
				}
			}

			const response = await fetch(`/api/admin/listings/export?${params}`);
			if (!response.ok) throw new Error("Export failed");

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `listings-export-${type}-${
				new Date().toISOString().split("T")[0]
			}.csv`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);
		} catch (error) {
			console.error("Error exporting listings:", error);
			toast.error("Failed to export listings");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="">
			<h1 className="text-2xl font-bold mb-4">Listing Management</h1>
			<div className="flex justify-between mb-4">
				<div className="flex gap-2 w-full max-w-sm">
					<Input
						type="text"
						value={search}
						onChange={handleSearchChange}
						placeholder="Search by business name, owner, or location..."
						className="w-full"
					/>
					<select
						value={verificationFilter}
						onChange={(e) => handleVerificationFilterChange(e.target.value)}
						className="px-3 py-2 border border-gray-300 rounded-md text-sm"
					>
						<option value="all">All Verifications</option>
						<option value="NONE">Unverified</option>
						<option value="PROFILE_COMPLETE">Profile Complete</option>
						<option value="VERIFIED">Verified</option>
						<option value="CERTIFIED_PRO">Certified Pro</option>
					</select>
				</div>

				<div className="flex gap-2">
					{selectedListings.length >= 2 ? (
						<Button onClick={() => setShowMergeDialog(true)}>
							Merge Selected ({selectedListings.length})
						</Button>
					) : (
						<>
							<Button onClick={() => setShowQuickCreateDialog(true)}>
								Quick Create
							</Button>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button variant="outline">
										Export
										<ChevronDownIcon className="ml-2 h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent>
									<DropdownMenuItem
										onClick={() => handleExportListings("current")}
									>
										Current Results ({totalResults})
									</DropdownMenuItem>
									<DropdownMenuItem
										onClick={() => handleExportListings("verified")}
									>
										All Verified Listings
									</DropdownMenuItem>
									<DropdownMenuItem
										onClick={() => handleExportListings("unverified")}
									>
										All Unverified Listings
									</DropdownMenuItem>
									<DropdownMenuItem onClick={() => handleExportListings("all")}>
										All Listings
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
							<Link href="/admin/listings/duplicates">
								<Button variant="outline">View Potential Duplicates</Button>
							</Link>
						</>
					)}
				</div>
			</div>

			{isLoading ? (
				<div className="h-24">
					<Loader />
				</div>
			) : listings.length === 0 ? (
				<div className="text-center py-8">No listings found</div>
			) : (
				<div className="bg-white rounded-lg shadow">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead className="w-[30px]">
									{/* <input type="checkbox" disabled /> */}
								</TableHead>
								<TableHead>Name</TableHead>
								<TableHead>Business Name</TableHead>
								<TableHead>Slug</TableHead>
								<TableHead>Location</TableHead>
								<TableHead>Status</TableHead>
								<TableHead>Verification</TableHead>
								<TableHead className="w-[120px]">Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{listings.map((listing) => (
								<TableRow key={listing.id}>
									<TableCell>
										<input
											type="checkbox"
											checked={selectedListings.includes(listing.id)}
											onChange={() => handleListingSelect(listing.id)}
										/>
									</TableCell>
									<TableCell>
										{listing.first_name} {listing.last_name}
									</TableCell>
									<TableCell>{listing.business_name}</TableCell>
									<TableCell>
										{editingSlug === listing.id ? (
											<div className="flex items-center gap-2">
												<Input
													value={slugValue}
													onChange={(e) => setSlugValue(e.target.value)}
													className="h-8 text-sm"
													placeholder="Enter slug..."
													disabled={isUpdatingSlug}
													onKeyDown={(e) => {
														if (e.key === "Enter") {
															handleSlugSave(listing.id);
														} else if (e.key === "Escape") {
															handleSlugCancel();
														}
													}}
													autoFocus
												/>
												<div className="flex items-center gap-1">
													<Button
														size="sm"
														onClick={() => handleSlugSave(listing.id)}
														disabled={isUpdatingSlug}
														className="h-6 w-6 p-0 bg-green-600 hover:bg-green-700"
													>
														✓
													</Button>
													<Button
														size="sm"
														variant="outline"
														onClick={handleSlugCancel}
														disabled={isUpdatingSlug}
														className="h-6 w-6 p-0"
													>
														✕
													</Button>
												</div>
											</div>
										) : (
											<div className="flex items-center gap-2 group">
												<span className="font-mono text-sm text-gray-600">
													{listing.slug}
												</span>
												<Button
													size="sm"
													variant="ghost"
													onClick={() =>
														handleSlugEdit(listing.id, listing.slug)
													}
													className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
													title="Edit slug"
												>
													<Edit className="h-3 w-3" />
												</Button>
											</div>
										)}
									</TableCell>
									<TableCell>
										{listing.location
											? `${listing.location.city}, ${listing.location.state}`
											: "N/A"}
									</TableCell>
									<TableCell>
										<StatusBadge status={listing.status} />
									</TableCell>
									<TableCell>
										<VerificationBadge
											level={listing.rv_help_verification_level || "NONE"}
										/>
									</TableCell>
									<TableCell>
										<div className="flex items-center gap-1">
											{/* View Listing */}
											<Link href={`/providers/${listing.slug}`} target="_blank">
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title="View listing (opens in new tab)"
												>
													<Eye className="h-4 w-4" />
												</Button>
											</Link>

											{/* Edit Listing */}
											<Link
												href={`/provider/business/profile?id=${listing.id}`}
											>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title="Edit listing"
												>
													<Edit className="h-4 w-4" />
												</Button>
											</Link>

											{/* Admin Notes */}
											<div className="relative">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => {
														setSelectedListingForNotes(listing);
														setShowAdminNotesDialog(true);
													}}
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title={
														listing.admin_notes &&
														listing.admin_notes.trim().length > 0
															? "View admin notes"
															: "No admin notes - click to add"
													}
												>
													<StickyNote className="h-4 w-4" />
												</Button>
												{/* Badge indicator when there are notes */}
												{listing.admin_notes &&
													listing.admin_notes.trim().length > 0 && (
														<div className="absolute -top-0.5 -right-0.5 h-2.5 w-2.5 bg-blue-500 rounded-full border border-white" />
													)}
											</div>

											{/* Activity Feed */}
											<Button
												variant="ghost"
												size="sm"
												onClick={() => handleViewActivityFeed(listing)}
												className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
												title="View activity feed"
											>
												<Activity className="h-4 w-4" />
											</Button>

											{/* Delete Listing */}
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													handleDeleteClick(
														listing.id,
														`${listing.first_name} ${listing.last_name}`
													)
												}
												className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
												title="Delete listing"
											>
												<Trash2 className="h-4 w-4" />
											</Button>
										</div>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			)}

			{totalPages > 1 && (
				<div className="py-4">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						onPageChange={handlePageChange}
					/>
				</div>
			)}

			{showQuickCreateDialog && (
				<QuickListingDialog
					isOpen={showQuickCreateDialog}
					onClose={() => setShowQuickCreateDialog(false)}
					onSuccess={() => {
						fetchListings(search, currentPage, verificationFilter);
					}}
				/>
			)}

			{showMergeDialog && (
				<MergeDialog
					isOpen={showMergeDialog}
					onClose={() => {
						setShowMergeDialog(false);
						setSelectedListings([]);
					}}
					selectedListings={listings.filter((listing) =>
						selectedListings.includes(listing.id)
					)}
					onSuccess={() => {
						setShowMergeDialog(false);
						setSelectedListings([]);
						fetchListings(search, currentPage, verificationFilter);
					}}
				/>
			)}

			{showDeleteDialog && listingToDelete && (
				<DeleteListingDialog
					isOpen={showDeleteDialog}
					onClose={() => {
						setShowDeleteDialog(false);
						setListingToDelete(null);
					}}
					onSuccess={() => {
						setShowDeleteDialog(false);
						setListingToDelete(null);
						fetchListings(search, currentPage, verificationFilter);
					}}
					listingId={listingToDelete.id}
					listingName={listingToDelete.name}
				/>
			)}

			{showAdminNotesDialog && selectedListingForNotes && (
				<AdminNotesDialog
					isOpen={showAdminNotesDialog}
					onClose={() => {
						setShowAdminNotesDialog(false);
						setSelectedListingForNotes(null);
					}}
					title="Listing Admin Notes"
					entityType="listing"
					entityData={{
						id: selectedListingForNotes.id,
						name: `${selectedListingForNotes.first_name} ${selectedListingForNotes.last_name}`,
						email: selectedListingForNotes.email,
						business_name: selectedListingForNotes.business_name,
						admin_notes: selectedListingForNotes.admin_notes || null,
						created_at:
							typeof selectedListingForNotes.created_at === "string"
								? selectedListingForNotes.created_at
								: selectedListingForNotes.created_at.toISOString(),
						updated_at:
							typeof selectedListingForNotes.updated_at === "string"
								? selectedListingForNotes.updated_at
								: selectedListingForNotes.updated_at.toISOString()
					}}
					onNotesUpdated={() => {
						// Refresh the listings data to show updated notes
						fetchListings(search, currentPage, verificationFilter);
					}}
				/>
			)}

			{showActivityFeed && selectedListingForActivity && (
				<ListingActivityFeed
					isOpen={showActivityFeed}
					onClose={() => {
						setShowActivityFeed(false);
						setSelectedListingForActivity(null);
					}}
					listingId={selectedListingForActivity.id}
				/>
			)}
		</div>
	);
}
