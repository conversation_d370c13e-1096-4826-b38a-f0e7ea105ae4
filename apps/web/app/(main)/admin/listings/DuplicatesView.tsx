"use client";

import { AbsoluteLoader } from "@/components/Loader";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import { ListingWithLocation } from "@/types/global";
import debounce from "lodash/debounce";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "react-hot-toast";
import MergeDialog from "./MergeDialog";

interface ListingWithLocationAndOwner extends ListingWithLocation {
	owner?: {
		id: string;
		first_name: string;
		last_name: string;
		email: string;
	};
}

interface DuplicateGroup {
	listings: ListingWithLocationAndOwner[];
	similarity: number;
	reason: string;
}

export default function DuplicatesView() {
	const [duplicateGroups, setDuplicateGroups] = useState<DuplicateGroup[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [totalDuplicates, setTotalDuplicates] = useState(0);
	const [selectedListings, setSelectedListings] = useState<
		ListingWithLocationAndOwner[]
	>([]);
	const [showMergeDialog, setShowMergeDialog] = useState(false);
	const [search, setSearch] = useState("");
	const [reasonFilter, setReasonFilter] = useState("all");
	const [verificationFilter, setVerificationFilter] = useState("all");

	const fetchDuplicates = useCallback(
		async (
			page: number,
			searchTerm: string,
			reason: string,
			verification: string
		) => {
			setIsLoading(true);
			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: "10"
				});

				if (searchTerm) params.append("search", searchTerm);
				if (reason !== "all") params.append("reason", reason);
				if (verification !== "all") params.append("verification", verification);

				const response = await fetch(
					`/api/admin/listings/duplicates?${params}`
				);
				if (!response.ok) throw new Error("Failed to fetch duplicates");

				const data = await response.json();
				setDuplicateGroups(data.duplicateGroups);
				setTotalPages(data.totalPages);
				setTotalDuplicates(data.totalDuplicates);
			} catch (error) {
				console.error("Error fetching duplicates:", error);
				toast.error("Failed to fetch potential duplicates");
			} finally {
				setIsLoading(false);
			}
		},
		[]
	);

	const debouncedFetchDuplicates = useMemo(
		() =>
			debounce(
				(
					page: number,
					searchTerm: string,
					reason: string,
					verification: string
				) => fetchDuplicates(page, searchTerm, reason, verification),
				300
			),
		[fetchDuplicates]
	);

	useEffect(() => {
		debouncedFetchDuplicates(
			currentPage,
			search,
			reasonFilter,
			verificationFilter
		);
	}, [
		currentPage,
		search,
		reasonFilter,
		verificationFilter,
		debouncedFetchDuplicates
	]);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value);
		setCurrentPage(1);
	};

	const handleReasonFilterChange = (value: string) => {
		setReasonFilter(value);
		setCurrentPage(1);
	};

	const handleVerificationFilterChange = (value: string) => {
		setVerificationFilter(value);
		setCurrentPage(1);
	};

	const handleMarkNonDuplicate = async (
		listings: ListingWithLocationAndOwner[]
	) => {
		if (listings.length < 2) {
			toast.error(
				"Please select at least two listings to mark as non-duplicates"
			);
			return;
		}

		try {
			const response = await fetch("/api/admin/listings/mark-non-duplicate", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					listingIds: listings.map((l) => l.id)
				})
			});

			if (!response.ok) throw new Error("Failed to mark as non-duplicates");

			toast.success("Successfully marked as non-duplicates");
			// Refresh the duplicates list
			fetchDuplicates(currentPage, search, reasonFilter, verificationFilter);
		} catch (error) {
			console.error("Error marking non-duplicates:", error);
			toast.error("Failed to mark as non-duplicates");
		}
	};

	return (
		<div className="relative">
			{isLoading && <AbsoluteLoader />}

			{/* Filter section */}
			<div className="mb-6 space-y-4">
				<div className="flex justify-between items-center">
					<h2 className="text-lg font-semibold">Potential Duplicates</h2>
					<div className="text-sm text-muted-foreground">
						Total Potential Duplicates: {totalDuplicates}
					</div>
				</div>

				<div className="flex gap-4 flex-wrap">
					<div className="flex-1 min-w-64">
						<Input
							type="text"
							value={search}
							onChange={handleSearchChange}
							placeholder="Search by business name, email, phone, or owner..."
							className="w-full"
						/>
					</div>
					<div className="flex gap-2">
						<select
							value={reasonFilter}
							onChange={(e) => handleReasonFilterChange(e.target.value)}
							className="px-3 py-2 border border-gray-300 rounded-md text-sm"
						>
							<option value="all">All Reasons</option>
							<option value="business_name">Business Name Match</option>
							<option value="phone">Phone Match</option>
							<option value="email">Email Match</option>
						</select>
						<select
							value={verificationFilter}
							onChange={(e) => handleVerificationFilterChange(e.target.value)}
							className="px-3 py-2 border border-gray-300 rounded-md text-sm"
						>
							<option value="all">All Verifications</option>
							<option value="NONE">Unverified</option>
							<option value="PROFILE_COMPLETE">Profile Complete</option>
							<option value="VERIFIED">Verified</option>
							<option value="CERTIFIED_PRO">Certified Pro</option>
						</select>
					</div>
				</div>
			</div>

			{duplicateGroups.map((group) => (
				<Card key={group.listings[0].id} className="mb-8">
					<CardHeader>
						<CardTitle>Potential Duplicate Group</CardTitle>
						<CardDescription>
							Similarity: {(group.similarity * 100).toFixed(0)}%
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-2 gap-4">
							{group.listings.map((listing) => {
								const otherListing = group.listings.find(
									(l) => l.id !== listing.id
								);
								const matches = {
									business_name:
										otherListing?.business_name?.toLowerCase() ===
										listing.business_name?.toLowerCase(),
									email:
										otherListing?.email?.toLowerCase() ===
										listing.email?.toLowerCase(),
									phone: otherListing?.phone === listing.phone,
									rvtaa_member_id:
										otherListing?.rvtaa_member_id === listing.rvtaa_member_id,
									owner_email:
										otherListing?.owner?.email?.toLowerCase() ===
										listing.owner?.email?.toLowerCase()
								};

								return (
									<div key={listing.id} className="border rounded-lg p-4">
										<div className="flex justify-between items-start mb-4">
											<div>
												<h3
													className={cn(
														"font-semibold",
														matches.business_name &&
															"bg-yellow-100 px-1 rounded"
													)}
												>
													{listing.business_name}
												</h3>
												<p className="text-sm text-muted-foreground">
													{listing.location?.city}, {listing.location?.state}
												</p>
											</div>
											<Checkbox
												checked={selectedListings.includes(listing)}
												onCheckedChange={(checked) => {
													if (checked) {
														setSelectedListings([...selectedListings, listing]);
													} else {
														setSelectedListings(
															selectedListings.filter(
																(l) => l.id !== listing.id
															)
														);
													}
												}}
											/>
										</div>

										<div className="space-y-2 text-sm">
											<p
												className={cn(
													matches.phone && "bg-yellow-100 px-1 rounded"
												)}
											>
												Phone: {listing.phone || "N/A"}
											</p>
											<p
												className={cn(
													matches.email && "bg-yellow-100 px-1 rounded"
												)}
											>
												Email: {listing.email || "N/A"}
											</p>
											<p>Reviews: {listing.num_reviews}</p>
											<p
												className={cn(
													matches.rvtaa_member_id &&
														listing.rvtaa_member_id &&
														"bg-yellow-100 px-1 rounded"
												)}
											>
												RVTA Member ID: {listing.rvtaa_member_id || "N/A"}
											</p>
											<p>
												Verification Status:{" "}
												{listing.rv_help_verification_level || "NONE"}
											</p>

											{listing.owner && (
												<div className="mt-4 p-3 bg-muted rounded-md">
													<h4 className="font-medium mb-2">Owner Info</h4>
													<p>
														{listing.owner.first_name} {listing.owner.last_name}
													</p>
													<p
														className={cn(
															"text-muted-foreground",
															matches.owner_email &&
																"bg-yellow-100 px-1 rounded"
														)}
													>
														{listing.owner.email}
													</p>
												</div>
											)}
										</div>
									</div>
								);
							})}
						</div>
						<div className="mt-4 flex justify-between items-center">
							<div className="text-sm text-muted-foreground">
								Match Reason: {group.reason}
							</div>
							<div className="space-x-2">
								<Button
									variant="outline"
									onClick={() => {
										setSelectedListings(group.listings);
										setShowMergeDialog(true);
									}}
								>
									Merge
								</Button>
								<Button
									variant="outline"
									onClick={() => handleMarkNonDuplicate(group.listings)}
									disabled={group.listings.length < 2}
								>
									Not Duplicates
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			))}

			{totalPages > 1 && (
				<div className="mt-4 flex justify-center">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						onPageChange={setCurrentPage}
					/>
				</div>
			)}

			{showMergeDialog && (
				<MergeDialog
					isOpen={showMergeDialog}
					onClose={() => {
						setShowMergeDialog(false);
						setSelectedListings([]);
					}}
					selectedListings={selectedListings}
					onSuccess={() =>
						fetchDuplicates(
							currentPage,
							search,
							reasonFilter,
							verificationFilter
						)
					}
				/>
			)}
		</div>
	);
}
