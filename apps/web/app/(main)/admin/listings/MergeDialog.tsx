"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";

import { cn } from "@/lib/utils";
import { ListingWithLocation } from "@/types/global";
import { useState } from "react";
import { toast } from "react-hot-toast";

interface MergeDialogProps {
	isOpen: boolean;
	onClose: () => void;
	selectedListings: ListingWithLocation[];
	onSuccess: () => void;
}

export default function MergeDialog({
	isOpen,
	onClose,
	selectedListings,
	onSuccess
}: MergeDialogProps) {
	const [primaryListingId, setPrimaryListingId] = useState<string>("");
	const [isMerging, setIsMerging] = useState(false);

	const handleMerge = async () => {
		if (!primaryListingId) {
			toast.error("Please select a primary listing");
			return;
		}

		setIsMerging(true);
		try {
			const response = await fetch("/api/admin/listings/merge", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					primaryListingId,
					listingsToMerge: selectedListings
						.filter((listing) => listing.id !== primaryListingId)
						.map((listing) => listing.id)
				})
			});

			if (!response.ok) {
				throw new Error("Failed to merge listings");
			}

			toast.success("Listings merged successfully");
			onSuccess();
			onClose();
		} catch (error) {
			console.error("Error merging listings:", error);
			toast.error("Failed to merge listings");
		} finally {
			setIsMerging(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl">
				<DialogHeader>
					<DialogTitle>Merge Listings</DialogTitle>
					<DialogDescription>
						Select the primary listing that will remain after merging. All
						reviews and data from other listings will be transferred to the
						primary listing.
						<p className="mt-2 text-sm">
							<span className="inline-block w-3 h-3 bg-yellow-100 rounded-sm mr-2" />{" "}
							Matching fields
						</p>
					</DialogDescription>
				</DialogHeader>

				<div className="mt-4 grid grid-cols-2 gap-4">
					{selectedListings.map((listing) => {
						const otherListing = selectedListings.find(
							(l) => l.id !== listing.id
						);
						const matches = {
							business_name:
								otherListing?.business_name?.toLowerCase() ===
								listing.business_name?.toLowerCase(),
							email:
								otherListing?.email?.toLowerCase() ===
								listing.email?.toLowerCase(),
							phone: otherListing?.phone === listing.phone,
							rvtaa_member_id:
								otherListing?.rvtaa_member_id === listing.rvtaa_member_id
						};

						return (
							<div key={listing.id} className="border rounded-lg p-4">
								<div className="flex justify-between items-start mb-4">
									<div>
										<h3
											className={cn(
												"font-semibold",
												matches.business_name && "bg-yellow-100 px-1 rounded"
											)}
										>
											{listing.business_name}
										</h3>
										<p className="text-sm text-muted-foreground">
											{listing.location?.city}, {listing.location?.state}
										</p>
									</div>
									<input
										type="radio"
										name="primaryListing"
										value={listing.id}
										checked={primaryListingId === listing.id}
										onChange={(e) => setPrimaryListingId(e.target.value)}
									/>
								</div>

								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Reviews</span>
										<span className="font-medium">
											{listing.num_reviews || 0}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Profile Complete</span>
										<span>{listing.profile_completed ? "Yes" : "No"}</span>
									</div>
									<div className="flex justify-between">
										<span>Verification Level</span>
										<span>{listing.rv_help_verification_level || "NONE"}</span>
									</div>

									<div className="flex justify-between">
										<span>Status</span>
										<span>{listing.status || "ACTIVE"}</span>
									</div>

									{listing.owner && (
										<div className="mt-4 p-3 bg-muted rounded-md">
											<h4 className="font-medium mb-2">Owner Info</h4>
											<p>
												{listing.owner.first_name} {listing.owner.last_name}
											</p>
											<p
												className={cn(
													"text-muted-foreground",
													matches.email && "bg-yellow-100 px-1 rounded"
												)}
											>
												{listing.owner.email}
											</p>
											{listing.phone && (
												<p
													className={cn(
														"text-muted-foreground",
														matches.phone && "bg-yellow-100 px-1 rounded"
													)}
												>
													{listing.phone}
												</p>
											)}
										</div>
									)}
								</div>
							</div>
						);
					})}
				</div>

				<DialogFooter className="mt-6">
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleMerge}
						disabled={!primaryListingId || isMerging}
					>
						{isMerging ? "Merging..." : "Merge Listings"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
