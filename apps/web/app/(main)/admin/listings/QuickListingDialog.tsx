"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON>Title
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { url } from "@/lib/url";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const quickListingSchema = z.object({
	first_name: z.string().min(1, "First name is required"),
	last_name: z.string().min(1, "Last name is required"),
	business_name: z.string().min(1, "Business name is required"),
	email: z.string().email("Invalid email address"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(
			/^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)]{10,17}$/,
			"Please enter a valid phone number"
		)
		.refine(
			(val) => {
				// Remove all non-digit characters to check length
				const digitsOnly = val.replace(/\D/g, "");
				return digitsOnly.length >= 10 && digitsOnly.length <= 15;
			},
			{
				message: "Phone number must contain 10-15 digits"
			}
		),
	city: z.string().min(1, "City is required"),
	state: z.string().min(1, "State is required")
});

type QuickListingFormData = z.infer<typeof quickListingSchema>;

interface QuickListingDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSuccess: () => void;
}

export default function QuickListingDialog({
	isOpen,
	onClose,
	onSuccess
}: QuickListingDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isPending, startTransition] = useTransition();
	const router = useRouter();
	const {
		register,
		handleSubmit,
		reset,
		formState: { errors }
	} = useForm<QuickListingFormData>({
		resolver: zodResolver(quickListingSchema)
	});

	const isLoading = isSubmitting || isPending;

	const onSubmit = async (data: QuickListingFormData) => {
		setIsSubmitting(true);
		try {
			const response = await fetch("/api/admin/listings/quick-create", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				throw new Error("Failed to create listing");
			}

			const responseData = await response.json();

			toast.success("Listing created successfully");
			reset();
			startTransition(() => {
				router.push(url("edit-listing", { id: responseData.id }));
			});
		} catch (error) {
			console.error("Error creating listing:", error);
			toast.error("Failed to create listing");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Quick Create Listing</DialogTitle>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<div className="grid grid-cols-2 gap-4">
						<div>
							<Label htmlFor="first_name">First Name</Label>
							<Input {...register("first_name")} />
							{errors.first_name && (
								<p className="text-sm text-red-500">
									{errors.first_name.message}
								</p>
							)}
						</div>
						<div>
							<Label htmlFor="last_name">Last Name</Label>
							<Input {...register("last_name")} />
							{errors.last_name && (
								<p className="text-sm text-red-500">
									{errors.last_name.message}
								</p>
							)}
						</div>
					</div>

					<div>
						<Label htmlFor="business_name">Business Name</Label>
						<Input {...register("business_name")} />
						{errors.business_name && (
							<p className="text-sm text-red-500">
								{errors.business_name.message}
							</p>
						)}
					</div>

					<div>
						<Label htmlFor="email">Email</Label>
						<Input type="email" {...register("email")} />
						{errors.email && (
							<p className="text-sm text-red-500">{errors.email.message}</p>
						)}
					</div>

					<div>
						<Label htmlFor="phone">Phone</Label>
						<Input type="tel" {...register("phone")} />
						{errors.phone && (
							<p className="text-sm text-red-500">{errors.phone.message}</p>
						)}
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div>
							<Label htmlFor="city">City</Label>
							<Input {...register("city")} />
							{errors.city && (
								<p className="text-sm text-red-500">{errors.city.message}</p>
							)}
						</div>
						<div>
							<Label htmlFor="state">State</Label>
							<Input {...register("state")} />
							{errors.state && (
								<p className="text-sm text-red-500">{errors.state.message}</p>
							)}
						</div>
					</div>

					<DialogFooter>
						<Button type="button" variant="outline" onClick={onClose}>
							Cancel
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading ? (
								<div className="flex items-center gap-2">
									<Loader2 className="h-4 w-4 animate-spin" />
									{isPending ? "Redirecting..." : "Creating..."}
								</div>
							) : (
								"Create Listing"
							)}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
