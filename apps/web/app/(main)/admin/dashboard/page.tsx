"use client";

import { CenteredPageLoader } from "@/components/Loader";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import {
	Activity,
	BadgeCheck,
	BarChart2,
	Clock,
	Heart,
	LocateFixed,
	MessageSquare,
	Search,
	Settings,
	ShieldCheck,
	Star,
	Truck,
	Users,
	Users2,
	Video
} from "lucide-react";
import Link from "next/link";
import { Suspense, useEffect, useState } from "react";
import QuickListingDialog from "../listings/QuickListingDialog";
import AddUserDialog from "../users/add-user-dialog";

const DashboardHeader = () => {
	return (
		<div className="flex items-center justify-between mb-8">
			<h1 className="text-2xl font-bold">Admin Dashboard</h1>
		</div>
	);
};

const QuickActions = () => {
	const [isAddUserOpen, setIsAddUserOpen] = useState(false);
	const [showQuickCreateDialog, setShowQuickCreateDialog] = useState(false);

	return (
		<div className="grid grid-cols-2 md:grid-cols-5 gap-2 md:gap-4">
			<Link href="/admin/listings" className="block">
				<Button
					variant="outline"
					className="h-20 md:h-24 w-full bg-white hover:bg-[#447D6A]/5 border-[#447D6A]/20 hover:border-[#447D6A]/30"
				>
					<div className="flex flex-col items-center">
						<Settings className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-[#447D6A]" />
						<span className="text-sm md:text-base text-center">
							Manage Listings
						</span>
					</div>
				</Button>
			</Link>

			<Link href="/admin/users" className="block">
				<Button
					variant="outline"
					className="h-20 md:h-24 w-full bg-white hover:bg-[#447D6A]/5 border-[#447D6A]/20 hover:border-[#447D6A]/30"
				>
					<div className="flex flex-col items-center">
						<ShieldCheck className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-[#447D6A]" />
						<span className="text-sm md:text-base text-center">
							Manage Users
						</span>
					</div>
				</Button>
			</Link>

			<Link href="/admin/analytics/providers" className="block">
				<Button
					variant="outline"
					className="h-20 md:h-24 w-full bg-white hover:bg-[#447D6A]/5 border-[#447D6A]/20 hover:border-[#447D6A]/30"
				>
					<div className="flex flex-col items-center">
						<Activity className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-[#447D6A]" />
						<span className="text-sm md:text-base text-center">
							Site Activity
						</span>
					</div>
				</Button>
			</Link>

			<Button
				variant="outline"
				className="h-20 md:h-24 w-full bg-white hover:bg-[#447D6A]/5 border-[#447D6A]/20 hover:border-[#447D6A]/30"
				onClick={() => setIsAddUserOpen(true)}
			>
				<div className="flex flex-col items-center">
					<Users2 className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-[#447D6A]" />
					<span className="text-sm md:text-base text-center">Create User</span>
				</div>
			</Button>

			<Button
				variant="outline"
				className="h-20 md:h-24 w-full bg-white hover:bg-[#447D6A]/5 border-[#447D6A]/20 hover:border-[#447D6A]/30"
				onClick={() => setShowQuickCreateDialog(true)}
			>
				<div className="flex flex-col items-center">
					<LocateFixed className="h-5 w-5 md:h-6 md:w-6 mb-1 md:mb-2 text-[#447D6A]" />
					<span className="text-sm md:text-base text-center">
						Create Listing
					</span>
				</div>
			</Button>
			<AddUserDialog
				isAddUserOpen={isAddUserOpen}
				setIsAddUserOpen={setIsAddUserOpen}
				fetchUsers={() => { }}
			/>

			{showQuickCreateDialog && (
				<QuickListingDialog
					isOpen={showQuickCreateDialog}
					onClose={() => setShowQuickCreateDialog(false)}
					onSuccess={() => setShowQuickCreateDialog(false)}
				/>
			)}
		</div>
	);
};

const MetricsSection = ({ title, description, metrics }) => (
	<div className="grid grid-cols-1 gap-4">
		<div className="flex items-center justify-between mb-4">
			<div>
				<h2 className="text-xl font-semibold">{title}</h2>
				<p className="text-muted-foreground">{description}</p>
			</div>
			{title === "RV Owner Metrics" && (
				<Button variant="default" asChild>
					<Link
						href="/admin/analytics/users"
						className="flex items-center gap-2"
					>
						<BarChart2 className="w-4 h-4" />
						View Detailed Stats
					</Link>
				</Button>
			)}
		</div>
		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			{metrics.map((metric, index) => (
				<div
					key={index}
					className="bg-white p-4 md:p-6 rounded-lg border hover:border-gray-300 transition-all duration-200 shadow-sm h-full"
				>
					<div className="flex justify-between items-start mb-2">
						<div className="space-y-1">
							<div>
								<p className="text-sm font-medium text-gray-500">
									{metric.label}
								</p>
								<p className="text-xs text-gray-400 mt-1">
									{metric.description}
								</p>
							</div>
							{!metric.customContent && (
								<div className="flex items-baseline gap-2 mt-3">
									<p className="text-xl md:text-2xl font-semibold text-gray-900">
										{metric.value}
									</p>
									{metric.trend && metric.trend >= 0 && (
										<span className="text-sm text-emerald-600 font-medium whitespace-nowrap">
											↑ {metric.trend}%
										</span>
									)}
									{metric.trend && metric.trend < 0 && (
										<span className="text-sm text-red-600 font-medium whitespace-nowrap">
											↓ {metric.trend}%
										</span>
									)}
								</div>
							)}
						</div>
						<div
							className={`p-2 rounded-lg flex-shrink-0 ${getColorClasses(
								metric.color
							)}`}
						>
							{metric.icon}
						</div>
					</div>
					{metric.customContent
						? metric.customContent
						: metric.breakdown && (
							<p className="text-sm text-gray-500 mt-2">{metric.breakdown}</p>
						)}
				</div>
			))}
		</div>
	</div>
);

const getColorClasses = (color) => {
	const colorMap = {
		emerald: "text-emerald-600 bg-emerald-50",
		blue: "text-blue-600 bg-blue-50",
		indigo: "text-indigo-600 bg-indigo-50",
		violet: "text-violet-600 bg-violet-50",
		purple: "text-purple-600 bg-purple-50",
		gold: "text-yellow-600 bg-yellow-50"
	};
	return colorMap[color] || colorMap.emerald;
};

const VerificationMetrics = ({ metrics }) => {
	if (!metrics) return null;

	const percentage = metrics?.providers?.listings?.percentage || 0;

	return (
		<div className="bg-white p-6 rounded-lg border">
			<div className="flex items-start justify-between mb-8">
				<div>
					<h3 className="text-xl font-semibold mb-1">Verification Status</h3>
					<p className="text-sm text-muted-foreground">
						Progress toward 100% verified service providers
					</p>
				</div>
				<div className="flex items-center gap-2 bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
					<span className="w-2 h-2 bg-blue-500 rounded-full" />
					{metrics?.providers.listings.total.toLocaleString()} Total Listings
				</div>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
				<div>
					<div className="flex flex-col">
						<span className="text-4xl font-bold text-primary mb-2">
							{metrics?.providers.listings.verified.toLocaleString()}
						</span>
						<span className="text-sm font-medium text-gray-600">
							Verified Providers
						</span>
					</div>
				</div>

				<div>
					<div className="flex flex-col">
						<div className="flex items-baseline gap-2">
							<span className="text-4xl font-bold text-emerald-600">
								{percentage}%
							</span>
							<span className="text-sm font-medium text-emerald-600">
								Complete
							</span>
						</div>
						<span className="text-sm font-medium text-gray-600">
							Of total listings
						</span>
					</div>
				</div>
			</div>

			<div className="space-y-4">
				<div className="relative h-4 bg-gray-100 rounded-full overflow-hidden">
					<div
						className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-emerald-500 transition-all duration-500"
						style={{ width: `${percentage}%` }}
					/>
					<div className="absolute top-0 left-1/4 w-px h-full bg-gray-300" />
					<div className="absolute top-0 left-1/2 w-px h-full bg-gray-300" />
					<div className="absolute top-0 left-3/4 w-px h-full bg-gray-300" />
				</div>

				<div className="flex justify-between text-sm">
					<div className="space-y-1">
						<div className="text-gray-600 font-medium">Current Progress</div>
						<div className="text-2xl font-bold text-primary">{percentage}%</div>
					</div>
					<div className="space-y-1 text-right">
						<div className="text-gray-600 font-medium">Target</div>
						<div className="text-2xl font-bold text-gray-900">100%</div>
					</div>
				</div>
			</div>
		</div>
	);
};

const Dashboard = () => {
	const { user, loading } = useAuth();
	const [metrics, setMetrics] = useState(null) as any;
	const [membershipStats, setMembershipStats] = useState(null) as any;
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		async function fetchMetrics() {
			try {
				const response = await fetch("/api/admin/dashboard/metrics");
				const data = await response.json();
				setMetrics(data);
			} catch (error) {
				console.error("Error fetching metrics:", error);
			} finally {
				setIsLoading(false);
			}
		}

		async function fetchMembershipStats() {
			try {
				// Get stats for the last 30 days
				const endDate = new Date();
				const startDate = new Date();
				startDate.setDate(startDate.getDate() - 30);

				const response = await fetch(`/api/admin/membership-stats?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`);

				if (response.ok) {
					const data = await response.json();
					setMembershipStats(data);
				}
			} catch (error) {
				console.error("Error fetching membership stats:", error);
			}
		}

		if (user) {
			fetchMetrics();
			fetchMembershipStats();
		}
	}, [user]);

	const userMetrics = [
		{
			label: "Total RV Owners",
			description: "Total number of registered RV owners",
			value: metrics?.users.total.total.toLocaleString() || "-",
			trend: (
				(metrics?.users.newSignups.total / metrics?.users.total.total) *
				100
			).toFixed(1),
			breakdown: `${metrics?.users.newSignups.last24Hours || 0
				} new profiles created in last 24h`,
			icon: <Users className="w-5 h-5" />,
			color: "purple"
		},
		{
			label: "Active RV Owners",
			description: "RV owners who have logged in within 30 days",
			value: metrics?.users.activeUsers.total.toLocaleString() || "-",
			trend: metrics?.users.activeUsers.trend,
			breakdown: `${metrics?.users.activeUsers.percentage}% of total user base`,
			icon: <Users className="w-5 h-5" />,
			color: "emerald"
		},
		{
			label: "Total Leads",
			description: "Total number of service requests submitted",
			value: metrics?.users.leads.total.toLocaleString() || "-",
			breakdown: `${metrics?.users.leads.last24Hours || 0} new leads in last 24h`,
			icon: <MessageSquare className="w-5 h-5" />,
			color: "blue"
		},
		{
			label: "Unique Lead Submitters",
			description: "Number of unique users who submitted leads",
			value: metrics?.users.leads.uniqueUsers.toLocaleString() || "-",
			breakdown: `${((metrics?.users.leads.uniqueUsers / metrics?.users.leads.total) * 100).toFixed(1)}% of total leads`,
			icon: <Users className="w-5 h-5" />,
			color: "indigo"
		},
		{
			label: "Total Favorites",
			description: "Total number of listings favorited by users",
			value: metrics?.users.favorites.total.toLocaleString() || "-",
			breakdown: `${metrics?.users.favorites.last24Hours || 0} favorites in last 24h`,
			icon: <Heart className="w-5 h-5" />,
			color: "pink"
		}
	];

	const membershipMetrics = [
		{
			label: "Total Active Members",
			description: "Total number of active paid members",
			value: membershipStats?.totalActiveMembers?.toLocaleString() || "-",
			breakdown: "Standard + Premium memberships",
			icon: <BadgeCheck className="w-5 h-5" />,
			color: "emerald"
		},
		{
			label: "New Members (30d)",
			description: "New paid memberships in the last 30 days",
			value: membershipStats?.newMemberships?.toLocaleString() || "-",
			breakdown: "First-time paid subscriptions",
			icon: <Users className="w-5 h-5" />,
			color: "blue"
		},
		{
			label: "Cancellations (30d)",
			description: "Membership cancellations in the last 30 days",
			value: membershipStats?.totalCancellations?.toLocaleString() || "-",
			breakdown: "Cancelled memberships",
			icon: <Activity className="w-5 h-5" />,
			color: "purple"
		}
	];

	const reviewMetrics = [
		{
			label: "Total Provider Reviews",
			description:
				"User reviews of service providers created on Google or RVHelp in the last 30 days",
			value: metrics?.providers.reviews.thisMonth.toLocaleString() || "-",
			trend: metrics?.providers.reviews.trend,
			breakdown: `${metrics?.providers.reviews.total} Total Reviews`,
			icon: <Star className="w-5 h-5" />,
			color: "gold"
		},
		{
			label: "RVHelp Provider Reviews",
			description:
				"User reviews of service providers created on RVHelp only in the last 30 days",
			value: metrics?.providers.rvhelpReviews.thisMonth.toLocaleString() || "-",
			trend: metrics?.providers.rvhelpReviews.trend,
			breakdown: `${metrics?.providers.rvhelpReviews.total} Total Reviews`,
			icon: <Star className="w-5 h-5" />,
			color: "gold"
		}
	];

	const providerMetrics = [
		{
			label: "Verification Progress",
			description: `${metrics?.providers.listings.total.toLocaleString() || "0"} Total Listings`,
			customContent: <VerificationMetrics metrics={metrics} />,
			icon: <BadgeCheck className="w-5 h-5" />,
			color: "blue"
		}
	];

	const featureMetrics = [
		{
			label: "Hourly Rate Discount",
			description: "Listings offering hourly rate discounts",
			value: metrics?.providers.features.hourlyRateDiscount.total.toLocaleString() || "-",
			trend: metrics?.providers.features.hourlyRateDiscount.trend,
			icon: <Clock className="w-5 h-5" />,
			color: "emerald"
		},
		{
			label: "Dispatch Fee Discount",
			description: "Listings offering dispatch fee discounts",
			value: metrics?.providers.features.dispatchFeeDiscount.total.toLocaleString() || "-",
			trend: metrics?.providers.features.dispatchFeeDiscount.trend,
			icon: <Truck className="w-5 h-5" />,
			color: "blue"
		},
		{
			label: "Virtual Diagnosis",
			description: "Listings offering virtual diagnosis",
			value: metrics?.providers.features.virtualDiagnosis.total.toLocaleString() || "-",
			trend: metrics?.providers.features.virtualDiagnosis.trend,
			icon: <Video className="w-5 h-5" />,
			color: "violet"
		}
	];

	const searchMetrics = [
		{
			label: "Total Monthly Searches",
			description: "Total number of provider searches performed by RV owners",
			value: "Coming Soon",
			comingSoon: true,
			icon: <Search className="w-5 h-5" />,
			color: "violet"
		},
		{
			label: "User Analytics",
			description: "Detailed user engagement and conversion metrics",
			value: "View Analytics",
			customContent: (
				<Link href="/admin/analytics/users">
					<Button variant="outline" className="mt-2">
						View Detailed Stats
					</Button>
				</Link>
			),
			icon: <Users className="w-5 h-5" />,
			color: "blue"
		}
	];

	if (loading || !user || isLoading) {
		return <CenteredPageLoader />;
	}

	return (
		<Suspense fallback={<CenteredPageLoader />}>
			<div className="min-h-screen">
				<div className="space-y-6">
					<DashboardHeader />
					<QuickActions />

					<MetricsSection
						title="RV Owner Metrics"
						description="Track RV owner activity and engagement"
						metrics={userMetrics}
					/>

					<MetricsSection
						title="Membership Metrics"
						description="Track paid membership growth and retention"
						metrics={membershipMetrics}
					/>

					<MetricsSection
						title="Review Metrics"
						description="Monitor the number of user reviews and ratings"
						metrics={reviewMetrics}
					/>

					<MetricsSection
						title="Provider Metrics"
						description="Monitor the growth and engagement of RV service providers"
						metrics={providerMetrics}
					/>

					<MetricsSection
						title="Listing Features"
						description="Track adoption of listing features and discounts"
						metrics={featureMetrics}
					/>
				</div>
			</div>
		</Suspense>
	);
};

export default withAuthorization(Dashboard, "ADMIN");
