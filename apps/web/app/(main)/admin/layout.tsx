"use client";

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import {
	ChartLine,
	ChevronRight,
	FileText,
	LayoutDashboard,
	Mail,
	Settings,
	Terminal,
	Users
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

const sections = [
	{
		label: "Overview",
		icon: LayoutDashboard,
		description: "System overview and analytics",
		subsections: [
			{
				label: "Dashboard",
				href: "/admin/dashboard",
				description: "Key metrics and stats"
			}
		]
	},
	{
		label: "Management",
		icon: Users,
		description: "User and listing management",
		subsections: [
			{
				label: "Users",
				href: "/admin/users",
				description: "Manage user accounts"
			},

			{
				label: "Listings",
				href: "/admin/listings",
				description: "Manage Listings"
			},
			// leads
			{
				label: "Leads",
				href: "/admin/leads",
				description: "Manage leads"
			},
			{
				label: "Troubleshooting Requests",
				href: "/admin/troubleshooting",
				description: "Manage troubleshooting requests"
			},
			// {
			// 	label: "Review Listings",
			// 	href: "/admin/listings/review",
			// 	icon: ClipboardCheckIcon,
			// 	badge: (
			// 		<Suspense>
			// 			<PendingListingsCount />
			// 		</Suspense>
			// 	)
			// },

			{
				label: "Marketing Campaigns",
				href: "/admin/marketing-campaigns",
				description: "Create and manage marketing campaigns with discount codes"
			},
			{
				label: "Reviews",
				href: "/admin/reviews",
				description: "Manage reviews"
			},
			{
				label: "Fraud Management",
				href: "/admin/fraud",
				description: "Monitor and manage fraudulent activity"
			}
		]
	},

	// content
	{
		label: "Content",
		icon: FileText,
		description: "Content management",
		subsections: [
			{
				label: "Blog Posts",
				href: "/admin/content?type=blog-post",
				description: "Manage blog posts"
			},

			{
				label: "Certifications",
				href: "/admin/certifications",
				description: "Manage provider certification programs"
			},
			{
				label: "Lead Magnets",
				href: "/admin/lead-magnets",
				description: "Manage lead magnets for blog posts"
			},
			{
				label: "Job Postings",
				href: "/admin/content?type=job-posting",
				description: "Manage job postings"
			},

			{
				label: "Press Releases",
				href: "/admin/content?type=press-release",
				description: "Manage press releases"
			},
			{
				label: "Provider Announcements",
				href: "/admin/content?type=provider-announcement",
				description: "Manage provider announcements"
			},
			{
				label: "City Content",
				href: "/admin/cities",
				description: "Manage city content"
			}
		]
	},
	{
		label: "Analytics",
		icon: ChartLine,
		description: "Analytics and insights",
		subsections: [
			{
				label: "Members",
				href: "/admin/members",
				description: "Manage and view all RV Help members"
			},
			{
				label: "Market Analytics",
				href: "/admin/market-analytics",
				description: "View market analytics"
			},
			{
				label: "User Journey",
				href: "/admin/analytics",
				description: "View User Journey"
			},
			// {
			// 	label: "Provider Analytics",
			// 	href: "/admin/analytics/providers",
			// 	description: "View Provider Analytics"
			// },
			{
				label: "User Analytics",
				href: "/admin/analytics/users",
				description: "View user analytics"
			}

			// {
			// 	label: "Audit Logs",
			// 	href: "/admin/audit-logs",
			// 	description: "View audit logs"
			// }
		]
	},

	{
		label: "Debugging",
		icon: Terminal,
		description: "Debugging tools",
		subsections: [
			{
				label: "Admin Logs",
				href: "/admin/logs",
				description: "View selected admin debug logs"
			},
			{
				label: "SMS Records",
				href: "/admin/sms-records",
				description: "View SMS records"
			}
		]
	},
	{
		label: "Settings",
		icon: Settings,
		description: "System settings and configuration",
		subsections: [
			{
				label: "Settings",
				href: "/admin/settings",
				description: "System settings and configuration"
			},
			{
				label: "Safelist",
				href: "/admin/settings/safelist",
				description:
					"Manage allowed emails, domains, and phone numbers for development environments"
			}
		]
	},
	{
		label: "Tools",
		icon: Mail,
		description: "Administrative tools",
		subsections: [
			{
				label: "Service Dispatch",
				href: "/admin/communication/dispatch",
				description: "Manage service dispatch emails"
			},

			{
				label: "Import Member",
				href: "/admin/import/rvtaa/single",
				description: "Import members from RVTA"
			}
		]
	}
];

function AdminLayout({ children }: { children: React.ReactNode }) {
	const pathname = usePathname();
	const router = useRouter();

	// Flatten sections for mobile select
	const flattenedSections = sections.flatMap((section) =>
		section.subsections.map((sub) => ({
			label: `${section.label} - ${sub.label}`,
			href: sub.href,
			icon: section.icon,
			badge: (sub as any).badge
		}))
	);

	return (
		<div className="flex flex-col md:flex-row min-h-screen bg-gray-50">
			{/* Mobile Select Navigation */}
			<div className="md:hidden w-full bg-white border-b border-gray-200 p-4 sticky top-0 z-10">
				<Select value={pathname} onValueChange={(value) => router.push(value)}>
					<SelectTrigger className="w-full">
						<SelectValue placeholder="Select section">
							{flattenedSections.find((item) => item.href === pathname)?.label}
						</SelectValue>
					</SelectTrigger>
					<SelectContent>
						{flattenedSections.map((item) => (
							<SelectItem key={item.href} value={item.href}>
								<div className="flex items-center">
									<item.icon className="w-4 h-4 mr-2" />
									{item.label}
								</div>
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Desktop Sidebar */}
			<div className="hidden md:block w-64 bg-white border-r border-gray-200 p-4">
				<div className="space-y-6">
					<div className="space-y-2">
						{sections.map((section) => (
							<details
								key={section.label}
								className="group"
								open={
									section.subsections.some((sub) => pathname === sub.href) ||
									pathname.includes(section.label.toLowerCase())
								}
							>
								<summary
									className="flex items-center justify-between p-2 rounded-lg cursor-pointer hover:bg-gray-100"
									onClick={(e) => {
										// Prevent the default toggle behavior
										e.preventDefault();

										// Toggle the details element
										const details = e.currentTarget
											.parentElement as HTMLDetailsElement;
										details.open = !details.open;
									}}
								>
									<div className="flex items-center">
										<section.icon className="w-5 h-5 mr-2 text-gray-500" />
										<span className="font-medium">{section.label}</span>
									</div>
									<ChevronRight
										className={cn(
											"w-4 h-4 transition-transform duration-200 text-gray-500",
											(section.subsections.some(
												(sub) => pathname === sub.href
											) ||
												pathname.includes(section.label.toLowerCase())) &&
												"rotate-90"
										)}
									/>
								</summary>
								<AnimatePresence>
									<motion.div
										initial={{ height: 0, opacity: 0, y: -10 }}
										animate={{ height: "auto", opacity: 1, y: 0 }}
										exit={{ height: 0, opacity: 0, y: -10 }}
										transition={{
											duration: 0.2,
											ease: "easeOut"
										}}
										className="ml-7 mt-2 space-y-1 overflow-hidden"
									>
										{section.subsections.map((sub) => (
											<motion.div
												key={sub.href}
												initial={{ opacity: 0, x: -10 }}
												animate={{ opacity: 1, x: 0 }}
												transition={{
													duration: 0.2,
													delay: 0.1
												}}
											>
												<Link
													href={sub.href}
													className={cn(
														"block relative w-full text-left p-2 text-sm rounded-md transition-colors",
														pathname === sub.href
															? "font-medium text-primary"
															: "text-muted-foreground hover:bg-gray-100 hover:text-primary"
													)}
												>
													<span className="absolute right-0">
														{(sub as any).badge}
													</span>
													{sub.label}
												</Link>
											</motion.div>
										))}
									</motion.div>
								</AnimatePresence>
							</details>
						))}
					</div>
				</div>
			</div>

			{/* Main Content Area */}
			<main className="flex-1">
				<div className="max-w-7xl mx-auto p-4 md:p-6">
					<AnimatePresence mode="wait">
						<motion.div
							key={pathname}
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
							className="h-full"
						>
							{children}
						</motion.div>
					</AnimatePresence>
				</div>
			</main>
		</div>
	);
}

export default withAuthorization(AdminLayout, "ADMIN");
