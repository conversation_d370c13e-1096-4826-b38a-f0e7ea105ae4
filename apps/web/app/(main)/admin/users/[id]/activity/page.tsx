"use client";

import { ActivityTimeline } from "../../activity/activity-timeline";

export default function UserActivityPage({
	params
}: {
	params: { id: string };
}) {
	return (
		<div className="container mx-auto px-4 py-8">
			<h1 className="text-2xl font-bold mb-4">User Activity Timeline</h1>

			<div className="bg-white rounded-lg shadow">
				<ActivityTimeline userId={params.id} />
			</div>
		</div>
	);
}
