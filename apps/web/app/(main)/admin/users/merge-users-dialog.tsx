import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	Di<PERSON>Header,
	DialogTitle
} from "@/components/ui/dialog";
import { MembershipBadge } from "@/components/ui/membership-badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { User } from "@rvhelp/database";
import { useState } from "react";
import toast from "react-hot-toast";

interface MergeUsersDialogProps {
	isOpen: boolean;
	onClose: () => void;
	users: User[];
	onMergeComplete: () => void;
}

export default function MergeUsersDialog({
	isOpen,
	onClose,
	users,
	onMergeComplete
}: MergeUsersDialogProps) {
	const [isMerging, setIsMerging] = useState(false);
	const [keepEmail, setKeepEmail] = useState<string>("");

	if (users.length !== 2) return null;

	const [primaryUser, secondaryUser] = users;
	const sameEmail =
		primaryUser.email.toLowerCase() === secondaryUser.email.toLowerCase();

	const handleMerge = async () => {
		setIsMerging(true);
		try {
			if (!sameEmail && !keepEmail) {
				toast.error("Please select which email to keep");
				return;
			}

			const response = await fetch("/api/admin/users/merge", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					primaryUserId: primaryUser.id,
					secondaryUserId: secondaryUser.id,
					keepEmail: sameEmail ? primaryUser.email : keepEmail
				})
			});

			if (!response.ok) {
				throw new Error("Failed to merge users");
			}

			toast.success("Users merged successfully");
			onMergeComplete();
			onClose();
		} catch (error) {
			console.error("Error merging users:", error);
			toast.error("Failed to merge users");
		} finally {
			setIsMerging(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Merge Users</DialogTitle>
					<DialogDescription>
						{sameEmail ? (
							<>
								Are you sure you want to merge these accounts? All listings and
								permissions will be transferred to the primary account. The
								higher membership level will be preserved.
							</>
						) : (
							<>
								Select which email address to keep for the merged account. All
								listings and permissions will be transferred to the primary
								account. The higher membership level will be preserved.
							</>
						)}
					</DialogDescription>
				</DialogHeader>

				<div className="py-4">
					{/* Show which membership level will be preserved */}
					{primaryUser.membership_level !== secondaryUser.membership_level && (
						<div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
							<p className="text-sm text-blue-800">
								<strong>Membership Note:</strong> The{" "}
								{primaryUser.membership_level === "PREMIUM" ||
								secondaryUser.membership_level === "PREMIUM"
									? "PREMIUM"
									: primaryUser.membership_level === "STANDARD" ||
										  secondaryUser.membership_level === "STANDARD"
										? "STANDARD"
										: "FREE"}{" "}
								membership level will be preserved in the merged account.
							</p>
						</div>
					)}
					<div className="space-y-4">
						<div className="border rounded-lg p-4">
							<h3 className="font-medium mb-2">Primary User</h3>
							<div className="flex items-center gap-3">
								{!sameEmail && (
									<RadioGroup value={keepEmail} onValueChange={setKeepEmail}>
										<RadioGroupItem value={primaryUser.email} />
									</RadioGroup>
								)}
								<div>
									<p>{primaryUser.email}</p>
									<p className="text-sm text-muted-foreground">
										{primaryUser.first_name} {primaryUser.last_name}
									</p>
									<div className="mt-2">
										<MembershipBadge
											level={primaryUser.membership_level || "FREE"}
										/>
									</div>
									{primaryUser.last_login && (
										<p className="text-sm text-muted-foreground mt-1">
											Last login:{" "}
											{new Date(primaryUser.last_login).toLocaleDateString()}
										</p>
									)}
								</div>
							</div>
						</div>

						<div className="border rounded-lg p-4">
							<h3 className="font-medium mb-2">Secondary User</h3>
							<div className="flex items-center gap-3">
								{!sameEmail && (
									<RadioGroup value={keepEmail} onValueChange={setKeepEmail}>
										<RadioGroupItem value={secondaryUser.email} />
									</RadioGroup>
								)}
								<div>
									<p>{secondaryUser.email}</p>
									<p className="text-sm text-muted-foreground">
										{secondaryUser.first_name} {secondaryUser.last_name}
									</p>
									<div className="mt-2">
										<MembershipBadge
											level={secondaryUser.membership_level || "FREE"}
										/>
									</div>
									{secondaryUser.last_login && (
										<p className="text-sm text-muted-foreground mt-1">
											Last login:{" "}
											{new Date(secondaryUser.last_login).toLocaleDateString()}
										</p>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						onClick={handleMerge}
						disabled={isMerging || (!sameEmail && !keepEmail)}
					>
						{isMerging ? "Merging..." : "Merge Users"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
