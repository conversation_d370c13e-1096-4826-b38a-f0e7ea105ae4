"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Activity, ExternalLink } from "lucide-react";
import { useRouter } from "next/navigation";
import { ActivityTimeline } from "./activity-timeline";

export function ActivityDialog({
	isOpen,
	onClose,
	userId
}: {
	isOpen: boolean;
	onClose: () => void;
	userId: string;
}) {
	const router = useRouter();

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
				<DialogHeader>
					<div className="flex justify-between items-center">
						<div className="flex items-center gap-2">
							<Activity className="h-5 w-5" />
							<DialogTitle>User Activity Timeline</DialogTitle>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => router.push(`/admin/users/${userId}/activity`)}
						>
							Open in New Tab
							<ExternalLink className="ml-2 h-4 w-4" />
						</Button>
					</div>
				</DialogHeader>

				<div className="mt-4 overflow-y-auto max-h-[calc(90vh-120px)]">
					<ActivityTimeline userId={userId} />
				</div>
			</DialogContent>
		</Dialog>
	);
}
