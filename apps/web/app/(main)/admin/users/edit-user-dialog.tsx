"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { User } from "@rvhelp/database";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";

const editUserSchema = z
	.object({
		email: z.string().email("Invalid email address"),
		first_name: z.string().min(1, "First name is required"),
		last_name: z.string().min(1, "Last name is required"),
		role: z.enum(["USER", "ADMIN", "PROVIDER", "OEM", "OEM_MANAGER"]),
		admin_notes: z.string().optional(),
		company_id: z.string().optional()
	})
	.refine(
		(data) => {
			if (data.role === "OEM" || data.role === "OEM_MANAGER") {
				return !!data.company_id;
			}
			return true;
		},
		{
			message: "Company is required for OEM and OEM Manager roles",
			path: ["company_id"]
		}
	);

type FormData = z.infer<typeof editUserSchema>;

interface Company {
	id: string;
	name: string;
	abbreviation?: string;
}

interface EditUserDialogProps {
	user: User;
	onClose: () => void;
	onUserUpdated: () => void;
}

export default function EditUserDialog({
	user,
	onClose,
	onUserUpdated
}: EditUserDialogProps) {
	const {
		register,
		handleSubmit,
		control,
		watch,
		formState: { errors, isSubmitting }
	} = useForm<FormData>({
		resolver: zodResolver(editUserSchema),
		defaultValues: {
			email: user.email,
			first_name: user.first_name,
			last_name: user.last_name,
			role: user.role as "USER" | "ADMIN" | "PROVIDER" | "OEM" | "OEM_MANAGER",
			admin_notes: user.admin_notes || "",
			company_id: user.company_id || ""
		}
	});

	const selectedRole = watch("role");
	const [companies, setCompanies] = useState<Company[]>([]);
	const [companiesLoading, setCompaniesLoading] = useState(false);

	// Fetch companies when component mounts
	useEffect(() => {
		const fetchCompanies = async () => {
			setCompaniesLoading(true);
			try {
				const response = await fetch("/api/admin/companies");
				if (response.ok) {
					const data = await response.json();
					setCompanies(data.companies || []);
				}
			} catch (error) {
				console.error("Error fetching companies:", error);
			} finally {
				setCompaniesLoading(false);
			}
		};

		fetchCompanies();
	}, []);

	const onSubmit = async (data: FormData) => {
		try {
			const response = await fetch(`/api/admin/users?id=${user.id}`, {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (response.ok) {
				toast.success("User updated successfully");
				onUserUpdated();
				onClose();
			} else {
				throw new Error("Failed to update user");
			}
		} catch (error) {
			console.error("Error updating user:", error);
			toast.error("Failed to update user");
		}
	};

	return (
		<Dialog open={true} onOpenChange={onClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Edit User</DialogTitle>
				</DialogHeader>
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<Input
						{...register("email")}
						label="Email"
						error={errors.email?.message}
					/>

					<Input
						{...register("first_name")}
						label="First Name"
						error={errors.first_name?.message}
					/>

					<Input
						{...register("last_name")}
						label="Last Name"
						error={errors.last_name?.message}
					/>

					<div className="space-y-2">
						<Label>Role</Label>
						<Controller
							name="role"
							control={control}
							render={({ field }) => (
								<Select onValueChange={field.onChange} value={field.value}>
									<SelectTrigger>
										<SelectValue placeholder="Select role" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="USER">User</SelectItem>
										<SelectItem value="ADMIN">Admin</SelectItem>
										<SelectItem value="PROVIDER">Provider</SelectItem>
										<SelectItem value="OEM">OEM</SelectItem>
										<SelectItem value="OEM_MANAGER">OEM Manager</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
						{errors.role && (
							<p className="text-sm text-destructive">{errors.role.message}</p>
						)}
					</div>

					{(selectedRole === "OEM" || selectedRole === "OEM_MANAGER") && (
						<div className="space-y-2">
							<Label>Company</Label>
							<Controller
								name="company_id"
								control={control}
								render={({ field }) => (
									<Select
										onValueChange={field.onChange}
										value={field.value}
										disabled={companiesLoading}
									>
										<SelectTrigger>
											<SelectValue
												placeholder={
													companiesLoading
														? "Loading companies..."
														: "Select Company"
												}
											/>
										</SelectTrigger>
										<SelectContent>
											{companies.map((company) => (
												<SelectItem key={company.id} value={company.id}>
													{company.name}{" "}
													{company.abbreviation && `(${company.abbreviation})`}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								)}
							/>
							{errors.company_id && (
								<p className="text-sm text-destructive">
									{errors.company_id.message}
								</p>
							)}
						</div>
					)}

					<div className="space-y-2">
						<Label>Admin Notes</Label>
						<Textarea
							{...register("admin_notes")}
							placeholder="Internal admin notes (not visible to users)"
							rows={3}
						/>
						{errors.admin_notes && (
							<p className="text-sm text-destructive">
								{errors.admin_notes.message}
							</p>
						)}
					</div>

					<DialogFooter>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? "Saving..." : "Save changes"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
