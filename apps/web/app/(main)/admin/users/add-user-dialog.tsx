"use client";

import { Absolute<PERSON>oader } from "@/components/Loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const userSchema = z
	.object({
		email: z.string().email("Email address is required"),
		first_name: z.string().min(2, "First name must be at least 2 characters"),
		last_name: z.string().min(2, "Last name must be at least 2 characters"),
		use_random_password: z.boolean().default(true),
		password: z.string().optional(),
		role: z.enum(["USER", "ADMIN", "PROVIDER", "OEM", "OEM_MANAGER"], {
			required_error: "Please select a role"
		}),
		company_id: z.string().optional()
	})
	.refine(
		(data) => {
			if (!data.use_random_password && !data.password) {
				return false;
			}
			if (data.password) {
				return (
					data.password.length >= 8 &&
					/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(data.password)
				);
			}
			return true;
		},
		{
			message:
				"Password must be at least 8 characters and contain uppercase, lowercase, and number",
			path: ["password"]
		}
	)
	.refine(
		(data) => {
			if (data.role === "OEM" || data.role === "OEM_MANAGER") {
				return !!data.company_id;
			}
			return true;
		},
		{
			message: "Company is required for OEM and OEM Manager roles",
			path: ["company_id"]
		}
	);

type UserFormData = z.infer<typeof userSchema>;

interface Company {
	id: string;
	name: string;
	abbreviation?: string;
}

const AddUserDialog = ({
	isAddUserOpen,
	setIsAddUserOpen,
	fetchUsers
}: {
	isAddUserOpen: boolean;
	setIsAddUserOpen: (open: boolean) => void;
	fetchUsers: () => void;
}) => {
	const form = useForm<UserFormData>({
		resolver: zodResolver(userSchema),
		defaultValues: {
			email: "",
			first_name: "",
			last_name: "",
			use_random_password: true,
			password: "",
			role: undefined,
			company_id: ""
		}
	});

	const useRandomPassword = form.watch("use_random_password");
	const selectedRole = form.watch("role");

	const [loading, setLoading] = useState(false);
	const [companies, setCompanies] = useState<Company[]>([]);
	const [companiesLoading, setCompaniesLoading] = useState(false);

	// Fetch companies when component mounts
	useEffect(() => {
		const fetchCompanies = async () => {
			setCompaniesLoading(true);
			try {
				const response = await fetch("/api/admin/companies");
				if (response.ok) {
					const data = await response.json();
					setCompanies(data.companies || []);
				}
			} catch (error) {
				console.error("Error fetching companies:", error);
			} finally {
				setCompaniesLoading(false);
			}
		};

		if (isAddUserOpen) {
			fetchCompanies();
		}
	}, [isAddUserOpen]);

	const onSubmit = async (data: UserFormData) => {
		setLoading(true);
		try {
			const response = await fetch("/api/admin/users", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			const result = await response.json();

			if (response.ok) {
				if (result.generated_password) {
					// Show the generated password in a dialog or alert
					alert(
						`User created successfully! Please have the user use the forgot password feature to set their password.`
					);
				}
				setIsAddUserOpen(false);
				fetchUsers();
				form.reset();
			} else {
				form.setError("root", {
					type: "server",
					message: result.error || "Failed to add user"
				});
			}
		} catch (error) {
			form.setError("root", {
				type: "server",
				message: "An error occurred while adding the user"
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Add New User</DialogTitle>
				</DialogHeader>
				{loading && <AbsoluteLoader />}
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className={cn("space-y-4", loading ? "opacity-50" : "")}
					>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<Label>Email</Label>
									<FormControl>
										<Input placeholder="Email" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="first_name"
							render={({ field }) => (
								<FormItem>
									<Label>First Name</Label>
									<FormControl>
										<Input placeholder="First Name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="last_name"
							render={({ field }) => (
								<FormItem>
									<Label>Last Name</Label>
									<FormControl>
										<Input placeholder="Last Name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="use_random_password"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center space-x-2 space-y-0">
									<FormControl>
										<Checkbox
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
									<Label>Use random password</Label>
								</FormItem>
							)}
						/>

						{!useRandomPassword && (
							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<Label>Password</Label>
										<FormControl>
											<Input
												type="password"
												placeholder="Password"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}

						<FormField
							control={form.control}
							name="role"
							render={({ field }) => (
								<FormItem>
									<Label>Role</Label>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select Role" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="USER">User</SelectItem>
											<SelectItem value="ADMIN">Admin</SelectItem>
											<SelectItem value="PROVIDER">Provider</SelectItem>
											<SelectItem value="OEM">OEM</SelectItem>
											<SelectItem value="OEM_MANAGER">OEM Manager</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						{(selectedRole === "OEM" || selectedRole === "OEM_MANAGER") && (
							<FormField
								control={form.control}
								name="company_id"
								render={({ field }) => (
									<FormItem>
										<Label>Company</Label>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
											disabled={companiesLoading}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue
														placeholder={
															companiesLoading
																? "Loading companies..."
																: "Select Company"
														}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{companies.map((company) => (
													<SelectItem key={company.id} value={company.id}>
														{company.name}{" "}
														{company.abbreviation &&
															`(${company.abbreviation})`}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}

						{form.formState.errors.root && (
							<p className="text-red-500 text-sm">
								{form.formState.errors.root.message}
							</p>
						)}

						<Button type="submit" disabled={loading}>
							Add User
						</Button>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
};

export default AddUserDialog;
