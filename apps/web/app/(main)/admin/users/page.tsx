"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MembershipBadge } from "@/components/ui/membership-badge";
import { SecurityIndicator } from "@/components/ui/security-indicator";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { FraudManagementDialog } from "../fraud/fraud-management-dialog";
import DeleteUserDialog from "./delete-user-dialog";
import EditUserDialog from "./edit-user-dialog";

import AdminNotesDialog from "@/components/admin/AdminNotesDialog";
import Loader from "@/components/Loader";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Pagination } from "@/components/ui/pagination";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { User } from "@rvhelp/database";
import debounce from "lodash/debounce";
import {
	Activity,
	Edit,
	HelpCircle,
	MoreVertical,
	StickyNote,
	UserCheck
} from "lucide-react";
import { signIn } from "next-auth/react";
import toast from "react-hot-toast";
import { ActivityDialog } from "./activity/activity-dialog";
import AddUserDialog from "./add-user-dialog";
import GrantMembershipDialog from "./grant-membership-dialog";
import MergeUsersDialog from "./merge-users-dialog";
import MergeServiceRequestsDialog from "./MergeServiceRequestsDialog";

// Extend User type to include blacklist information
type UserWithBlacklist = User & {
	user_blacklisted?: boolean;
	email_blacklisted?: boolean;
	blacklist_message?: string;
};

export default function AdminUserManagement() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [users, setUsers] = useState<UserWithBlacklist[]>([]);
	const [search, setSearch] = useState(searchParams.get("search") || "");
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [isAddUserOpen, setIsAddUserOpen] = useState(false);
	const [editingUser, setEditingUser] = useState<UserWithBlacklist | null>(
		null
	);
	const [deletingUser, setDeletingUser] = useState<UserWithBlacklist | null>(
		null
	);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedUsers, setSelectedUsers] = useState<UserWithBlacklist[]>([]);
	const [isMergeDialogOpen, setIsMergeDialogOpen] = useState(false);
	const [viewingActivityUser, setViewingActivityUser] =
		useState<UserWithBlacklist | null>(null);
	const [showAdminNotesDialog, setShowAdminNotesDialog] = useState(false);
	const [selectedUserForNotes, setSelectedUserForNotes] =
		useState<UserWithBlacklist | null>(null);
	const [selectedUserForFraud, setSelectedUserForFraud] =
		useState<UserWithBlacklist | null>(null);
	const [isFraudDialogOpen, setIsFraudDialogOpen] = useState(false);
	const [selectedUserForMembership, setSelectedUserForMembership] =
		useState<UserWithBlacklist | null>(null);
	const [isGrantMembershipDialogOpen, setIsGrantMembershipDialogOpen] =
		useState(false);
	const [
		selectedUserForMergeServiceRequests,
		setSelectedUserForMergeServiceRequests
	] = useState<UserWithBlacklist | null>(null);
	const [
		isMergeServiceRequestsDialogOpen,
		setIsMergeServiceRequestsDialogOpen
	] = useState(false);

	const itemsPerPage = 10;

	const fetchUsers = useCallback(
		async (searchTerm: string, page: number, userId?: string) => {
			setIsLoading(true);
			setError(null);
			try {
				const params = new URLSearchParams({
					search: searchTerm,
					page: page.toString(),
					per_page: itemsPerPage.toString(),
					...(userId ? { id: userId } : {})
				});

				const response = await fetch(`/api/admin/users?${params}`);
				if (!response.ok) {
					throw new Error("Failed to fetch users");
				}
				const data = await response.json();
				setUsers(data.users);
				setTotalPages(Math.ceil(data.total / itemsPerPage));
			} catch (error) {
				console.error("Error fetching users:", error);
				setError("Failed to load users. Please try again.");
				toast.error("An error occurred while fetching users");
			} finally {
				setIsLoading(false);
			}
		},
		[]
	);

	const debouncedFetchUsers = useMemo(
		() =>
			debounce(
				(searchTerm: string, page: number) => fetchUsers(searchTerm, page),
				300
			),
		[fetchUsers]
	);

	useEffect(() => {
		debouncedFetchUsers(search, currentPage);
	}, [search, currentPage, debouncedFetchUsers]);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value);
		setCurrentPage(1); // Reset to first page on new search

		// Update URL with search param
		const params = new URLSearchParams(searchParams);
		if (e.target.value) {
			params.set("search", e.target.value);
		} else {
			params.delete("search");
		}
		router.replace(`/admin/users?${params.toString()}`);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
		window.scrollTo({ top: 0, behavior: "smooth" });
	};

	const handleDeleteUser = async (userId: string) => {
		const response = await fetch(`/api/admin/users?id=${userId}`, {
			method: "DELETE"
		});

		if (response.ok) {
			fetchUsers(search, currentPage); // Refresh the user list
			toast.success("User deleted successfully");
		} else {
			toast.error("Failed to delete user");
		}
		setDeletingUser(null); // Close the dialog after deletion attempt
	};

	const handleEditUser = (user: UserWithBlacklist) => {
		setEditingUser(user);
	};

	const handleViewAdminNotes = (user: UserWithBlacklist) => {
		setSelectedUserForNotes(user);
		setShowAdminNotesDialog(true);
	};

	const handleImpersonate = async (userId: string) => {
		try {
			const response = await fetch("/api/admin/impersonate", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ userId })
			});

			if (!response.ok) {
				throw new Error("Failed to impersonate user");
			}

			const { token, email } = await response.json();

			const result = await signIn("credentials", {
				email,
				impersonationToken: token,
				redirectTo: "/dashboard"
			});

			if (result?.error) {
				toast.error("Failed to impersonate user");
				return;
			}

			window.location.href = "/dashboard";
		} catch (error) {
			toast.error("Failed to impersonate user");
		}
	};

	const handlePasswordReset = async (email: string) => {
		try {
			setIsLoading(true);
			const response = await fetch(`/api/auth/forgot-password`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ email })
			});

			if (!response.ok) {
				throw new Error("Failed to send password reset");
			}

			toast.success("Password reset email sent successfully");
		} catch (error) {
			console.error("Error sending password reset:", error);
			toast.error("Failed to send password reset email");
		} finally {
			setIsLoading(false);
		}
	};

	const handleCopyPasswordResetLink = async (email: string) => {
		try {
			const resetLink = `${window.location.origin}/forgot-password?email=${encodeURIComponent(email)}`;
			
			await navigator.clipboard.writeText(resetLink);
			toast.success("Password reset link copied to clipboard");
		} catch (error) {
			console.error("Error copying link:", error);
			toast.error("Failed to copy link to clipboard");
		}
	};

	const handleMergeServiceRequests = (user: UserWithBlacklist) => {
		setSelectedUserForMergeServiceRequests(user);
		setIsMergeServiceRequestsDialogOpen(true);
	};

	const handleExportUsers = async (type: "provider" | "user" | "all") => {
		setIsLoading(true);
		try {
			const response = await fetch(`/api/admin/users/export?type=${type}`);
			if (!response.ok) throw new Error("Export failed");

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `users-export-${type}-${
				new Date().toISOString().split("T")[0]
			}.csv`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);
		} catch (error) {
			console.error("Error exporting users:", error);
			toast.error("Failed to export users");
		} finally {
			setIsLoading(false);
		}
	};

	if (error) {
		return (
			<div className="text-center p-4">
				<p className="text-red-500">Error: {error}</p>
				<Button
					onClick={() => fetchUsers(search, currentPage)}
					className="mt-2"
				>
					Retry
				</Button>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<h1 className="text-2xl font-bold mb-4">User Management</h1>
			<div className="flex justify-between mb-4">
				<div className="flex gap-2 w-full max-w-sm">
					<Input
						type="text"
						value={search}
						onChange={handleSearchChange}
						placeholder="Search by name, email, or role..."
						className="w-full"
					/>
				</div>

				<div className="flex gap-2">
					{selectedUsers.length === 2 ? (
						<Button
							variant="outline"
							onClick={() => setIsMergeDialogOpen(true)}
						>
							Merge Selected (2)
						</Button>
					) : (
						<>
							<Button variant="default" onClick={() => setIsAddUserOpen(true)}>
								Add User
							</Button>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button variant="outline">
										Export
										<ChevronDownIcon className="ml-2 h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent>
									<DropdownMenuItem
										onClick={() => handleExportUsers("provider")}
									>
										Providers
									</DropdownMenuItem>
									<DropdownMenuItem onClick={() => handleExportUsers("user")}>
										Users
									</DropdownMenuItem>
									<DropdownMenuItem onClick={() => handleExportUsers("all")}>
										All Users
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					)}
				</div>
				<AddUserDialog
					isAddUserOpen={isAddUserOpen}
					setIsAddUserOpen={setIsAddUserOpen}
					fetchUsers={() => fetchUsers(search, currentPage)}
				/>
			</div>

			<div className="bg-white rounded-lg shadow">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[50px]">Select</TableHead>
							<TableHead>Email</TableHead>
							<TableHead>Name</TableHead>
							<TableHead>Role</TableHead>
							<TableHead>Membership</TableHead>
							<TableHead>Security</TableHead>
							<TableHead>Created At</TableHead>
							<TableHead>Last Login</TableHead>
							<TableHead className="w-[150px]">Quick Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{isLoading ? (
							<TableRow>
								<TableCell colSpan={9} className="h-24">
									<Loader />
								</TableCell>
							</TableRow>
						) : users.length === 0 ? (
							<TableRow>
								<TableCell colSpan={9} className="text-center py-8">
									No users found
								</TableCell>
							</TableRow>
						) : (
							users.map((user) => (
								<TableRow
									key={user.id}
									className={
										searchParams.get("id") === user.id ? "bg-blue-50" : ""
									}
								>
									<TableCell>
										<input
											type="checkbox"
											checked={selectedUsers.some((u) => u.id === user.id)}
											onChange={(e) => {
												if (e.target.checked) {
													if (selectedUsers.length < 2) {
														setSelectedUsers([...selectedUsers, user]);
													}
												} else {
													setSelectedUsers(
														selectedUsers.filter((u) => u.id !== user.id)
													);
												}
											}}
										/>
									</TableCell>
									<TableCell>{user.email}</TableCell>
									<TableCell>{`${user.first_name} ${user.last_name}`}</TableCell>
									<TableCell>{user.role}</TableCell>
									<TableCell>
										<MembershipBadge level={user.membership_level || "FREE"} />
									</TableCell>
									<TableCell>
										<div className="flex justify-center">
											<SecurityIndicator
												userBlacklisted={user.user_blacklisted}
												emailBlacklisted={user.email_blacklisted}
												blacklistMessage={user.blacklist_message}
											/>
										</div>
									</TableCell>
									<TableCell>
										{new Date(user.created_at).toLocaleDateString()}
									</TableCell>
									<TableCell>
										{user.last_login
											? new Date(user.last_login).toLocaleDateString()
											: "Never"}
									</TableCell>
									<TableCell>
										<div className="flex items-center gap-2">
											{/* Quick Actions */}
											<div className="flex items-center gap-1">
												{/* Edit User */}
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleEditUser(user)}
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title="Edit user"
												>
													<Edit className="h-4 w-4" />
												</Button>

												{/* Admin Notes Button */}
												<div className="relative">
													<Button
														variant="ghost"
														size="sm"
														onClick={() => {
															setSelectedUserForNotes(user);
															setShowAdminNotesDialog(true);
														}}
														className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
														title={
															user.admin_notes &&
															user.admin_notes.trim().length > 0
																? "View admin notes"
																: "No admin notes - click to add"
														}
													>
														<StickyNote className="h-4 w-4" />
													</Button>
													{/* Badge indicator when there are notes */}
													{user.admin_notes &&
														user.admin_notes.trim().length > 0 && (
															<div className="absolute top-0.5 right-0 h-2.5 w-2.5 bg-blue-500 rounded-full border border-white" />
														)}
												</div>

												{/* Impersonate */}
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleImpersonate(user.id)}
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title="Impersonate user"
												>
													<UserCheck className="h-4 w-4" />
												</Button>

												{/* View Activity */}
												<Button
													variant="ghost"
													size="sm"
													onClick={() => setViewingActivityUser(user)}
													className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													title="View user activity"
												>
													<Activity className="h-4 w-4" />
												</Button>
											</div>

											{/* Additional Actions Dropdown */}
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
													>
														<MoreVertical className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem
														onClick={() => setViewingActivityUser(user)}
													>
														View Activity
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handlePasswordReset(user.email)}
													>
														Send Password Reset
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handleCopyPasswordResetLink(user.email)}
													>
														Copy Password Reset Link
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handleMergeServiceRequests(user)}
														className="flex items-center justify-between"
													>
														<span>Merge Service Requests</span>
														<a
															href="/docs/merge-service-requests.md"
															target="_blank"
															rel="noopener noreferrer"
															className="text-gray-400 hover:text-gray-600 transition-colors"
															title="View documentation"
															onClick={(e) => e.stopPropagation()}
														>
															<HelpCircle className="h-3 w-3" />
														</a>
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem
														onClick={() => {
															setSelectedUserForMembership(user);
															setIsGrantMembershipDialogOpen(true);
														}}
														className="text-green-600"
													>
														Grant Membership
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem
														onClick={() => {
															setSelectedUserForFraud(user);
															setIsFraudDialogOpen(true);
														}}
														className="text-orange-600"
													>
														Fraud Management
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem
														className="text-red-600"
														onClick={() => setDeletingUser(user)}
													>
														Delete
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>
			{totalPages > 1 && (
				<div className="py-4">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						onPageChange={setCurrentPage}
					/>
				</div>
			)}

			{editingUser && (
				<EditUserDialog
					user={editingUser}
					onClose={() => setEditingUser(null)}
					onUserUpdated={() => fetchUsers(search, currentPage)}
				/>
			)}
			<DeleteUserDialog
				isOpen={!!deletingUser}
				onClose={() => setDeletingUser(null)}
				onDelete={() => deletingUser && handleDeleteUser(deletingUser.id)}
			/>
			<MergeUsersDialog
				isOpen={isMergeDialogOpen}
				onClose={() => {
					setIsMergeDialogOpen(false);
					setSelectedUsers([]);
				}}
				users={selectedUsers}
				onMergeComplete={() => fetchUsers(search, currentPage)}
			/>
			<ActivityDialog
				isOpen={!!viewingActivityUser}
				onClose={() => setViewingActivityUser(null)}
				userId={viewingActivityUser?.id || ""}
			/>

			{showAdminNotesDialog && selectedUserForNotes && (
				<AdminNotesDialog
					isOpen={showAdminNotesDialog}
					onClose={() => {
						setShowAdminNotesDialog(false);
						setSelectedUserForNotes(null);
					}}
					title="User Admin Notes"
					entityType="user"
					entityData={{
						id: selectedUserForNotes.id,
						name: `${selectedUserForNotes.first_name} ${selectedUserForNotes.last_name}`,
						email: selectedUserForNotes.email,
						admin_notes: selectedUserForNotes.admin_notes || null,
						created_at:
							typeof selectedUserForNotes.created_at === "string"
								? selectedUserForNotes.created_at
								: selectedUserForNotes.created_at.toISOString(),
						updated_at:
							typeof selectedUserForNotes.updated_at === "string"
								? selectedUserForNotes.updated_at
								: selectedUserForNotes.updated_at.toISOString()
					}}
					onNotesUpdated={() => {
						// Refresh the users data to show updated notes
						fetchUsers(search, currentPage);
					}}
				/>
			)}

			{selectedUserForFraud && (
				<FraudManagementDialog
					isOpen={isFraudDialogOpen}
					onClose={() => {
						setIsFraudDialogOpen(false);
						setSelectedUserForFraud(null);
					}}
					job={{
						id: "user-fraud",
						first_name: selectedUserForFraud.first_name,
						last_name: selectedUserForFraud.last_name,
						email: selectedUserForFraud.email,
						message: "User fraud management",
						created_at:
							typeof selectedUserForFraud.created_at === "string"
								? selectedUserForFraud.created_at
								: selectedUserForFraud.created_at.toISOString(),
						user: {
							id: selectedUserForFraud.id,
							first_name: selectedUserForFraud.first_name,
							last_name: selectedUserForFraud.last_name,
							email: selectedUserForFraud.email,
							phone: selectedUserForFraud.phone || "",
							created_at:
								typeof selectedUserForFraud.created_at === "string"
									? selectedUserForFraud.created_at
									: selectedUserForFraud.created_at.toISOString()
						},
						quotes: []
					}}
					onAction={async (action, data) => {
						if (action === "ban_user") {
							try {
								const response = await fetch("/api/admin/fraud", {
									method: "POST",
									headers: { "Content-Type": "application/json" },
									body: JSON.stringify({ action, ...data })
								});

								if (response.ok) {
									const result = await response.json();
									toast.success(result.message);
									fetchUsers(search, currentPage);
									setIsFraudDialogOpen(false);
									setSelectedUserForFraud(null);
								} else {
									const error = await response.json();
									toast.error(error.error || "Failed to ban user");
								}
							} catch (error) {
								console.error("Error banning user:", error);
								toast.error("Failed to ban user");
							}
						}
					}}
				/>
			)}

			<GrantMembershipDialog
				isOpen={isGrantMembershipDialogOpen}
				onClose={() => {
					setIsGrantMembershipDialogOpen(false);
					setSelectedUserForMembership(null);
				}}
				user={selectedUserForMembership}
				onMembershipGranted={() => {
					fetchUsers(search, currentPage);
					setIsGrantMembershipDialogOpen(false);
					setSelectedUserForMembership(null);
				}}
			/>

			{selectedUserForMergeServiceRequests && (
				<MergeServiceRequestsDialog
					isOpen={isMergeServiceRequestsDialogOpen}
					onClose={() => {
						setIsMergeServiceRequestsDialogOpen(false);
						setSelectedUserForMergeServiceRequests(null);
					}}
					user={selectedUserForMergeServiceRequests}
					onSuccess={() => {
						fetchUsers(search, currentPage);
						setIsMergeServiceRequestsDialogOpen(false);
						setSelectedUserForMergeServiceRequests(null);
					}}
				/>
			)}
		</div>
	);
}
