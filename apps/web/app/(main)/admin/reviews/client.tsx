"use client";

import Loader from "@/components/Loader";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Pagination } from "@/components/ui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { upload } from "@/lib/storage";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { zodResolver } from "@hookform/resolvers/zod";
import { Listing, Review as PrismaReview, User } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const reviewModerationSchema = z.object({
	status: z.enum(["active", "in_moderation", "rejected"]),
	moderation_notes: z.string().optional(),
	moderation_attachment: z.instanceof(File).optional()
});

const deleteReviewSchema = z.object({
	reason: z.string().min(1, "Deletion reason is required"),
	admin_notes: z.string().optional()
});

type ReviewFormData = z.infer<typeof reviewModerationSchema>;
type DeleteReviewFormData = z.infer<typeof deleteReviewSchema>;

type Review = PrismaReview & {
	user: User;
	listing: Listing;
	moderated_by?: User;
	moderation_attachment?: string;
};

export default function ReviewManagementClient() {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [selectedReview, setSelectedReview] = useState<Review | null>(null);
	const [selectedDaysToReview, setSelectedDaysToReview] = useState(0);
	const [selectedStatus, setSelectedStatus] = useState<string>("all");
	const [search, setSearch] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [totalReviews, setTotalReviews] = useState(0);
	const [isModerating, setIsModerating] = useState(false);
	const [showModerationConfirm, setShowModerationConfirm] = useState(false);
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [isResendingVerification, setIsResendingVerification] = useState(false);

	const {
		register,
		handleSubmit,
		reset,
		watch,
		formState: { errors },
		setValue
	} = useForm<ReviewFormData>({
		resolver: zodResolver(reviewModerationSchema)
	});

	const {
		register: registerDelete,
		handleSubmit: handleSubmitDelete,
		reset: resetDelete,
		formState: { errors: deleteErrors }
	} = useForm<DeleteReviewFormData>({
		resolver: zodResolver(deleteReviewSchema)
	});

	const moderationStatus = watch("status");

	const fetchReviews = async () => {
		try {
			const params = new URLSearchParams({
				...(selectedDaysToReview > 0 && {
					daysToReview: selectedDaysToReview.toString()
				}),
				...(selectedStatus !== "all" && {
					status: selectedStatus
				}),
				search: search,
				page: currentPage.toString(),
				limit: "10"
			});
			const response = await fetch(`/api/admin/reviews?${params}`);
			const data = await response.json();
			setReviews(data.reviews || []);
			setTotalPages(data.totalPages || 1);
			setTotalReviews(data.total || 0);
		} catch (error) {
			toast.error("Failed to fetch reviews");
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchReviews();
	}, [selectedDaysToReview, selectedStatus, search, currentPage]);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value);
		setCurrentPage(1);
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleModerationConfirm = async () => {
		if (!selectedReview) return;

		setIsModerating(true);
		try {
			const response = await fetch(
				`/api/reviews/${selectedReview.id}/moderate`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						status: "in_moderation",
						moderation_notes: "Review placed under moderation for investigation"
					})
				}
			);

			if (!response.ok) {
				throw new Error("Failed to moderate review");
			}

			toast.success("Review placed under moderation");
			setShowModerationConfirm(false);
			setSelectedReview((prev) =>
				prev
					? {
							...prev,
							status: "in_moderation"
						}
					: null
			);
			fetchReviews();
		} catch (error) {
			toast.error("Failed to moderate review");
			console.error("Moderation error:", error);
		} finally {
			setIsModerating(false);
		}
	};

	const handleModerationDecision = async (data: ReviewFormData) => {
		if (!selectedReview) return;

		setIsModerating(true);
		try {
			// If there's an attachment, upload it first
			let attachmentUrl;
			if (data.moderation_attachment) {
				const uploadResult = await upload(
					data.moderation_attachment,
					`moderation-${selectedReview.id}-${Date.now()}`,
					"moderation-attachments"
				);

				if (uploadResult.error) {
					throw new Error("Failed to upload attachment");
				}

				attachmentUrl = uploadResult.url;
			}

			const response = await fetch(
				`/api/reviews/${selectedReview.id}/moderate`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						status: data.status,
						moderation_notes: data.moderation_notes,
						moderation_attachment: attachmentUrl
					})
				}
			);

			if (!response.ok) {
				throw new Error("Failed to update review status");
			}

			toast.success(
				`Review ${data.status === "active" ? "approved" : "rejected"}`
			);
			setSelectedReview(null);
			reset();
			fetchReviews();
		} catch (error) {
			toast.error("Failed to update review status");
			console.error("Moderation error:", error);
		} finally {
			setIsModerating(false);
		}
	};

	const handleDeleteReview = async (data: DeleteReviewFormData) => {
		if (!selectedReview) return;

		setIsDeleting(true);
		try {
			const response = await fetch(`/api/reviews/${selectedReview.id}`, {
				method: "DELETE",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					reason: data.reason,
					admin_notes: data.admin_notes
				})
			});

			if (!response.ok) {
				throw new Error("Failed to delete review");
			}

			toast.success("Review deleted successfully");
			setShowDeleteConfirm(false);
			setSelectedReview(null);
			resetDelete();
			fetchReviews();
		} catch (error) {
			toast.error("Failed to delete review");
			console.error("Delete error:", error);
		} finally {
			setIsDeleting(false);
		}
	};

	const handleResendVerification = async (reviewId: string) => {
		setIsResendingVerification(true);
		try {
			const response = await fetch(
				`/api/admin/reviews/${reviewId}/resend-verification`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					}
				}
			);

			if (response.ok) {
				toast.success("Verification email sent successfully");
				fetchReviews(); // Refresh the list to update status
			} else {
				const errorData = await response.json();
				toast.error(errorData.error || "Failed to send verification email");
			}
		} catch (error) {
			toast.error("An error occurred while sending verification email");
		} finally {
			setIsResendingVerification(false);
		}
	};

	const renderRatingSection = (review: Review) => {
		const ratings = [
			{ label: "Overall", value: review.overall },
			{ label: "Service", value: review.service },
			{ label: "Responsiveness", value: review.responsiveness },
			{ label: "Expertise", value: review.expertise },
			{ label: "Results", value: review.results },
			{ label: "Communication", value: review.communication }
		];

		return (
			<div className="grid grid-cols-2 gap-4 mt-4">
				{ratings.map(
					({ label, value }) =>
						value && (
							<div key={label}>
								<span className="text-sm text-gray-500">{label}:</span>
								<span className="ml-2 font-medium">{value}</span>
							</div>
						)
				)}
			</div>
		);
	};

	return (
		<div>
			<div className="flex justify-between items-center mb-4">
				<div>
					<h1 className="text-2xl font-bold">Review Management</h1>
					<p className="text-muted-foreground">
						Manage and monitor {totalReviews}{" "}
						{totalReviews === 1 ? "review" : "reviews"} across all listings
					</p>
				</div>
				<div className="flex gap-2">
					<Input
						type="search"
						placeholder="Search reviews..."
						value={search}
						onChange={handleSearchChange}
						className="max-w-[300px]"
					/>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="outline" size="sm">
								{selectedStatus === "all"
									? "All Statuses"
									: selectedStatus === "in_moderation"
										? "In Moderation"
										: selectedStatus === "active"
											? "Active"
											: selectedStatus === "draft"
												? "Draft"
												: selectedStatus === "rejected"
													? "Rejected"
													: selectedStatus === "pending_verification"
														? "Pending Verification"
														: "Unknown Status"}
								<ChevronDownIcon className="ml-2 h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuItem onClick={() => setSelectedStatus("all")}>
								All Statuses
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedStatus("active")}>
								Active
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() => setSelectedStatus("in_moderation")}
							>
								In Moderation
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedStatus("draft")}>
								Draft
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedStatus("rejected")}>
								Rejected
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() => setSelectedStatus("pending_verification")}
							>
								Pending Verification
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="outline" size="sm">
								{selectedDaysToReview === 0
									? "All time"
									: `Last ${selectedDaysToReview} days`}
								<ChevronDownIcon className="ml-2 h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuItem onClick={() => setSelectedDaysToReview(0)}>
								All time
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedDaysToReview(3)}>
								Last 3 days
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedDaysToReview(7)}>
								Last 7 days
							</DropdownMenuItem>
							<DropdownMenuItem onClick={() => setSelectedDaysToReview(30)}>
								Last 30 days
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</div>

			<div className="bg-white rounded-lg shadow">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Review</TableHead>
							<TableHead>Customer</TableHead>
							<TableHead>Business</TableHead>
							<TableHead>Rating</TableHead>
							<TableHead>Date</TableHead>
							<TableHead className="text-right">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{isLoading ? (
							<TableRow>
								<TableCell colSpan={6} className="h-24">
									<Loader />
								</TableCell>
							</TableRow>
						) : reviews.length === 0 ? (
							<TableRow>
								<TableCell colSpan={6} className="h-24 text-center">
									<div className="flex flex-col items-center justify-center space-y-2">
										<p className="text-gray-500">No reviews found</p>
										<p className="text-sm text-gray-400">
											Reviews will appear here once customers submit them
										</p>
									</div>
								</TableCell>
							</TableRow>
						) : (
							reviews.map((review: Review) => (
								<TableRow
									key={review.id}
									className={`cursor-pointer hover:bg-gray-50 ${
										review.status === "rejected"
											? "bg-red-50/50 hover:bg-red-50"
											: review.status === "in_moderation"
												? "bg-amber-50/50 hover:bg-amber-50"
												: ""
									}`}
								>
									<TableCell>
										<div>
											<div className="font-medium">{review.title}</div>
											<div className="text-sm text-muted-foreground">
												<Badge variant="outline" className="text-xs">
													{review.source === "rvhelp"
														? "RV Help"
														: review.source}
												</Badge>
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div className="text-sm">
											<div className="font-medium">
												{review.first_name} {review.last_name}
											</div>
											<div className="text-muted-foreground">
												{review.email}
											</div>
										</div>
									</TableCell>
									<TableCell>{review.listing.business_name}</TableCell>
									<TableCell>
										<div className="flex items-center">
											<span className="font-medium">{review.overall}</span>
											<span className="text-amber-400 ml-1">★</span>
										</div>
									</TableCell>
									<TableCell className="text-muted-foreground">
										{formatDistanceToNow(new Date(review.created_at))} ago
									</TableCell>
									<TableCell className="text-right">
										<div className="flex items-center justify-end gap-2">
											{review.status === "in_moderation" && (
												<Badge
													variant="outline"
													className="border-amber-500 text-amber-700 bg-amber-50"
												>
													In Moderation
												</Badge>
											)}
											{review.status === "rejected" && (
												<Badge variant="destructive">Rejected</Badge>
											)}
											{review.status === "draft" && (
												<Badge
													variant="outline"
													className="border-gray-500 text-gray-700 bg-gray-50"
												>
													Draft - Email Verification Required
												</Badge>
											)}
											{review.status === "pending_verification" && (
												<Badge
													variant="outline"
													className="border-blue-500 text-blue-700 bg-blue-50"
												>
													Pending Email Verification
												</Badge>
											)}
											{(review.status === "draft" ||
												review.status === "pending_verification") && (
												<Button
													variant="outline"
													size="sm"
													onClick={() => handleResendVerification(review.id)}
													disabled={isResendingVerification}
													className="text-blue-600 border-blue-600 hover:bg-blue-50"
												>
													{isResendingVerification
														? "Sending..."
														: "Resend Verification"}
												</Button>
											)}
											<Button
												variant="outline"
												size="sm"
												onClick={() => setSelectedReview(review)}
											>
												View Details
											</Button>
											<Button
												variant="destructive"
												size="sm"
												onClick={() => {
													setSelectedReview(review);
													setShowDeleteConfirm(true);
												}}
											>
												Delete
											</Button>
										</div>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>

			{totalPages > 1 && (
				<div className="mt-4 flex justify-center">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						onPageChange={handlePageChange}
					/>
				</div>
			)}

			<Dialog
				open={!!selectedReview}
				onOpenChange={() => {
					setSelectedReview(null);
					reset();
					resetDelete();
					setShowModerationConfirm(false);
					setShowDeleteConfirm(false);
				}}
			>
				<DialogContent className="max-w-3xl">
					<DialogHeader>
						<DialogTitle>Review Details</DialogTitle>
						<DialogDescription>
							View and manage review details
						</DialogDescription>
					</DialogHeader>
					{selectedReview && (
						<div className="space-y-4">
							<div className="flex justify-between items-start">
								<div>
									<h3 className="font-medium mb-2">Review Information</h3>
									<div className="space-y-2">
										<p className="text-sm text-gray-600">
											<strong>Moderation Status:</strong>{" "}
											<span
												className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
													selectedReview.status === "active"
														? "bg-green-100 text-green-800"
														: selectedReview.status === "in_moderation"
															? "bg-yellow-100 text-yellow-800"
															: selectedReview.status === "draft"
																? "bg-gray-100 text-gray-800"
																: selectedReview.status ===
																	  "pending_verification"
																	? "bg-blue-100 text-blue-800"
																	: "bg-red-100 text-red-800"
												}`}
											>
												{selectedReview.status === "active"
													? "Active"
													: selectedReview.status === "in_moderation"
														? "In Moderation"
														: selectedReview.status === "draft"
															? "Draft - Email Verification Required"
															: selectedReview.status === "pending_verification"
																? "Pending Email Verification"
																: "Rejected"}
											</span>
										</p>
										<p className="text-sm text-gray-600">
											<strong>Created:</strong>{" "}
											{formatDistanceToNow(new Date(selectedReview.created_at))}{" "}
											ago
										</p>
										<p className="text-sm text-gray-600">
											<strong>Last Updated:</strong>{" "}
											{formatDistanceToNow(new Date(selectedReview.updated_at))}{" "}
											ago
										</p>
									</div>
								</div>
							</div>

							<div>
								<h3 className="font-medium mb-2">Business</h3>
								<p>{selectedReview.listing.business_name}</p>
							</div>

							<div className="space-y-4">
								<div>
									<h3 className="font-medium">Review Content</h3>
									<div className="bg-gray-50 p-4 rounded-md">
										<h4 className="font-medium mb-2">{selectedReview.title}</h4>
										<p className="text-sm text-gray-600 mb-4">
											{selectedReview.content}
										</p>
										{renderRatingSection(selectedReview)}
									</div>
								</div>
							</div>

							{selectedReview.status === "in_moderation" ? (
								<div className="pt-4 border-t">
									<h3 className="font-medium mb-2">Moderation Decision</h3>
									<form
										onSubmit={handleSubmit(handleModerationDecision)}
										className="space-y-4"
									>
										<div className="grid gap-2">
											<Label htmlFor="status">Decision</Label>
											<select
												id="status"
												{...register("status")}
												className={`w-full p-2 border rounded transition-colors ${
													errors.status
														? "border-red-500"
														: moderationStatus === "rejected"
															? "bg-red-50 border-red-200"
															: moderationStatus === "active"
																? "bg-green-50 border-green-200"
																: ""
												}`}
											>
												<option value="active">Approve Review</option>
												<option value="rejected">Reject Review</option>
											</select>
											{errors.status && (
												<p className="text-sm text-red-500">
													{errors.status.message}
												</p>
											)}
										</div>
										<div className="grid gap-2">
											<Label htmlFor="moderation_notes">Notes (Optional)</Label>
											<Textarea
												id="moderation_notes"
												{...register("moderation_notes")}
												placeholder="Enter moderation notes..."
												className={`${
													moderationStatus === "rejected"
														? "bg-red-50 border-red-200"
														: moderationStatus === "active"
															? "bg-green-50 border-green-200"
															: ""
												}`}
											/>
										</div>
										<div className="grid gap-2">
											<Label htmlFor="moderation_attachment">
												Supporting Evidence (Optional)
											</Label>
											<Input
												id="moderation_attachment"
												type="file"
												accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
												onChange={(e) => {
													const file = e.target.files?.[0];
													if (file) {
														setValue("moderation_attachment", file);
													}
												}}
												className={`${
													moderationStatus === "rejected"
														? "bg-red-50 border-red-200"
														: moderationStatus === "active"
															? "bg-green-50 border-green-200"
															: ""
												}`}
											/>
											<p className="text-sm text-gray-500">
												Attach any supporting documentation (PDF, DOC, DOCX,
												TXT) or images (JPG, PNG)
											</p>
										</div>
										<DialogFooter>
											<Button
												type="submit"
												disabled={isModerating}
												variant={
													moderationStatus === "rejected"
														? "destructive"
														: "default"
												}
												className={`${
													moderationStatus === "active"
														? "bg-green-600 hover:bg-green-700"
														: ""
												}`}
											>
												{isModerating ? "Processing..." : "Submit Decision"}
											</Button>
										</DialogFooter>
									</form>
								</div>
							) : selectedReview.status === "draft" ? (
								<div className="pt-4 border-t">
									<h3 className="font-medium mb-2">Review Status</h3>
									<p className="text-sm text-gray-600 mb-4">
										This review is in draft state because the user has not
										completed the email verification process. The review cannot
										be placed under moderation until the user verifies their
										email address.
									</p>
								</div>
							) : (
								<div className="pt-4 border-t">
									<h3 className="font-medium mb-2">Moderation Actions</h3>
									<div className="flex gap-2">
										<Button
											variant="outline"
											onClick={() => setShowModerationConfirm(true)}
											disabled={
												isModerating || selectedReview.status === "draft"
											}
										>
											Place Under Moderation
										</Button>
										<Button
											variant="destructive"
											onClick={() => setShowDeleteConfirm(true)}
											disabled={isDeleting}
										>
											Delete Review
										</Button>
									</div>
								</div>
							)}
						</div>
					)}
				</DialogContent>
			</Dialog>

			<Dialog
				open={showModerationConfirm}
				onOpenChange={setShowModerationConfirm}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Confirm Moderation</DialogTitle>
						<DialogDescription>
							Are you sure you want to place this review under moderation? This
							will hide it from public view while you investigate.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowModerationConfirm(false)}
						>
							Cancel
						</Button>
						<Button onClick={handleModerationConfirm} disabled={isModerating}>
							{isModerating ? "Processing..." : "Place Under Moderation"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			<Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Delete Review</DialogTitle>
						<DialogDescription>
							Are you sure you want to permanently delete this review? This
							action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<form
						onSubmit={handleSubmitDelete(handleDeleteReview)}
						className="space-y-4"
					>
						<div className="grid gap-2">
							<Label htmlFor="delete_reason">Reason for Deletion *</Label>
							<select
								id="delete_reason"
								{...registerDelete("reason")}
								className="w-full p-2 border rounded"
							>
								<option value="">Select a reason...</option>
								<option value="spam">Spam or Fake Review</option>
								<option value="inappropriate">Inappropriate Content</option>
								<option value="duplicate">Duplicate Review</option>
								<option value="wrong_business">Wrong Business</option>
								<option value="customer_request">Customer Request</option>
								<option value="other">Other</option>
							</select>
							{deleteErrors.reason && (
								<p className="text-sm text-red-500">
									{deleteErrors.reason.message}
								</p>
							)}
						</div>
						<div className="grid gap-2">
							<Label htmlFor="delete_admin_notes">
								Additional Notes (Optional)
							</Label>
							<Textarea
								id="delete_admin_notes"
								{...registerDelete("admin_notes")}
								placeholder="Provide additional context..."
								rows={3}
							/>
						</div>
						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => setShowDeleteConfirm(false)}
								disabled={isDeleting}
							>
								Cancel
							</Button>
							<Button type="submit" variant="destructive" disabled={isDeleting}>
								{isDeleting ? "Deleting..." : "Delete Review"}
							</Button>
						</DialogFooter>
					</form>
				</DialogContent>
			</Dialog>
		</div>
	);
}
