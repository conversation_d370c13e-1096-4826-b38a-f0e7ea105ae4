"use client";

import Loader from "@/components/Loader";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getCategoryName } from "@/lib/categories";
import { useDebounce } from "@/lib/hooks/useDebounce";
import { JobStatus } from "@rvhelp/database";
import { format, formatDistanceToNow } from "date-fns";
import {
	Briefcase,
	Building2,
	Download,
	ExternalLink,
	User,
	Users
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { getQuoteStatusBadge } from "../../../../lib/utils/quote";
import { columns, Lead } from "./columns";
import MergeJobsDialog from "./MergeJobsDialog";
import { providerColumns, ProviderLead } from "./provider-columns";

interface PaginationData {
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

interface AdminStats {
	totalJobs: number;
	jobsWithNoResponse: number;
	jobsPendingResponse: number;
	jobsOver3DaysOld: number;
	overallResponseRate: number;
	noResponseRate: number;
	pendingResponseRate: number;
	stagnantJobRate: number;
}

type ViewType = "jobs" | "providers";

export function LeadsTable() {
	const [leads, setLeads] = useState<Lead[]>([]);
	const [providerLeads, setProviderLeads] = useState<ProviderLead[]>([]);
	const [loading, setLoading] = useState(true);
	const [statsLoading, setStatsLoading] = useState(true);
	const [viewType, setViewType] = useState<ViewType>("providers");
	const [stats, setStats] = useState<AdminStats>({
		totalJobs: 0,
		jobsWithNoResponse: 0,
		jobsPendingResponse: 0,
		jobsOver3DaysOld: 0,
		overallResponseRate: 0,
		noResponseRate: 0,
		pendingResponseRate: 0,
		stagnantJobRate: 0
	});
	const [pagination, setPagination] = useState<PaginationData>({
		total: 0,
		page: 1,
		limit: 10,
		totalPages: 0
	});
	const [providerStatus, setProviderStatus] = useState<string>("all");
	const [resolutionStatus, setResolutionStatus] = useState<string>("all");
	const [search, setSearch] = useState("");
	const [providerSearch, setProviderSearch] = useState("");
	const debouncedSearch = useDebounce(search, 300);
	const debouncedProviderSearch = useDebounce(providerSearch, 300);
	const [selectedLead, setSelectedLead] = useState<Lead | ProviderLead | null>(
		null
	);
	const [exporting, setExporting] = useState(false);
	const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
	const [showMergeDialog, setShowMergeDialog] = useState(false);

	// Fetch stats separately
	useEffect(() => {
		const fetchStats = async () => {
			setStatsLoading(true);
			try {
				const response = await fetch(`/api/admin/leads?stats_only=true`);
				if (!response.ok) throw new Error("Failed to fetch stats");

				const data = await response.json();
				setStats(data.stats || {});
			} catch (error) {
				console.error("Error fetching stats:", error);
			} finally {
				setStatsLoading(false);
			}
		};

		fetchStats();
	}, []); // Only fetch stats once on mount

	useEffect(() => {
		const fetchLeads = async () => {
			setLoading(true);
			try {
				const queryParams = new URLSearchParams({
					page: pagination.page.toString(),
					limit: pagination.limit.toString(),
					view_type: viewType,
					...(debouncedSearch && { search: debouncedSearch }),
					...(debouncedProviderSearch && {
						provider_search: debouncedProviderSearch
					})
				});

				const response = await fetch(`/api/admin/leads?${queryParams}`);
				if (!response.ok) throw new Error("Failed to fetch leads");

				const data = await response.json();

				if (viewType === "providers") {
					setProviderLeads(data.leads || []);
				} else {
					setLeads(data.leads || []);
				}

				setPagination(
					data.pagination || {
						total: 0,
						page: 1,
						limit: 10,
						totalPages: 0
					}
				);
			} catch (error) {
				console.error("Error fetching leads:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchLeads();
	}, [
		pagination.page,
		pagination.limit,
		debouncedSearch,
		debouncedProviderSearch,
		viewType
	]);

	useEffect(() => {
		const handleOpenLeadDetails = (e: CustomEvent<Lead | ProviderLead>) => {
			setSelectedLead(e.detail);
		};

		window.addEventListener(
			"OPEN_LEAD_DETAILS",
			handleOpenLeadDetails as EventListener
		);
		return () => {
			window.removeEventListener(
				"OPEN_LEAD_DETAILS",
				handleOpenLeadDetails as EventListener
			);
		};
	}, []);

	const handleExportCSV = async () => {
		try {
			setExporting(true);
			const response = await fetch("/api/admin/leads/export");

			if (!response.ok) {
				throw new Error("Failed to export leads");
			}

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.style.display = "none";
			a.href = url;
			a.download = `leads-export-${format(new Date(), "yyyy-MM-dd")}.csv`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			toast.success("Leads exported successfully");
		} catch (error) {
			console.error("Export error:", error);
			toast.error("Failed to export leads");
		} finally {
			setExporting(false);
		}
	};

	const handleViewTypeChange = (newViewType: ViewType) => {
		setViewType(newViewType);
		setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page when changing view
		setSearch(""); // Clear search when changing view
		setProviderSearch(""); // Clear provider search when changing view
		setSelectedJobs([]); // Clear selected jobs when changing view
	};

	const currentColumns =
		viewType === "providers"
			? providerColumns
			: columns.map((col) => {
					if (col.accessorKey === "select") {
						return {
							...col,
							cell: ({ row }: any) => (
								<input
									type="checkbox"
									className="h-4 w-4 rounded border-gray-300"
									data-job-id={row.original.id}
									checked={selectedJobs.includes(row.original.id)}
									onChange={(e) => {
										const jobId = row.original.id;
										setSelectedJobs((prev) => {
											if (e.target.checked) {
												return [...prev, jobId];
											} else {
												return prev.filter((id) => id !== jobId);
											}
										});
									}}
								/>
							)
						};
					}
					return col;
				});

	return (
		<>
			<div className="space-y-4">
				{/* View Toggle */}
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<Tabs
							value={viewType}
							onValueChange={handleViewTypeChange}
							className="w-auto"
						>
							<TabsList className="grid w-full grid-cols-2">
								<TabsTrigger value="jobs" className="flex items-center gap-2">
									<Briefcase className="w-4 h-4" />
									Group by Job
								</TabsTrigger>
								<TabsTrigger
									value="providers"
									className="flex items-center gap-2"
								>
									<Users className="w-4 h-4" />
									Group by Provider
								</TabsTrigger>
							</TabsList>
						</Tabs>
					</div>
					<div className="flex items-center gap-2">
						{viewType === "jobs" && selectedJobs.length >= 2 ? (
							<Button
								onClick={() => setShowMergeDialog(true)}
								className="flex items-center gap-2"
							>
								Merge Selected ({selectedJobs.length})
							</Button>
						) : (
							<>
								<Link href="/admin/leads/analytics">
									<Button variant="outline" className="flex items-center gap-2">
										Analytics
									</Button>
								</Link>
								<Button
									onClick={handleExportCSV}
									disabled={exporting}
									className="flex items-center gap-2"
								>
									<Download size={16} />
									{exporting ? "Exporting..." : "Export CSV"}
								</Button>
							</>
						)}
					</div>
				</div>

				{/* Search and Filters */}
				<div className="flex items-center gap-4">
					{viewType === "jobs" ? (
						<Input
							placeholder="Search customers, emails, or messages..."
							value={search}
							onChange={(e) => setSearch(e.target.value)}
							className="max-w-sm"
						/>
					) : (
						<Input
							placeholder="Search providers by name, business, or email..."
							value={providerSearch}
							onChange={(e) => setProviderSearch(e.target.value)}
							className="max-w-sm"
						/>
					)}
					<Select value={providerStatus} onValueChange={setProviderStatus}>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Provider Status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Statuses</SelectItem>
							<SelectItem value="pending">Pending</SelectItem>
							<SelectItem value="accepted">Accepted</SelectItem>
							<SelectItem value="rejected">Rejected</SelectItem>
							<SelectItem value="resolved">Resolved</SelectItem>
						</SelectContent>
					</Select>
					<Select value={resolutionStatus} onValueChange={setResolutionStatus}>
						<SelectTrigger className="w-[180px]">
							<SelectValue placeholder="Resolution Status" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">All Resolutions</SelectItem>
							<SelectItem value="completed">Completed</SelectItem>
							<SelectItem value="cancelled">Cancelled</SelectItem>
							<SelectItem value="no_response">No Response</SelectItem>
							<SelectItem value="not_viable">Not Viable</SelectItem>
							<SelectItem value="referred">Referred</SelectItem>
							<SelectItem value="other">Other</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="bg-white rounded-lg border">
					<Table>
						<TableHeader>
							<TableRow>
								{currentColumns.map((column) => (
									<TableHead key={column.accessorKey as string}>
										{typeof column.header === "function"
											? column.header({
													column: {
														toggleSorting: () => {},
														getIsSorted: () => false
													}
												})
											: column.header}
									</TableHead>
								))}
							</TableRow>
						</TableHeader>
						<TableBody>
							{loading ? (
								<TableRow>
									<TableCell colSpan={currentColumns.length} className="h-24">
										<Loader />
									</TableCell>
								</TableRow>
							) : (viewType === "providers"
									? providerLeads.length
									: leads.length) === 0 ? (
								<TableRow>
									<TableCell
										colSpan={currentColumns.length}
										className="text-center py-8"
									>
										No {viewType === "providers" ? "provider leads" : "jobs"}{" "}
										found
									</TableCell>
								</TableRow>
							) : viewType === "providers" ? (
								providerLeads.map((lead) => (
									<TableRow key={lead.quote_id} className="hover:bg-muted/50">
										{providerColumns.map((column) => (
											<TableCell key={column.accessorKey as string}>
												{column.cell
													? column.cell({
															row: {
																original: lead,
																getValue: (key: string) =>
																	lead[key as keyof ProviderLead]
															}
														})
													: String(
															lead[column.accessorKey as keyof ProviderLead] ||
																""
														)}
											</TableCell>
										))}
									</TableRow>
								))
							) : (
								leads.map((lead) => (
									<TableRow key={lead.id} className="hover:bg-muted/50">
										{currentColumns.map((column) => (
											<TableCell key={column.accessorKey as string}>
												{column.cell
													? column.cell({
															row: {
																original: lead,
																getValue: (key: string) =>
																	lead[key as keyof Lead]
															}
														})
													: String(
															lead[column.accessorKey as keyof Lead] || ""
														)}
											</TableCell>
										))}
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>

				{pagination.totalPages > 1 && (
					<div className="mt-4 flex justify-center">
						<Pagination
							currentPage={pagination.page}
							totalPages={pagination.totalPages}
							onPageChange={(page) =>
								setPagination((prev) => ({ ...prev, page }))
							}
						/>
					</div>
				)}
			</div>

			<Dialog open={!!selectedLead} onOpenChange={() => setSelectedLead(null)}>
				<DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>Job Details</DialogTitle>
					</DialogHeader>

					{selectedLead && (
						<div className="space-y-6">
							{/* Customer Information */}
							<Card>
								<CardHeader>
									<CardTitle className="text-lg">
										Customer Information
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-3">
									<div className="grid grid-cols-2 gap-4">
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Name
											</label>
											<p className="text-sm">{selectedLead.user_name}</p>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Email
											</label>
											<p className="text-sm">{selectedLead.email}</p>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Phone
											</label>
											<p className="text-sm">{selectedLead.phone || "N/A"}</p>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Location
											</label>
											<p className="text-sm">
												{selectedLead.location?.city &&
												selectedLead.location?.state
													? `${selectedLead.location.city}, ${selectedLead.location.state}`
													: selectedLead.location?.address || "N/A"}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Job Information */}
							<Card>
								<CardHeader>
									<CardTitle className="text-lg">Job Information</CardTitle>
								</CardHeader>
								<CardContent className="space-y-3">
									<div className="grid grid-cols-2 gap-4">
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Category
											</label>
											<p className="text-sm capitalize">
												{getCategoryName(selectedLead.category)}
											</p>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Status
											</label>
											<div className="flex items-center gap-2">
												<Badge
													variant={
														selectedLead.status === JobStatus.COMPLETED
															? "success"
															: selectedLead.status === JobStatus.CANCELLED
																? "destructive"
																: selectedLead.status === JobStatus.IN_PROGRESS
																	? "success"
																	: "secondary"
													}
												>
													{selectedLead.status}
												</Badge>
											</div>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Created
											</label>
											<p className="text-sm">
												{new Date(selectedLead.created_at).toLocaleDateString()}
												({selectedLead.days_since_created} days ago)
											</p>
										</div>
										<div>
											<label className="text-sm font-medium text-muted-foreground">
												Providers Invited
											</label>
											<p className="text-sm">{selectedLead.quotes_count}</p>
										</div>
									</div>
									<div>
										<label className="text-sm font-medium text-muted-foreground">
											Message
										</label>
										<p className="text-sm bg-muted p-3 rounded-md">
											{selectedLead.message}
										</p>
									</div>
								</CardContent>
							</Card>

							{/* Provider Response Summary */}
							<Card>
								<CardHeader>
									<CardTitle className="text-lg">
										Provider Response Summary
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
										<div className="text-center">
											<div className="text-2xl font-bold text-blue-600">
												{selectedLead.quotes_invited}
											</div>
											<div className="text-sm text-muted-foreground">
												Invited
											</div>
										</div>
										<div className="text-center">
											<div className="text-2xl font-bold text-green-600">
												{selectedLead.quotes_responded}
											</div>
											<div className="text-sm text-muted-foreground">
												Responded
											</div>
										</div>
										<div className="text-center">
											<div className="text-2xl font-bold text-red-600">
												{selectedLead.quotes_declined}
											</div>
											<div className="text-sm text-muted-foreground">
												Declined
											</div>
										</div>
										<div className="text-center">
											<div className="text-2xl font-bold text-purple-600">
												{selectedLead.quotes_accepted}
											</div>
											<div className="text-sm text-muted-foreground">
												Accepted
											</div>
										</div>
									</div>

									<Separator className="my-4" />

									<div className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium">Response Rate</span>
											<span className="text-sm">
												{selectedLead.quotes_count > 0
													? `${Math.round((selectedLead.quotes_responded / selectedLead.quotes_count) * 100)}%`
													: "N/A"}
											</span>
										</div>
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium">
												Overall Status
											</span>
											<div>
												{selectedLead.quotes_accepted > 0 ? (
													<Badge variant="success">Job Accepted</Badge>
												) : selectedLead.quotes_declined ===
														selectedLead.quotes_count &&
												  selectedLead.quotes_count > 0 ? (
													<Badge variant="destructive">All Declined</Badge>
												) : selectedLead.has_responses ? (
													<Badge variant="secondary">Partial Response</Badge>
												) : (
													<Badge variant="warning">No Response</Badge>
												)}
											</div>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Individual Providers */}
							{selectedLead.providers && selectedLead.providers.length > 0 && (
								<Card>
									<CardHeader>
										<CardTitle className="text-lg">
											Invited Providers ({selectedLead.providers.length})
										</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-4">
											{selectedLead.providers.map((provider) => (
												<div
													key={provider.id}
													className="border rounded-lg p-4"
												>
													<div className="flex justify-between items-start mb-3">
														<div className="flex-1">
															<div className="flex items-center gap-2 mb-1">
																{provider.listing.business_name ? (
																	<Building2 className="w-3 h-3 text-blue-600" />
																) : (
																	<User className="w-3 h-3 text-green-600" />
																)}
																<h4 className="font-medium text-blue-600 hover:text-blue-800 transition-colors">
																	<Link
																		href={`/providers/${provider.listing.slug}`}
																		className="flex items-center gap-1 hover:underline"
																		target="_blank"
																		rel="noopener noreferrer"
																	>
																		{provider.listing.business_name ||
																			`${provider.listing.first_name} ${provider.listing.last_name}`}
																		<ExternalLink className="w-2 h-2" />
																	</Link>
																</h4>
															</div>
															<p className="text-sm text-muted-foreground">
																Invited{" "}
																{formatDistanceToNow(
																	new Date(provider.invited_at),
																	{
																		addSuffix: true
																	}
																)}
															</p>
														</div>
														{getQuoteStatusBadge(provider.status)}
													</div>

													<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
														<div>
															<dt className="font-medium text-muted-foreground">
																Email
															</dt>
															<dd>{provider.listing.email}</dd>
														</div>
														{provider.listing.phone && (
															<div>
																<dt className="font-medium text-muted-foreground">
																	Phone
																</dt>
																<dd>{provider.listing.phone}</dd>
															</div>
														)}
														{provider.listing.location && (
															<div>
																<dt className="font-medium text-muted-foreground">
																	Location
																</dt>
																<dd>
																	{provider.listing.location.city &&
																	provider.listing.location.state
																		? `${provider.listing.location.city}, ${provider.listing.location.state}`
																		: "Location not specified"}
																</dd>
															</div>
														)}
														{provider.distance_miles && (
															<div>
																<dt className="font-medium text-muted-foreground">
																	Distance
																</dt>
																<dd>
																	{provider.distance_miles.toFixed(1)} miles
																</dd>
															</div>
														)}
													</div>

													{provider.provider_notes && (
														<div className="mt-3 pt-3 border-t">
															<dt className="font-medium text-muted-foreground mb-1">
																Provider Notes
															</dt>
															<dd className="text-sm whitespace-pre-wrap">
																{provider.provider_notes}
															</dd>
														</div>
													)}

													{provider.responded_at && (
														<div className="mt-2 text-xs text-muted-foreground">
															Responded{" "}
															{formatDistanceToNow(
																new Date(provider.responded_at),
																{
																	addSuffix: true
																}
															)}
														</div>
													)}
												</div>
											))}
										</div>
									</CardContent>
								</Card>
							)}
						</div>
					)}
				</DialogContent>
			</Dialog>

			{showMergeDialog && (
				<MergeJobsDialog
					isOpen={showMergeDialog}
					onClose={() => {
						setShowMergeDialog(false);
						setSelectedJobs([]);
					}}
					selectedJobs={leads.filter((lead) => selectedJobs.includes(lead.id))}
					onSuccess={() => {
						setShowMergeDialog(false);
						setSelectedJobs([]);
						// Refresh the data by triggering a re-fetch
						setPagination((prev) => ({ ...prev }));
					}}
				/>
			)}
		</>
	);
}
