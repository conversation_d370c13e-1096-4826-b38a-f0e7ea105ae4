import prisma from "@/lib/prisma";
import { SearchAlgorithmForm } from "./search/search-algorithm-form";

export default async function SearchSettingsPage() {
	const settings = await prisma.siteSettings.findFirst({
		where: { key: "search_algorithm" }
	});

	// Parse the JSON value from the database
	const parsedSettings = settings?.value
		? JSON.parse(JSON.stringify(settings.value))
		: undefined;

	return (
		<div className="container mx-auto py-8">
			<h1 className="text-2xl font-bold mb-6">Search Algorithm Settings</h1>
			<div className="bg-white rounded-lg shadow p-6">
				<SearchAlgorithmForm initialSettings={parsedSettings} />
			</div>
		</div>
	);
}
