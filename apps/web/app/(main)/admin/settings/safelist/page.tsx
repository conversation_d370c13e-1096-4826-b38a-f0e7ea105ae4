"use client";

import { AddSafelistEntry } from "@/components/admin/safelist/AddSafelistEntry";
import { SafelistTable } from "@/components/admin/safelist/SafelistTable";
import { useRef } from "react";

export default function SafelistPage() {
	const tableRef = useRef<{ fetchEntries: () => Promise<void> }>(null);

	const handleEntryAdded = () => {
		tableRef.current?.fetchEntries();
	};

	return (
		<div className="space-y-6">
			<AddSafelistEntry onSuccess={handleEntryAdded} />
			<SafelistTable ref={tableRef} />
		</div>
	);
}
