"use client";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import Wysiwyg from "@/components/Wysiwyg";
import { upload } from "@/lib/storage";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm, useFormContext } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const formSchema = z.object({
	title: z.string().min(1, "Title is required"),
	slug: z.string().min(1, "Slug is required"),
	description: z.string().min(1, "Description is required"),
	content: z.string().min(1, "Content is required"),
	type: z.enum(
		[
			"press-release",
			"blog-post",
			"job-posting",
			"help-article",
			"provider-announcement",
			"provider-training",
			"provider-office-hours"
		],
		{
			required_error: "Type is required"
		}
	),
	status: z.enum(["draft", "published"], {
		required_error: "Status is required"
	}),
	pinned: z.boolean().default(false),
	is_featured_provider: z.boolean().default(false),
	meta_image: z.string().nullable().optional(),
	zoom_url: z.string().nullable().optional(),
	youtube_url: z.string().nullable().optional(),
	tags: z.array(z.string()).default([]),
	lead_magnet_id: z.string().nullable().optional()
});

type FormData = z.infer<typeof formSchema>;

interface ArticleFormProps {
	mode?: "create" | "edit";
	article?: {
		id: string;
		title: string;
		slug: string;
		description: string;
		content: string;
		type: string;
		status: string;
		pinned?: boolean;
		is_featured_provider?: boolean;
		meta_image?: string;
		zoom_url?: string;
		youtube_url?: string;
		tags?: string[];
		lead_magnet_id?: string;
	};
	defaultType?:
		| "press-release"
		| "blog-post"
		| "job-posting"
		| "help-article"
		| "provider-announcement"
		| "provider-training"
		| "provider-office-hours";
	onSaving?: (saving: boolean) => void;
	onSaved?: (saved: boolean) => void;
}

export function ArticleFormSidebar() {
	const form = useFormContext<FormData>();
	const { isSubmitting } = form.formState;
	const status = form.watch("status");
	const type = form.watch("type");
	const slug = form.watch("slug");
	const tags = form.watch("tags") || [];
	const meta_image = form.watch("meta_image");
	const pinned = form.watch("pinned");
	const [leadMagnets, setLeadMagnets] = useState<
		{ id: string; title: string; status: string }[]
	>([]);

	// Fetch lead magnets when component mounts
	useEffect(() => {
		const fetchLeadMagnets = async () => {
			try {
				const response = await fetch("/api/admin/lead-magnets");
				if (response.ok) {
					const data = await response.json();
					setLeadMagnets(data.filter((lm: any) => lm.status === "active"));
				}
			} catch (error) {
				console.error("Failed to fetch lead magnets:", error);
			}
		};

		fetchLeadMagnets();
	}, []);

	const addTag = (tag: string) => {
		if (!tags.includes(tag)) {
			form.setValue("tags", [...tags, tag]);
		}
	};

	const removeTag = (tagToRemove: string) => {
		form.setValue(
			"tags",
			tags.filter((tag) => tag !== tagToRemove)
		);
	};

	const getPreviewUrl = () => {
		switch (type) {
			case "press-release":
				return `/press/${slug}`;
			case "blog-post":
				return `/blog/${slug}`;
			case "job-posting":
				return `/careers/${slug}`;
			case "help-article":
				return `/help/${slug}`;
			case "provider-announcement":
			case "provider-training":
				return `/provider/announcements/${slug}`;
			default:
				return "";
		}
	};

	const handleImageUpload = async (file: File) => {
		try {
			const result = await upload(
				file,
				`article-${Date.now()}`,
				"article-images"
			);

			if (result.error) {
				throw new Error(result.error);
			}

			form.setValue("meta_image", result.url);
		} catch (error) {
			console.error("Upload error:", error);
			toast.error("Failed to upload image");
		}
	};

	return (
		<>
			<Card className="p-6">
				<h3 className="font-medium mb-4">Article Settings</h3>
				<div className="space-y-4">
					<FormField
						control={form.control}
						name="pinned"
						render={({ field }) => (
							<FormItem className="flex flex-row items-center justify-between">
								<FormLabel>Pin to top</FormLabel>
								<FormControl>
									<Switch
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								</FormControl>
							</FormItem>
						)}
					/>

					{type === "blog-post" && (
						<>
							<FormField
								control={form.control}
								name="is_featured_provider"
								render={({ field }) => (
									<FormItem className="flex flex-row items-center justify-between">
										<FormLabel>Featured Provider</FormLabel>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="lead_magnet_id"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Lead Magnet</FormLabel>
										<Select
											onValueChange={(value) =>
												field.onChange(value === "none" ? null : value)
											}
											value={field.value || "none"}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select lead magnet (optional)" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="none">None</SelectItem>
												{leadMagnets.map((magnet) => (
													<SelectItem key={magnet.id} value={magnet.id}>
														{magnet.title}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</>
					)}

					<FormField
						control={form.control}
						name="slug"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Slug</FormLabel>
								<FormControl>
									<Input placeholder="article-slug" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="type"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Category</FormLabel>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Select category" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="press-release">Press Release</SelectItem>
										<SelectItem value="blog-post">Blog Post</SelectItem>
										<SelectItem value="job-posting">Job Posting</SelectItem>
										<SelectItem value="help-article">Help Article</SelectItem>
										<SelectItem value="provider-announcement">
											Provider Announcement
										</SelectItem>
										<SelectItem value="provider-training">
											Provider Training
										</SelectItem>
										<SelectItem value="provider-office-hours">
											Provider Office Hours
										</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="tags"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Tags</FormLabel>
								<FormControl>
									<div className="space-y-2">
										<div className="flex flex-wrap gap-2">
											{field.value?.map((tag) => (
												<Badge
													key={tag}
													variant="secondary"
													className="cursor-pointer"
													onClick={() => {
														field.onChange(
															field.value.filter((t) => t !== tag)
														);
													}}
												>
													{tag}
												</Badge>
											))}
										</div>
										<div className="flex gap-2">
											<Input
												placeholder="Add tags (press Enter)"
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														e.preventDefault();
														const input = e.target as HTMLInputElement;
														const tag = input.value.trim();
														if (tag && !field.value?.includes(tag)) {
															field.onChange([...(field.value || []), tag]);
															input.value = "";
														}
													}
												}}
											/>
											<Button
												type="button"
												variant="outline"
												onClick={() => {
													const input = document.querySelector(
														'input[placeholder="Add tags (press Enter)"]'
													) as HTMLInputElement;
													if (input) {
														const tag = input.value.trim();
														if (tag && !field.value?.includes(tag)) {
															field.onChange([...(field.value || []), tag]);
															input.value = "";
														}
													}
												}}
											>
												Add
											</Button>
										</div>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</Card>

			<Card className="p-6">
				<h3 className="font-medium mb-4">Publishing</h3>
				<div className="space-y-4">
					<FormField
						control={form.control}
						name="status"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Status</FormLabel>
								<Select onValueChange={field.onChange} value={field.value}>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Select status" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="draft">Draft</SelectItem>
										<SelectItem value="published">Published</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<div className="flex flex-col gap-2">
						<Button type="submit" className="w-full" disabled={isSubmitting}>
							{isSubmitting ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									{status === "published" ? "Publishing..." : "Saving..."}
								</>
							) : status === "published" ? (
								"Publish"
							) : (
								"Save Draft"
							)}
						</Button>
						{slug && (
							<Link href={getPreviewUrl()} target="_blank">
								<Button variant="outline" className="w-full" type="button">
									{status === "published" ? "View" : "Preview"}
								</Button>
							</Link>
						)}
					</div>
				</div>
			</Card>

			<Card className="p-6">
				<h3 className="font-medium mb-4">Featured Image</h3>
				<div className="space-y-4">
					<FormField
						control={form.control}
						name="meta_image"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<div className="space-y-2">
										{field.value && (
											<div className="relative w-full aspect-video rounded-lg overflow-hidden">
												<img
													src={field.value}
													alt="Featured"
													className="object-cover w-full h-full"
												/>
												<Button
													type="button"
													variant="destructive"
													size="sm"
													className="absolute top-2 right-2"
													onClick={() => form.setValue("meta_image", "")}
												>
													Remove
												</Button>
											</div>
										)}
										<Input
											type="file"
											accept="image/*"
											onChange={(e) => {
												const file = e.target.files?.[0];
												if (file) {
													handleImageUpload(file);
												}
											}}
										/>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</Card>
		</>
	);
}

export function ArticleForm({
	mode = "create",
	article,
	defaultType,
	onSaving,
	onSaved
}: ArticleFormProps) {
	const router = useRouter();
	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			status: (article?.status as "draft" | "published") || "draft",
			type:
				defaultType ||
				(article?.type as
					| "press-release"
					| "blog-post"
					| "job-posting"
					| "help-article"
					| "provider-announcement"
					| "provider-training"
					| "provider-office-hours") ||
				"press-release",
			title: article?.title || "",
			slug: article?.slug || "",
			description: article?.description || "",
			content: article?.content || "",
			meta_image: article?.meta_image || "",
			zoom_url: article?.zoom_url || "",
			youtube_url: article?.youtube_url || "",
			tags: article?.tags || [],
			pinned: article?.pinned || false,
			is_featured_provider: article?.is_featured_provider || false,
			lead_magnet_id: article?.lead_magnet_id || null
		}
	});

	const type = form.watch("type");
	const isProviderTraining =
		type === "provider-training" || type === "provider-office-hours";

	useEffect(() => {
		if (article) {
			form.reset({
				status: article.status as "draft" | "published",
				type: article.type as
					| "press-release"
					| "blog-post"
					| "job-posting"
					| "help-article"
					| "provider-announcement"
					| "provider-training"
					| "provider-office-hours",
				title: article.title,
				slug: article.slug,
				description: article.description,
				content: article.content,
				meta_image: article.meta_image,
				zoom_url: article.zoom_url,
				youtube_url: article.youtube_url,
				tags: article.tags || [],
				pinned: article.pinned || false,
				is_featured_provider: article.is_featured_provider || false,
				lead_magnet_id: article.lead_magnet_id || null
			});
		}
	}, [article]);

	const onSubmit = form.handleSubmit(
		async (data: FormData) => {
			onSaving?.(true);
			try {
				const url =
					mode === "edit"
						? `/api/admin/articles/${article?.id}`
						: "/api/admin/articles";

				// Ensure tags is always an array
				const formData = {
					...data,
					tags: data.tags || []
				};

				const response = await fetch(url, {
					method: mode === "edit" ? "PUT" : "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(formData)
				});

				if (!response.ok) throw new Error(`Failed to ${mode} article`);

				const updatedArticle = await response.json();

				// Update form with new values if in edit mode
				if (mode === "edit") {
					const resetData = {
						status: updatedArticle.status,
						type: updatedArticle.type,
						title: updatedArticle.title,
						slug: updatedArticle.slug,
						description: updatedArticle.description,
						content: updatedArticle.content,
						meta_image: updatedArticle.meta_image || "",
						zoom_url: updatedArticle.zoom_url || "",
						youtube_url: updatedArticle.youtube_url || "",
						tags: updatedArticle.tags || [],
						pinned: updatedArticle.pinned || false,
						is_featured_provider: updatedArticle.is_featured_provider || false,
						lead_magnet_id: updatedArticle.lead_magnet_id || null
					};

					form.reset(resetData);
				} else {
					// Redirect to edit page after creation
					router.push(`/admin/content/${updatedArticle.id}/edit`);
				}

				toast.success(
					`Article ${mode === "edit" ? "updated" : "created"} successfully`
				);
			} catch (error) {
				console.error("Submission error:", error);
				toast.error(`Failed to ${mode} article`);
			} finally {
				onSaving?.(false);
				onSaved?.(true);
			}
		},
		(errors) => {
			console.error("Form validation failed:", errors);
			toast.error("Please fill in all required fields");
		}
	);

	return (
		<Form {...form}>
			<form onSubmit={onSubmit} className="grid grid-cols-3 gap-6">
				<Card className="col-span-2 space-y-6 p-6">
					<FormField
						control={form.control}
						name="title"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Title</FormLabel>
								<FormControl>
									<Input placeholder="Article title" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Description</FormLabel>
								<FormControl>
									<Input placeholder="Brief description" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="content"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Content</FormLabel>
								<FormControl>
									<Wysiwyg
										variant="full"
										name="content"
										value={field.value}
										onChange={field.onChange}
										className="min-h-[400px]"
										enableLinks={true}
										enableImages={true}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="zoom_url"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Zoom Meeting URL</FormLabel>
								<FormControl>
									<Input
										placeholder="https://zoom.us/j/..."
										{...field}
										value={field.value || ""}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="youtube_url"
						render={({ field }) => (
							<FormItem>
								<FormLabel>YouTube Video URL</FormLabel>
								<FormControl>
									<Input
										placeholder="https://youtube.com/watch?v=..."
										{...field}
										value={field.value || ""}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</Card>
				<div className="space-y-6">
					<ArticleFormSidebar />
				</div>
			</form>
		</Form>
	);
}
