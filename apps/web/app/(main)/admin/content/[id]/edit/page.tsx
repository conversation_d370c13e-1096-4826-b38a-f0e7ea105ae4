"use client";

import { ArticleForm } from "@/app/(main)/admin/content/ArticleForm";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Article {
	id: string;
	title: string;
	slug: string;
	description: string;
	content: string;
	type: string;
	status: string;
	meta_image?: string;
	zoom_url?: string;
}

function ArticleFormSkeleton() {
	return (
		<div className="space-y-6">
			<div className="flex items-center gap-2">
				<Loader2 className="h-4 w-4 animate-spin" />
				<span className="text-sm text-gray-500">Saving changes...</span>
			</div>
			<Card className="p-6">
				<div className="space-y-6">
					<div className="h-10 bg-gray-200 rounded w-1/2 animate-pulse" />
					<div className="h-10 bg-gray-200 rounded w-full animate-pulse" />
					<div className="h-40 bg-gray-200 rounded w-full animate-pulse" />
				</div>
			</Card>
		</div>
	);
}

export default function EditArticlePage() {
	const params = useParams();
	const [article, setArticle] = useState<Article | null>(null);
	const [loading, setLoading] = useState(true);
	const [isSaving, setIsSaving] = useState(false);

	async function fetchArticle() {
		try {
			const response = await fetch(`/api/admin/articles/${params.id}`);
			if (!response.ok) throw new Error("Failed to fetch article");
			const data = await response.json();
			setArticle(data);
		} catch (error) {
			console.error("Error fetching article:", error);
		} finally {
			setLoading(false);
		}
	}

	useEffect(() => {
		fetchArticle();
	}, [params.id]);

	if (loading || isSaving) {
		return (
			<div className="container py-10">
				{isSaving ? (
					<ArticleFormSkeleton />
				) : (
					<div className="animate-pulse space-y-6">
						<div className="h-8 bg-gray-200 rounded w-1/4" />
						<Card className="p-6">
							<div className="space-y-6">
								<div className="h-10 bg-gray-200 rounded w-1/2" />
								<div className="h-10 bg-gray-200 rounded w-full" />
								<div className="h-40 bg-gray-200 rounded w-full" />
							</div>
						</Card>
					</div>
				)}
			</div>
		);
	}

	if (!article) {
		return (
			<div className="container flex flex-col gap-4">
				<Link
					href="/admin/content"
					className="text-primary hover:text-primary/80"
				>
					Back to Content
				</Link>

				<h1 className="text-2xl font-bold mb-4">Article Not Found</h1>
			</div>
		);
	}

	return (
		<div className="container">
			<div className="flex flex-col gap-4 mb-6">
				<Link
					href="/admin/content"
					className="inline-flex items-center text-primary hover:text-primary/80"
				>
					<ArrowLeft className="w-4 h-4 mr-2" />
					Back to Content
				</Link>
				<h1 className="text-2xl font-bold">Edit Article</h1>
			</div>

			<ArticleForm
				mode="edit"
				article={article}
				onSaving={setIsSaving}
				onSaved={fetchArticle}
			/>
		</div>
	);
}
