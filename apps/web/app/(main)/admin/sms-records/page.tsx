"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@/components/ui/table";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { formatToE164 } from "@/lib/utils";
import { format } from "date-fns";
import { Info } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

interface SMSRecord {
	id: string;
	to: string;
	body: string;
	status: string;
	dateSent: string;
	listing: {
		id: string;
		business_name: string;
	} | null;
}

export default function SMSRecordsPage() {
	const [records, setRecords] = useState<SMSRecord[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [phoneFilter, setPhoneFilter] = useState("");
	const [businessFilter, setBusinessFilter] = useState("");
	const [statusFilter, setStatusFilter] = useState<string | null>(null);
	const [dateRange, setDateRange] = useState<{
		from: Date | undefined;
		to: Date | undefined;
	}>({
		from: undefined,
		to: undefined
	});
	const [selectedMessage, setSelectedMessage] = useState<SMSRecord | null>(
		null
	);

	const fetchRecords = async () => {
		setLoading(true);
		setError(null);

		try {
			const params = new URLSearchParams();
			if (phoneFilter) params.append("phone", formatToE164(phoneFilter));
			if (businessFilter) params.append("businessName", businessFilter);
			if (dateRange.from)
				params.append("startDate", dateRange.from.toISOString());
			if (dateRange.to) params.append("endDate", dateRange.to.toISOString());

			const response = await fetch(
				`/api/admin/sms-records?${params.toString()}`
			);
			if (!response.ok) throw new Error("Failed to fetch SMS records");

			const data = await response.json();
			setRecords(data.messages);
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchRecords();
	}, []);

	const filteredRecords = records.filter((record) =>
		statusFilter ? record.status === statusFilter : true
	);

	const statusOptions = Array.from(
		new Set(records.map((record) => record.status))
	);

	return (
		<div className="p-6 max-w-[1400px] mx-auto">
			<h1 className="text-2xl font-bold mb-6">SMS Records</h1>

			<div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<div className="relative">
								<Input
									placeholder="Search by phone number"
									value={phoneFilter}
									onChange={(e) => setPhoneFilter(e.target.value)}
									className="w-full pr-8"
								/>
								<Info className="absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
							</div>
						</TooltipTrigger>
						<TooltipContent>
							<p>
								Enter full phone number including country code (e.g. +1 for
								US/CA)
							</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
				<Input
					placeholder="Search by business name"
					value={businessFilter}
					onChange={(e) => setBusinessFilter(e.target.value)}
					className="w-full"
				/>
				<Select
					value={statusFilter || "all"}
					onValueChange={(value) =>
						setStatusFilter(value === "all" ? null : value)
					}
				>
					<SelectTrigger>
						<SelectValue placeholder="Filter by status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Statuses</SelectItem>
						{statusOptions.map((status) => (
							<SelectItem key={status} value={status}>
								{status.charAt(0).toUpperCase() + status.slice(1)}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<DatePickerWithRange
					date={dateRange}
					setDate={(range) => {
						if (range) {
							setDateRange({
								from: range.from,
								to: range.to
							});
						}
					}}
					className="w-full"
				/>
				<Button onClick={fetchRecords} disabled={loading}>
					{loading ? "Loading..." : "Filter Results"}
				</Button>
			</div>

			{error && (
				<div className="bg-red-50 text-red-600 p-4 rounded-md mb-4">
					{error}
				</div>
			)}

			<div className="bg-white rounded-lg shadow overflow-hidden">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Date</TableHead>
							<TableHead>To</TableHead>
							<TableHead>Business</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Message</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{filteredRecords.map((record) => (
							<TableRow key={record.id}>
								<TableCell className="whitespace-nowrap">
									{format(new Date(record.dateSent), "MMM d, yyyy h:mm a")}
								</TableCell>
								<TableCell className="whitespace-nowrap">{record.to}</TableCell>
								<TableCell>
									{record.listing ? (
										<Link
											href={`/admin/listings/${record.listing.id}`}
											className="text-blue-600 hover:underline"
										>
											{record.listing.business_name}
										</Link>
									) : (
										"Unknown"
									)}
								</TableCell>
								<TableCell>
									<span
										className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
											record.status === "delivered"
												? "bg-green-100 text-green-800"
												: record.status === "failed"
													? "bg-red-100 text-red-800"
													: "bg-gray-100 text-gray-800"
										}`}
									>
										{record.status}
									</span>
								</TableCell>
								<TableCell>
									<div className="flex items-center">
										<span className="truncate max-w-[300px]">
											{record.body}
										</span>
										<Button
											variant="ghost"
											size="sm"
											className="ml-2"
											onClick={() => setSelectedMessage(record)}
										>
											View More
										</Button>
									</div>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			<Dialog
				open={!!selectedMessage}
				onOpenChange={() => setSelectedMessage(null)}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Message</DialogTitle>
					</DialogHeader>
					<div className="mt-4">
						<p className="whitespace-pre-wrap">{selectedMessage?.body}</p>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
