"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

interface MemberInspectionModalProps {
	memberId: string;
	isOpen: boolean;
	onClose: () => void;
	onImportSuccess: () => void;
}

export default function MemberInspectionModal({
	memberId,
	isOpen,
	onClose,
	onImportSuccess
}: MemberInspectionModalProps) {
	const [memberData, setMemberData] = useState<any>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isImporting, setIsImporting] = useState(false);

	useEffect(() => {
		async function fetchMemberData() {
			setIsLoading(true);
			try {
				const response = await fetch("/api/admin/rvsg/member", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ id: memberId })
				});

				if (!response.ok) {
					throw new Error("Failed to fetch member data");
				}

				const data = await response.json();
				setMemberData(data);
			} catch (error) {
				toast.error("Failed to load member data");
			} finally {
				setIsLoading(false);
			}
		}

		if (isOpen) {
			fetchMemberData();
		}
	}, [memberId, isOpen]);

	const handleImport = async () => {
		setIsImporting(true);
		try {
			const response = await fetch("/api/admin/import/rvtaa/single", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ userId: memberId })
			});

			if (!response.ok) {
				throw new Error("Import failed");
			}

			toast.success("Member imported successfully");
			onImportSuccess();
			onClose();
		} catch (error) {
			toast.error("Failed to import member");
		} finally {
			setIsImporting(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>Member Information</DialogTitle>
				</DialogHeader>

				<ScrollArea className="mt-4 h-[60vh]">
					{isLoading ? (
						<div className="flex items-center justify-center h-40">
							<Loader2 className="h-8 w-8 animate-spin text-primary" />
						</div>
					) : (
						<div className="space-y-6">
							<pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
								{JSON.stringify(memberData, null, 2)}
							</pre>

							<div className="flex justify-end gap-2">
								<Button variant="outline" onClick={onClose}>
									Cancel
								</Button>
								<Button onClick={handleImport} disabled={isImporting}>
									{isImporting ? (
										<>
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
											Importing...
										</>
									) : (
										"Import Member"
									)}
								</Button>
							</div>
						</div>
					)}
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}
