"use client";

import DirectoryStatePage from "@/components/directory/DirectoryStatePage";
import { City } from "@/lib/services/cities.service";
import { Award, Shield, Star } from "lucide-react";

export default function RVRepairStatePage({
	params,
	cities
}: {
	params: { state: string };
	cities: City[];
}) {
	return (
		<DirectoryStatePage
			category="rv-repair"
			params={params}
			title="RV Mobile Repair Services"
			description="Find trusted RV mobile repair technicians in your area"
			heroImage="/images/rv-repair-hero.webp"
			cities={cities}
			benefits={[
				{
					icon: Shield,
					title: "Verified Professionals",
					description:
						"All RV mobile repair technicians are vetted and verified for quality service."
				},
				{
					icon: Star,
					title: "Customer Reviews",
					description: "Read authentic reviews from RV owners in your area."
				},
				{
					icon: Award,
					title: "Certified Experts",
					description:
						"Find certified RV mobile repair technicians with proven expertise."
				}
			]}
		/>
	);
}
