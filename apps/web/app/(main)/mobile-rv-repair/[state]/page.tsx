import { generateStatePageMetadata } from "@/lib/metadata";
import { getCitiesByRegion } from "@/lib/services/cities.service";
import RVMobileRepairStateClient from "./client";

export async function generateMetadata(props: { params: { state: string } }) {
	return generateStatePageMetadata(props.params, "repair");
}

export default async function RVMobileRepairStatePage(props: {
	params: { state: string };
}) {
	// Decode the state parameter and fetch cities
	const decodedState = decodeURIComponent(props.params.state);
	const cities = await getCitiesByRegion(decodedState);

	return <RVMobileRepairStateClient {...props} cities={cities} />;
}
