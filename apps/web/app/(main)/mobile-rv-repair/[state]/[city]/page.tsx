import { getCityByNameAndRegion } from "@/lib/services/cities.service";
import { Metadata } from "next";
import RVMobileRepairCityClient from "./client";

// Generate enhanced title targeting both "near me" and "in city" intents
const generateEnhancedTitle = (
	city: string,
	state: string,
	providerCount: number,
	cityContent?: any
): string => {
	// Use custom title from city content if available
	if (cityContent?.title) {
		return cityContent.title;
	}

	const baseTitle = "Mobile RV Repair";
	// Target both search intents: "rv repair near me" AND "rv repair in Dallas"
	return `${baseTitle} Near You in ${city}, ${state}`;
};

// Generate enhanced description with local SEO signals
const generateEnhancedDescription = (
	city: string,
	state: string,
	providerCount: number,
	cityContent?: any,
	distance: string = "100"
): string => {
	// Use custom content from city content if available
	if (cityContent?.content) {
		// Truncate content to reasonable description length (around 160 characters)
		const truncatedContent =
			cityContent.content.length > 160
				? cityContent.content.substring(0, 157) + "..."
				: cityContent.content;
		return truncatedContent;
	}

	return `Certified mobile RV repair services near you in ${city}, ${state}. Emergency repairs, routine maintenance, and troubleshooting in the ${city} area.`;
};

export async function generateMetadata({
	params
}: {
	params: { state: string; city: string };
}): Promise<Metadata> {
	const city = decodeURIComponent(params.city)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");
	const state = decodeURIComponent(params.state)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");

	// Get city coordinates for the API call
	const cityData = await getCityByNameAndRegion(city, state);

	// Fetch city content for customization
	let cityContent = null;
	try {
		const cityContentResponse = await fetch(
			`${process.env.NEXT_PUBLIC_APP_URL}/api/city-content/${params.city.toLowerCase()}?category=rv-repair`
		);
		if (cityContentResponse.ok) {
			cityContent = await cityContentResponse.json();
		}
	} catch (error) {
		console.error("Error fetching city content:", error);
	}

	try {
		const searchParams = new URLSearchParams({
			page: "1",
			limit: "1",
			radius: "100",
			category: "rv-repair"
		});

		// Use coordinates if available, otherwise fall back to city/state
		if (cityData?.lat && cityData?.lng) {
			searchParams.set("lat", cityData.lat);
			searchParams.set("lng", cityData.lng);
		} else {
			searchParams.set("city", city);
			searchParams.set("state", state);
		}

		const response = await fetch(
			`${process.env.NEXT_PUBLIC_APP_URL}/api/listings/search?${searchParams}`
		);

		if (!response.ok) {
			// Fallback metadata
			const title = generateEnhancedTitle(city, state, 0, cityContent);
			const description = generateEnhancedDescription(
				city,
				state,
				0,
				cityContent
			);

			return {
				title,
				description,
				robots: { index: true, follow: true }
			};
		}

		const data = await response.json();
		const providerCount = data.total || 0;
		const hasProviders = providerCount > 0;

		// Enhanced title and description with city content
		const title = generateEnhancedTitle(
			city,
			state,
			providerCount,
			cityContent
		);
		const description = generateEnhancedDescription(
			city,
			state,
			providerCount,
			cityContent
		);

		// Self-referencing canonical (no competing pages to worry about)
		const stateParam = params.state.toLowerCase();
		const cityParam = params.city.toLowerCase();
		const canonicalUrl = `https://rvhelp.com/mobile-rv-repair/${stateParam}/${cityParam}`;

		// Simple indexing rule - index if we have providers
		const shouldIndex = hasProviders;

		// Enhanced structured data
		const jsonLd = {
			"@context": "https://schema.org",
			"@type": "Service",
			name: `Mobile RV Repair in ${city}, ${state}`,
			description: description,
			provider: {
				"@type": "Organization",
				name: "RVHelp"
			},
			areaServed: {
				"@type": "City",
				name: city,
				containedInPlace: {
					"@type": "State",
					name: state
				}
			},
			serviceType: "Mobile RV Repair",
			audience: {
				"@type": "Audience",
				audienceType: "RV Owners"
			}
		};

		return {
			title,
			description,
			robots: {
				index: shouldIndex,
				follow: true
			},
			alternates: { canonical: canonicalUrl },
			openGraph: {
				title,
				description,
				siteName: "RVHelp",
				type: "website",
				locale: "en_US",
				images: [
					{
						url: "/images/rv-repair-hero.webp",
						width: 1200,
						height: 630,
						alt: `Mobile RV repair services in ${city}, ${state}`
					}
				]
			},
			twitter: {
				card: "summary_large_image",
				title,
				description,
				images: ["/images/rv-repair-hero.webp"]
			},
			other: {
				"script:ld+json": JSON.stringify(jsonLd)
			}
		};
	} catch (error) {
		console.error("Error generating metadata:", error);
		const title = generateEnhancedTitle(city, state, 0, cityContent);
		const description = generateEnhancedDescription(
			city,
			state,
			0,
			cityContent
		);

		return {
			title,
			description,
			robots: { index: true, follow: true }
		};
	}
}

export default async function RVMobileRepairCityPage(props: {
	params: { state: string; city: string };
}) {
	// Decode city and state names
	const city = decodeURIComponent(props.params.city)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");
	const state = decodeURIComponent(props.params.state)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");

	// Fetch city coordinates from database
	const cityData = await getCityByNameAndRegion(city, state);

	// Fetch city content for customization
	let cityContent = null;
	try {
		const cityContentResponse = await fetch(
			`${process.env.NEXT_PUBLIC_APP_URL}/api/city-content/${props.params.city.toLowerCase()}?category=rv-repair`
		);
		if (cityContentResponse.ok) {
			cityContent = await cityContentResponse.json();
		}
	} catch (error) {
		console.error("Error fetching city content:", error);
	}

	return (
		<RVMobileRepairCityClient
			{...props}
			cityData={cityData}
			cityContent={cityContent}
		/>
	);
}
