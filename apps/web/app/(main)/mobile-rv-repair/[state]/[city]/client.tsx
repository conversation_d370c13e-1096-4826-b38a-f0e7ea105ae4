"use client";

import DirectoryCityPage from "@/components/directory/DirectoryCityPage";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { categories } from "@/lib/categories";
import { City } from "@/lib/services/cities.service";
import { useEffect, useState } from "react";

// Enhanced Static SEO Content Component with Breadcrumbs
function EnhancedSEOContent({
	city,
	state,
	providerCount,
	params,
	cityContent
}: {
	city: string;
	state: string;
	providerCount?: number;
	params: { state: string; city: string };
	cityContent?: any;
}) {
	// Generate enhanced H1 targeting both "near me" and "in city" intents
	const h1Text =
		cityContent?.title || `Find Mobile RV Repair Near You in ${city}, ${state}`;

	// Generate intro text with local SEO signals
	const getIntroText = () => {
		// Use custom content from city content if available
		if (cityContent?.content) {
			return cityContent.content;
		}

		const count = providerCount || 0;
		const countText =
			count > 0 ? `Compare ${count} certified` : "Find certified";

		return `${countText} mobile RV repair technicians offering comprehensive repair services in the ${city} metro area. Our certified professionals come to you for diagnostics, emergency fixes, and routine maintenance. Available 7 days a week for all RV types including travel trailers and motorhomes. Ready, available, and here to help whenever you need us.`;
	};

	// Additional local SEO content
	const getServiceAreas = () => {
		return `Serving ${city}, ${state} and surrounding areas within 150+ miles. Emergency service available with fast response times.`;
	};

	// Breadcrumb items
	const breadcrumbItems = [
		{
			label: "Mobile RV Repair",
			href: "/mobile-rv-repair"
		},
		{
			label: state,
			href: `/mobile-rv-repair/${params.state}`
		},
		{
			label: city
		}
	];

	return (
		<div className="bg-white border-b border-gray-200">
			<div className="container mx-auto px-4 py-8">
				{/* Breadcrumbs */}
				<Breadcrumbs items={breadcrumbItems} className="mb-6 text-gray-600" />

				{/* Mobile Scroll to Results Alert - Only visible on mobile */}
				<div className="lg:hidden bg-[#43806c] text-white py-3 px-4 mb-6 rounded-lg shadow-md">
					<button
						onClick={() => {
							const resultsSection = document.getElementById("results-section");
							if (resultsSection) {
								resultsSection.scrollIntoView({
									behavior: "smooth",
									block: "start"
								});
							}
						}}
						className="w-full text-left hover:bg-[#3a6d5a] p-2 rounded transition-colors"
					>
						<div className="flex items-center justify-between">
							<div>
								<span className="font-semibold text-lg">
									{providerCount || 0} technicians found
								</span>
								<div className="text-white/90 text-sm">
									in {city}, {state}
								</div>
							</div>
							<div className="text-white">
								<svg
									className="w-6 h-6"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M19 14l-7 7m0 0l-7-7m7 7V3"
									/>
								</svg>
							</div>
						</div>
					</button>
				</div>

				{/* Main H1 - Critical for SEO targeting both intents */}
				<h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
					{h1Text}
				</h1>

				{/* Intro paragraph with comprehensive local signals */}
				<p className="text-lg text-gray-700 mb-4 leading-relaxed">
					{getIntroText()}
				</p>

				{/* Service area info */}
				<p className="text-sm text-gray-600 mb-6">{getServiceAreas()}</p>

				{/* Key service highlights for better targeting */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
					<div>
						📍 <strong>Mobile Service:</strong> We come to you
					</div>
					<div>
						🚨 <strong>Emergency Repairs:</strong> 7 days a week
					</div>
					<div>
						✅ <strong>Certified Techs:</strong> RVTAA certified
					</div>
				</div>
			</div>
		</div>
	);
}

export function generatePageSchema(
	city: string,
	state: string,
	description: string,
	coordinates: { latitude: number; longitude: number },
	rating: { ratingValue: number; reviewCount: number }
) {
	const schema: any = {
		"@context": "https://schema.org",
		"@type": "Service",
		name: `${city}/${state} mobile RV repair`,
		description: description,
		itemReviewed: {
			"@type": "Service",
			name: `Mobile RV Repair Services in ${city}, ${state}`,
			description: description
		},
		areaServed: {
			"@type": "City",
			name: city,
			geo: {
				"@type": "GeoCoordinates",
				latitude: coordinates.latitude,
				longitude: coordinates.longitude
			}
		},
		serviceType: "Mobile RV Repair",
		openingHoursSpecification: "7 days a week"
	};

	// Only add aggregateRating if we have valid reviews
	if (rating.reviewCount > 0 && rating.ratingValue > 0) {
		schema.aggregateRating = {
			"@type": "AggregateRating",
			ratingValue: rating.ratingValue,
			reviewCount: rating.reviewCount,
			bestRating: 5,
			worstRating: 1
		};
	}
	return JSON.stringify(schema, null, 2);
}

export default function RVMobileRepairCityClient({
	params,
	cityData,
	cityContent
}: {
	params: { state: string; city: string };
	cityData: City | null;
	cityContent?: any;
}) {
	const [providerCount, setProviderCount] = useState<number | undefined>();
	const [userLocation, setUserLocation] = useState<{
		lat: number;
		lng: number;
	} | null>(null);

	const cityName = decodeURIComponent(params.city)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");

	const stateName = decodeURIComponent(params.state)
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");

	// GPS-based personalization enhancement
	useEffect(() => {
		if (!navigator.geolocation) return;

		// Request user location for personalized results ordering
		const requestLocation = () => {
			navigator.geolocation.getCurrentPosition(
				(position) => {
					setUserLocation({
						lat: position.coords.latitude,
						lng: position.coords.longitude
					});
				},
				(error) => {
					// Silently fail - we'll fall back to city center
					console.error("Geolocation not available or denied");
				},
				{
					timeout: 5000,
					enableHighAccuracy: false
				}
			);
		};

		// Only request location once per session
		if (!sessionStorage.getItem("location-requested")) {
			sessionStorage.setItem("location-requested", "true");
			requestLocation();
		}
	}, []);

	// Fetch provider count for static content
	useEffect(() => {
		const fetchProviderCount = async () => {
			try {
				const params = new URLSearchParams({
					category: "rv-repair",
					page: "1",
					limit: "1",
					radius: "100"
				});

				// Use coordinates if available from cityData, otherwise fall back to city/state
				if (cityData?.lat && cityData?.lng) {
					console.log(
						`[RV Repair Client] Using coordinates for provider count: lat=${cityData.lat}, lng=${cityData.lng}`
					);
					params.set("lat", cityData.lat);
					params.set("lng", cityData.lng);
				} else {
					console.log(
						`[RV Repair Client] Using city/state for provider count: ${cityName}, ${stateName}`
					);
					params.set("city", cityName);
					params.set("state", stateName);
				}

				console.log(
					`[RV Repair Client] Provider count API params:`,
					params.toString()
				);
				const response = await fetch(`/api/listings/search?${params}`);
				if (response.ok) {
					const data = await response.json();
					console.log(`[RV Repair Client] Provider count result:`, data.total);
					setProviderCount(data.total || 0);
				}
			} catch (error) {
				console.error("Error fetching provider count:", error);
			}
		};

		fetchProviderCount();
	}, [cityName, stateName, cityData]);

	return (
		<>
			<EnhancedSEOContent
				city={cityName}
				state={stateName}
				providerCount={providerCount}
				params={params}
				cityContent={cityContent}
			/>

			{/* Existing Directory Page with all features but without hero section */}
			<DirectoryCityPage
				categoryType="rv-repair"
				params={params}
				title={`Mobile RV Repair in ${cityName}`}
				description="Our certified technicians provide mobile RV repair service"
				generatePageSchema={generatePageSchema}
				heroImage="/images/rv-repair-hero.webp"
				cityData={cityData}
				initialFilters={{
					sortBy: "default",
					offersVirtualConsultation: false,
					verified: undefined,
					radius: "100",
					category: "rv-repair",
					subcategory: ""
				}}
				subcategories={categories["rv-repair"].subcategories}
				faqItems={[
					{
						question: "How does mobile RV repair work?",
						answer:
							"Mobile RV repair technicians come directly to your location - whether that's your home, campground, or anywhere else. They bring their tools and expertise to diagnose and repair issues on-site."
					},
					{
						question: `What areas around ${cityName} do your mobile RV techs cover?`,
						answer: `We cover all major areas around ${cityName}, including suburbs and surrounding cities. Our technicians are mobile and have various service areas. Please check the listing for specific coverage details.`
					},
					{
						question: `How quickly can I get emergency RV repairs in ${cityName}?`,
						answer:
							"Many mobile RV technicians offer emergency services and can often respond within 24-48 hours. Some offer same-day service depending on availability and your location but most work is typically scheduled within a few days."
					},
					{
						question:
							"What types of RV repairs can a mobile rv mechanic perform?",
						answer:
							"Mobile RV technicians can handle most repairs including electrical systems, plumbing, appliances, basic engine repairs, slide-outs, leveling systems, and more. While some complex repairs may require a shop visit, mobile technicians can often diagnose and repair the majority of issues on-site."
					},
					{
						question:
							"Can mobile technicians repair both motorhomes and campers?",
						answer:
							"Yes, certified mobile RV technicians are trained to work on all types of recreational vehicles including motorhomes, travel trailers, fifth wheels, and pop-up campers."
					},
					{
						question:
							"What are the advantages of mobile RV repair vs. taking my camper to an RV repair shop?",
						answer:
							"Mobile RV repair offers convenience with no need to move your RV, vastly faster service times than shops, and the ability to get repairs done right at your campsite. This can save tons of time and avoid costly towing fees."
					},
					{
						question:
							"What qualifications should I look for in an RV repair technician?",
						answer:
							"Look for technicians certified by the RVTAA (RV Technicians Association of America) or another reputable organization. RV Help verifies all technicians' certifications and displays their certification level: Registered, Certified, or Master Certified on their profile so you know you're getting a qualified technician."
					},
					{
						question: "How much does mobile RV repair typically cost?",
						answer:
							"Costs vary based on the type of repair needed and your location. Mobile technicians typically charge a service call fee plus hourly labor rates.."
					},
					{
						question:
							"Will my RV warranty cover work done by mobile RV repair technicians?",
						answer: `Yes, repairs by certified mobile technicians are often covered by RV warranties. Many of our ${cityName} RVTAA certified technicians maintain the necessary credentials to perform warranty work for certain manufacturers but it's always best to check with your warranty provider.`
					}
				]}
			/>

			{/* GPS enhancement script for personalization */}
			{userLocation && (
				<script
					dangerouslySetInnerHTML={{
						__html: `
							// Personalize search results based on user's actual location
							// This runs after page load and only affects client-side ordering
							window.userLocation = ${JSON.stringify(userLocation)};
						`
					}}
				/>
			)}
		</>
	);
}
