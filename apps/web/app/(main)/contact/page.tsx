"use client";

import { EmailProtected } from "@/components/EmailProtected";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/hooks/useAuth";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle, Mail, MapPin } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { z } from "zod";

const contactFormSchema = z.object({
	name: z.string().min(1, "Name is required"),
	email: z.string().min(1, "Email address is required").email("Invalid email address"),
	phone: z
		.string()
		.min(1, "Phone number is required")
		.regex(
			/^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)]{10,17}$/,
			"Please enter a valid phone number"
		)
		.refine(
			(val) => {
				// Remove all non-digit characters to check length
				const digitsOnly = val.replace(/\D/g, "");
				return digitsOnly.length >= 10 && digitsOnly.length <= 15;
			},
			{
				message: "Phone number must contain 10-15 digits"
			}
		),
	subject: z.string().min(1, "Subject is required"),
	message: z.string().min(1, "Message is required")
});

type ContactFormData = z.infer<typeof contactFormSchema>;

export default function ContactPage() {
	const { user } = useAuth();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);

	const form = useForm<ContactFormData>({
		resolver: zodResolver(contactFormSchema),
		defaultValues: {
			name: user ? `${user.first_name} ${user.last_name}` : "",
			email: user?.email || "",
			phone: user?.phone || "",
			subject: "",
			message: ""
		}
	});

	const onSubmit = async (data: ContactFormData) => {
		setIsSubmitting(true);
		try {
			const response = await fetch("/api/support/contact", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(data)
			});

			if (!response.ok) throw new Error();

			setIsSubmitted(true);
			toast.success("Message sent successfully!");
			form.reset();
		} catch (error) {
			toast.error("Failed to send message. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="container max-w-6xl py-12">
			<div className="grid gap-8 md:grid-cols-2">
				<div>
					<h1 className="text-4xl font-bold mb-6">Contact Us</h1>
					<p className="text-gray-600 mb-8">
						Have questions? We'd love to hear from you. Send us a message and
						we'll respond as soon as possible.
					</p>

					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<Mail className="h-5 w-5 text-primary" />
							<div>
								<h3 className="font-medium">Email</h3>
								<p className="text-gray-600">
									<EmailProtected />
								</p>
							</div>
						</div>

						<div className="flex items-center gap-3">
							<MapPin className="h-5 w-5 text-primary" />
							<div>
								<h3 className="font-medium">Mailing Address</h3>
								<p className="text-gray-600">
									6635 S Dayton St Ste 310 #431
									<br />
									Greenwood Village, CO 80111
									<br />
									United States
								</p>
							</div>
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg shadow-sm border">
					{isSubmitted ? (
						<div className="flex flex-col items-center gap-4 py-6">
							<div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
								<CheckCircle className="h-6 w-6 text-green-600" />
							</div>
							<div className="text-center space-y-2">
								<h3 className="font-medium text-gray-900">
									Message Sent Successfully!
								</h3>
								<p className="text-sm text-gray-500">
									Thank you for contacting us. We'll respond to your email
									within a few hours during normal business hours (M-F 9am-5pm
									EST).
								</p>
							</div>
						</div>
					) : (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-6"
							>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input
													{...field}
													disabled={isSubmitting}
													label={"Name"}
													required={true}
													onChange={(e) => {
														field.onChange(e);
														// Trigger validation for the name field to clear errors immediately
														form.trigger("name");
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input
													label="Email"
													required={true}
													type="email"
													{...field}
													disabled={isSubmitting}
													onChange={(e) => {
														field.onChange(e);
														// Trigger validation for the email field to clear errors immediately
														form.trigger("email");
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input
													type="tel"
													{...field}
													disabled={isSubmitting}
													label="Phone"
													required={true}
													onChange={(e) => {
														field.onChange(e);
														// Trigger validation for the phone field to clear errors immediately
														form.trigger("phone");
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="subject"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Input
													{...field}
													disabled={isSubmitting}
													label="Subject"
													required={true}
													onChange={(e) => {
														field.onChange(e);
														// Trigger validation for the subject field to clear errors immediately
														form.trigger("subject");
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="message"
									render={({ field }) => (
										<FormItem>
											<FormControl>
												<Textarea
													rows={5}
													{...field}
													disabled={isSubmitting}
													label="Message"
													required={true}
													onChange={(e) => {
														field.onChange(e);
														// Trigger validation for the message field to clear errors immediately
														form.trigger("message");
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<Button
									type="submit"
									className="w-full"
									disabled={isSubmitting || !form.formState.isValid}
								>
									{isSubmitting ? "Sending..." : "Send Message"}
								</Button>
							</form>
						</Form>
					)}
				</div>
			</div>
		</div>
	);
}
