import AdminEditContentButton from "@/components/AdminEditContentButton";
import { Button } from "@/components/ui/button";
import prisma from "@/lib/prisma";
import { ArrowLeft } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";

async function getJobPosting(slug: string) {
	const job = await prisma.article.findUnique({
		where: {
			slug,
			type: "job-posting",
			status: "published"
		}
	});
	return job;
}

export async function generateMetadata({
	params
}: {
	params: { slug: string };
}): Promise<Metadata> {
	const job = await getJobPosting(params.slug);

	if (!job) {
		return {
			title: "Job Not Found - RV Help Careers",
			description: "This job posting could not be found"
		};
	}

	return {
		title: `${job.title} - RV Help Careers`,
		description: job.description
	};
}

export default async function JobDetailPage({
	params
}: {
	params: { slug: string };
}) {
	const job = await getJobPosting(params.slug);

	if (!job) {
		return notFound();
	}

	return (
		<div className="min-h-screen bg-gray-50">
			<AdminEditContentButton contentId={job.id} />
			<header className="bg-[#43806c] text-white py-20 px-4">
				<div className="container mx-auto">
					<div className="max-w-4xl mx-auto">
						<Link href="/careers">
							<Button variant="ghost" className="text-white mb-4">
								<ArrowLeft className="mr-2 h-4 w-4" />
								Back to Careers
							</Button>
						</Link>
						<h1 className="text-4xl font-bold mb-4">{job.title}</h1>
					</div>
				</div>
			</header>

			<main className="container mx-auto py-16 px-4">
				<div className="max-w-4xl mx-auto prose prose-lg">
					<div className="bg-white rounded-xl p-8 shadow-sm">
						<div dangerouslySetInnerHTML={{ __html: job.content }} />
					</div>
				</div>
			</main>
		</div>
	);
}
