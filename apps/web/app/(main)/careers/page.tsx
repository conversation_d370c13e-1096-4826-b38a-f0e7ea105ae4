import { Card, CardContent } from "@/components/ui/card";
import prisma from "@/lib/prisma";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const dynamic = "force-dynamic";

export const metadata: Metadata = {
	title: "Careers at RV Help",
	description:
		"Join our growing team and help revolutionize the RV service industry"
};

async function getJobPostings() {
	const jobs = await prisma.article.findMany({
		where: {
			type: "job-posting",
			status: "published"
		},
		orderBy: {
			published_at: "desc"
		},
		select: {
			title: true,
			slug: true,
			description: true,
			content: true,
			published_at: true
		}
	});
	return jobs;
}

export default async function CareersPage() {
	const jobs = await getJobPostings();

	return (
		<div className="min-h-screen bg-gray-50">
			<header className="bg-[#43806c] text-white py-20 px-4">
				<div className="container mx-auto text-center">
					<h1 className="text-4xl font-bold mb-4">Join Our Growing Team</h1>
					<p className="text-xl max-w-2xl mx-auto">
						Help us revolutionize the RV service industry by connecting RV
						owners with vetted technicians and inspectors.
					</p>
				</div>
			</header>

			<main className="container mx-auto py-16 px-4">
				<div className="max-w-4xl mx-auto">
					<h2 className="text-2xl font-bold mb-8">Open Positions</h2>

					<div className="space-y-4">
						{jobs.map((job) => (
							<Link
								className="block"
								key={job.slug}
								href={`/careers/${job.slug}`}
							>
								<Card className="hover:shadow-lg transition-shadow">
									<CardContent className="flex items-center justify-between p-6">
										<div>
											<h3 className="text-xl font-semibold mb-2">
												{job.title}
											</h3>
											<div className="text-gray-600">{job.description}</div>
										</div>
										<ChevronRight className="text-gray-400" />
									</CardContent>
								</Card>
							</Link>
						))}
					</div>
				</div>
			</main>
		</div>
	);
}
