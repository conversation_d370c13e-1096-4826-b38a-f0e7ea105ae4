"use client";

import { CombinedSearch } from "@/components/CombinedSearch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Award,
	Clock,
	Globe,
	MessageSquare,
	Shield,
	Wrench
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const AboutPage = () => {
	const router = useRouter();

	return (
		<div className="min-h-screen bg-white">
			{/* Hero Section */}
			<header className="relative text-white py-32 px-4">
				<div className="absolute inset-0">
					<Image
						src="/images/myrvresource.webp"
						alt="RV in scenic landscape"
						layout="fill"
						objectFit="cover"
						quality={100}
					/>
					<div className="absolute inset-0 bg-gradient-to-r from-[#43806c]/90 to-[#2c5446]/90"></div>
				</div>
				<div className="container mx-auto relative z-10">
					<h1 className="text-4xl md:text-5xl font-bold mb-4 text-center">
						Transforming RV Service & Support
					</h1>
					<p className="text-xl text-center">
						Connecting RV owners with certified mobile technicians and
						inspectors across North America
					</p>
				</div>
			</header>

			{/* Mission & Team Section */}
			<section className="py-16 bg-white">
				<div className="container mx-auto px-4">
					<div className="max-w-3xl mx-auto text-center mb-16">
						<h2 className="text-3xl font-bold mb-8 text-[#43806c]">
							Our Mission
						</h2>
						<p className="text-lg text-gray-600 mb-6">
							At RV Help, we're revolutionizing how RV owners access essential
							services. Founded by experienced RV industry entrepreneurs Heath
							Padgett, Dennis Lankes, and Josiah Mann, we understand the unique
							challenges of RV ownership and maintenance.
						</p>
						<p className="text-lg text-gray-600">
							Through our partnerships with RVTAA and NRVIA, we ensure all
							technicians and inspectors meet rigorous certification standards,
							bringing expert service directly to your location.
						</p>
					</div>

					{/* Team Photo */}
				</div>
			</section>
			<section className="bg-gray-50 rounded-xl p-8 shadow-sm">
				<div className="container">
					<div className="flex flex-col md:flex-row items-center gap-8">
						<div className="md:w-1/2 w-full">
							<h3 className="text-2xl font-bold mb-4 text-[#43806c]">
								Meet Our Team
							</h3>
							<p className="text-gray-600 mb-4">
								Our founders bring together decades of experience in the RV
								industry, technology, and customer service. We're passionate
								about making RV service and support accessible to everyone.
							</p>
							<p className="text-gray-600">
								Left to right: Heath Padgett, Josiah Mann, and Dennis Lankes
							</p>
						</div>
						<div className="md:w-1/2 w-full">
							<div className="relative w-full aspect-square">
								<Image
									src="/images/team-photo.jpg"
									alt="RV Help Leadership Team"
									layout="fill"
									objectFit="cover"
									className="rounded-lg"
								/>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Key Features */}
			<section className="py-16 bg-gray-50">
				<div className="container mx-auto px-4">
					<h2 className="text-3xl font-bold mb-12 text-center text-[#43806c]">
						What Sets Us Apart
					</h2>
					<div className="grid md:grid-cols-3 gap-8">
						{[
							{
								icon: Shield,
								title: "Certified Professionals",
								description:
									"All technicians' certifications are verified through RVTAA and NRVIA"
							},
							{
								icon: Globe,
								title: "Nationwide Coverage",
								description:
									"Access to over 1,500 mobile service providers across North America"
							},
							{
								icon: Clock,
								title: "Fast Response Times",
								description:
									"Get expert service at your location within days, not months"
							}
						].map((feature, index) => (
							<Card key={index}>
								<CardHeader>
									<CardTitle className="flex items-center text-xl text-[#43806c]">
										<feature.icon className="mr-2" /> {feature.title}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<p className="text-gray-600">{feature.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-12 bg-[#43806c]">
				<div className="container mx-auto px-4 text-center">
					<h2 className="text-3xl font-bold mb-4 text-white">
						Ready to Find RV Service?
					</h2>
					<p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
						Connect with trusted RV service professionals today
					</p>
					<div className="max-w-md mx-auto">
						<CombinedSearch
							placeholder="Enter your location to get started"
							onSelect={(suggestion) => {
								const [lng, lat] = suggestion.center || [];
								const params = new URLSearchParams({
									category: 'rv-repair',
									location: suggestion.place_name
								});

								if (lat && lng) {
									params.set('lat', lat.toString());
									params.set('lng', lng.toString());
								}

								router.push(`/search?${params.toString()}`);
							}}
						/>
					</div>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-16 bg-white">
				<div className="container mx-auto px-4">
					<h2 className="text-3xl font-bold mb-12 text-center text-[#43806c]">
						Our Services
					</h2>
					<div className="grid md:grid-cols-3 gap-8">
						{[
							{
								icon: Wrench,
								title: "Mobile RV Repairs",
								description:
									"Professional repairs and maintenance at your campsite"
							},
							{
								icon: Award,
								title: "Pre-Purchase Inspections",
								description:
									"Comprehensive inspections from certified professionals"
							},
							{
								icon: MessageSquare,
								title: "Direct Communication",
								description:
									"Easy messaging with certified technicians through our platform"
							}
						].map((service, index) => (
							<Card key={index}>
								<CardHeader>
									<CardTitle className="flex items-center text-xl text-[#43806c]">
										<service.icon className="mr-2" /> {service.title}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<p className="text-gray-600">{service.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>
		</div>
	);
};

export default AboutPage;
