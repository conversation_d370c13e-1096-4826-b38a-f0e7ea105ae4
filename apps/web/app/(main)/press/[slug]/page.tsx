import AdminEditContentButton from "@/components/AdminEditContentButton";
import PageHeader from "@/components/PageHeader";
import prisma from "@/lib/prisma";
import { Article } from "@rvhelp/database";
import { notFound } from "next/navigation";

async function getArticleContent(slug: string): Promise<Article | null> {
	const article = await prisma.article.findUnique({
		where: { slug, type: "press-release" }
	});

	return article;
}

export default async function PressReleasePage({
	params
}: {
	params: { slug: string };
}) {
	const article = await getArticleContent(params.slug);

	if (!article) {
		return notFound();
	}

	return (
		<div>
			<AdminEditContentButton contentId={article.id} />
			<PageHeader
				title={article.title}
				description={article.description || ""}
				breadCrumbsItems={[
					{ label: "Press Releases", href: "/press" },
					{ label: article.title, href: `/press/${article.slug}` }
				]}
			/>

			<div className="py-6">
				<article className="prose prose-lg max-w-3xl mx-auto prose-pre:bg-[#282c34] prose-pre:p-4 prose-pre:rounded-lg">
					<p>
						{article.published_at?.toLocaleDateString("en-US", {
							year: "numeric",
							month: "long",
							day: "numeric"
						})}
					</p>
					<div dangerouslySetInnerHTML={{ __html: article.content }}></div>
				</article>
			</div>
		</div>
	);
}
