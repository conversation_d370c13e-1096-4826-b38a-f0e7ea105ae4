"use client";

import { Badge } from "@/components/ui/badge";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { getCategoryName } from "@/lib/categories";
import { formatDistanceToNow } from "date-fns";
import { AlertCircle, Clock, MessageSquare, Plus, Users } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

export default function ServiceRequestsPage() {
	const [isLoading, setIsLoading] = useState(true);
	const [jobs, setJobs] = useState([]);
	const router = useRouter();

	const fetchServiceRequests = async () => {
		try {
			const response = await fetch(`/api/jobs`);
			if (!response.ok) {
				throw new Error("Failed to fetch jobs");
			}
			const data = await response.json();
			setJobs(data);
		} catch (error) {
			console.error("Error fetching jobs:", error);
			toast.error("Failed to load jobs details");
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchServiceRequests();
	}, []);

	if (isLoading) {
		return (
			<div className="min-h-screen bg-muted/30">
				<div className="container mx-auto px-4 py-8 space-y-8">
					{/* Header Section Skeleton */}
					<div className="space-y-6">
						<div className="flex items-center gap-2">
							<Skeleton className="h-4 w-16" />
							<span className="text-muted-foreground">/</span>
							<Skeleton className="h-4 w-24" />
						</div>

						<div className="space-y-2">
							<Skeleton className="h-8 w-48" />
							<Skeleton className="h-4 w-80" />
						</div>
					</div>

					{/* Stats Overview Skeleton */}
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						{[...Array(4)].map((_, i) => (
							<Card key={i}>
								<CardContent className="p-6">
									<div className="flex items-center space-x-2 mb-2">
										<Skeleton className="h-2 w-2 rounded-full" />
										<Skeleton className="h-4 w-16" />
									</div>
									<Skeleton className="h-8 w-8" />
								</CardContent>
							</Card>
						))}
					</div>

					{/* Service Requests List Skeleton */}
					<div className="space-y-4">
						{[...Array(3)].map((_, i) => (
							<Card key={i}>
								<CardHeader className="pb-4">
									<div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
										<div className="flex flex-wrap items-center gap-2">
											<Skeleton className="h-6 w-16" />
											<Skeleton className="h-6 w-20" />
										</div>
									</div>
								</CardHeader>

								<CardContent className="space-y-6">
									<div>
										<Skeleton className="h-5 w-32 mb-2" />
										<div className="space-y-2">
											<Skeleton className="h-4 w-full" />
											<Skeleton className="h-4 w-3/4" />
											<Skeleton className="h-4 w-1/2" />
										</div>
									</div>

									<div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
										{[...Array(3)].map((_, j) => (
											<div key={j} className="flex items-center gap-3">
												<Skeleton className="h-10 w-10 rounded-lg" />
												<div className="space-y-1">
													<Skeleton className="h-3 w-12" />
													<Skeleton className="h-4 w-16" />
													<Skeleton className="h-3 w-20" />
												</div>
											</div>
										))}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</div>
		);
	}

	const getStatusVariant = (status) => {
		switch (status) {
			case "OPEN":
				return "default";
			case "IN_PROGRESS":
				return "secondary";
			case "COMPLETED":
				return "outline";
			case "CANCELLED":
				return "destructive";
			default:
				return "outline";
		}
	};

	const getActionRequired = (job) => {
		if (job.status === "OPEN" && (!job.quotes || job.quotes.length === 0)) {
			return {
				required: true,
				message: "Invite service providers"
			};
		}
		if (job.quotes?.some((quote) => quote.unread_messages_count > 0)) {
			return {
				required: true,
				message: "New messages from providers"
			};
		}
		return {
			required: false,
			message: null
		};
	};

	return (
		<div className="min-h-screen bg-muted/30">
			{/* Header Section */}

			<Breadcrumbs
				items={[
					{ label: "Dashboard", href: "/dashboard" },
					{ label: "Service Requests", href: "/service-requests" }
				]}
				className="mt-4 mb-4"
			/>
			<div className="space-y-8">
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
					<div className="space-y-2">
						<h1 className="text-3xl font-bold tracking-tight">
							Service Requests
						</h1>
						<p className="text-muted-foreground">
							Track and manage your RV service and repair requests
						</p>
					</div>
				</div>

				{/* Stats Overview */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center space-x-2">
								<div className="h-2 w-2 bg-blue-500 rounded-full"></div>
								<span className="text-sm font-medium">Open</span>
							</div>
							<p className="text-2xl font-bold mt-2">
								{jobs.filter((job) => job.status === "OPEN").length}
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center space-x-2">
								<div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
								<span className="text-sm font-medium">In Progress</span>
							</div>
							<p className="text-2xl font-bold mt-2">
								{jobs.filter((job) => job.status === "IN_PROGRESS").length}
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center space-x-2">
								<div className="h-2 w-2 bg-green-500 rounded-full"></div>
								<span className="text-sm font-medium">Completed</span>
							</div>
							<p className="text-2xl font-bold mt-2">
								{jobs.filter((job) => job.status === "COMPLETED").length}
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center space-x-2">
								<div className="h-2 w-2 bg-primary rounded-full"></div>
								<span className="text-sm font-medium">Total</span>
							</div>
							<p className="text-2xl font-bold mt-2">{jobs.length}</p>
						</CardContent>
					</Card>
				</div>

				{/* Service Requests List */}
				<div className="space-y-4">
					{jobs.map((job) => {
						const actionRequired = getActionRequired(job);
						const providersInvited = job.quotes?.length || 0;
						const providersResponded =
							job.quotes?.filter(
								(q) => !["PENDING", "EXPIRED"].includes(q.status)
							).length || 0;
						const hasUnreadMessages = job.quotes?.some(
							(quote) => quote.unread_messages_count > 0
						);

						return (
							<Link
								href={`/service-requests/${job.id}`}
								className="block transition-all duration-200 hover:scale-[1.02]"
								key={job.id}
							>
								<Card className="hover:shadow-md transition-shadow duration-200">
									<CardHeader className="pb-4">
										<div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
											<div className="flex flex-wrap items-center gap-2">
												<Badge variant={getStatusVariant(job.status)}>
													{job.status.replace("_", " ")}
												</Badge>
												<Badge variant="outline">
													{getCategoryName(job.category)}
												</Badge>
												{actionRequired.required && (
													<Badge
														variant="destructive"
														className="animate-pulse"
													>
														<AlertCircle className="h-3 w-3 mr-1" />
														Action Required
													</Badge>
												)}
											</div>
										</div>
									</CardHeader>

									<CardContent className="space-y-6">
										<div>
											<h3 className="font-semibold text-lg mb-2">
												{`${job.rv_year || ""} ${job.rv_make || ""} ${job.rv_model || ""} ${getCategoryName(job.category)}`.trim()}
											</h3>
											<p className="text-muted-foreground leading-relaxed line-clamp-3">
												{job.message}
											</p>
										</div>

										{actionRequired.required && (
											<div className="flex items-center gap-3 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
												<AlertCircle className="h-5 w-5 text-destructive flex-shrink-0" />
												<span className="text-sm font-medium text-destructive">
													{actionRequired.message}
												</span>
											</div>
										)}

										<div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
											<div className="flex items-center gap-3">
												<div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
													<Users className="h-5 w-5 text-primary" />
												</div>
												<div>
													<p className="text-sm text-muted-foreground">
														Providers
													</p>
													<p className="font-semibold">
														{providersInvited} invited
													</p>
													<p className="text-sm text-muted-foreground">
														{providersResponded} responded
													</p>
												</div>
											</div>

											<div className="flex items-center gap-3">
												<div className="h-10 w-10 bg-secondary/10 rounded-lg flex items-center justify-center">
													<Clock className="h-5 w-5 text-secondary" />
												</div>
												<div>
													<p className="text-sm text-muted-foreground">
														Created
													</p>
													<p className="font-semibold">
														{formatDistanceToNow(new Date(job.created_at), {
															addSuffix: true
														})}
													</p>
												</div>
											</div>

											{hasUnreadMessages && (
												<div className="flex items-center gap-3">
													<div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
														<MessageSquare className="h-5 w-5 text-primary" />
													</div>
													<div>
														<p className="text-sm text-primary font-medium">
															New messages
														</p>
														<p className="text-sm text-muted-foreground">
															From providers
														</p>
													</div>
												</div>
											)}
										</div>
									</CardContent>
								</Card>
							</Link>
						);
					})}

					{jobs.length === 0 && (
						<Card>
							<CardContent className="p-12">
								<div className="text-center space-y-4">
									<div className="h-16 w-16 bg-muted rounded-full flex items-center justify-center mx-auto">
										<AlertCircle className="h-8 w-8 text-muted-foreground" />
									</div>
									<div>
										<h3 className="font-semibold text-lg">
											No service requests found
										</h3>
										<p className="text-muted-foreground">
											Get started by finding a service provider
										</p>
									</div>
									<Button
										className="bg-primary text-primary-foreground hover:bg-primary/90"
										onClick={() => router.push("/mobile-rv-repair")}
									>
										<Plus className="h-4 w-4 mr-2" />
										Find a service provider
									</Button>
								</div>
							</CardContent>
						</Card>
					)}
				</div>
			</div>
		</div>
	);
}
