"use client";

import PricingModal from "@/components/membership/PricingModal";
import { useAuth } from "@/lib/hooks/useAuth";
import { ArrowR<PERSON>, Rocket } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

const ProMembership = () => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const { isPaid } = useAuth();
	const router = useRouter();
	if (isPaid) {
		return null;
	}

	return (
		<div className="bg-white border border-gray-200 rounded-xl p-6 mb-10 shadow-sm">
			<div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
				<div className="flex-1">
					<div className="inline-block bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-semibold mb-3">
						<Rocket className="inline-block w-4 h-4 mr-1" />
						Now Available
					</div>
					<h2 className="text-2xl font-bold text-gray-800 mb-2">
						Join RV Help Pro as a Founding Member
					</h2>
					<p className="text-gray-600">
						Get expert help, exclusive discounts, and priority support.
					</p>
				</div>

				<div className="flex flex-col sm:flex-row gap-3">
					<button
						onClick={() => router.push("/pro-membership")}
						className="bg-secondary hover:bg-secondary-dark text-white px-6 py-3 rounded-lg font-semibold transition-colors whitespace-nowrap"
					>
						View Plans & Pricing
					</button>
					<Link
						href="/pro-membership"
						className="inline-flex items-center justify-center gap-2 text-indigo-600 hover:text-indigo-700 font-semibold transition-colors px-6 py-3"
					>
						Learn More <ArrowRight className="w-4 h-4" />
					</Link>
				</div>
			</div>

			<PricingModal
				isOpen={isModalOpen}
				onClose={() => setIsModalOpen(false)}
			/>
		</div>
	);
};

export default ProMembership;
