"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Article } from "@rvhelp/database";
import { Pin } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export function AnnouncementsSection() {
	const [announcements, setAnnouncements] = useState<Article[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const fetchAnnouncements = async () => {
			try {
				const res = await fetch(
					"/api/articles?type=provider-announcement,provider-training,provider-office-hours"
				);
				if (res.ok) {
					const data = await res.json();
					// Sort pinned articles to the top, then by published date
					const sortedAnnouncements = data.sort((a: Article, b: Article) => {
						if (a.pinned && !b.pinned) return -1;
						if (!a.pinned && b.pinned) return 1;
						if (a.published_at && b.published_at) {
							return (
								new Date(b.published_at).getTime() -
								new Date(a.published_at).getTime()
							);
						}
						return 0;
					});
					setAnnouncements(sortedAnnouncements);
				}
			} catch (error) {
				console.error("Error fetching announcements:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchAnnouncements();
	}, []);

	if (isLoading) {
		return (
			<div className="animate-pulse">
				<div className="h-4 w-24 bg-gray-200 rounded mb-4" />
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="h-20 bg-gray-200 rounded" />
					<div className="h-20 bg-gray-200 rounded" />
					<div className="h-20 bg-gray-200 rounded" />
				</div>
			</div>
		);
	}

	if (announcements.length === 0) {
		return (
			<div className="text-center py-4">
				<p className="text-sm text-gray-500">No announcements found.</p>
			</div>
		);
	}

	return (
		<div className="mb-8 bg-white rounded-xl border border-gray-200 overflow-hidden">
			<div className="p-4 border-b border-gray-100 flex items-center justify-between">
				<h2 className="text-lg font-medium text-gray-900">
					Announcements & Updates
				</h2>
				<Button variant="outline" size="sm" asChild>
					<Link href="/provider/announcements">View all</Link>
				</Button>
			</div>
			<div className="p-4">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					{announcements.slice(0, 3).map((announcement) => {
						const isOfficeHours = announcement.type === "provider-office-hours";
						const isTraining = announcement.type === "provider-training";

						return (
							<Link
								key={announcement.id}
								href={`/provider/announcements/${announcement.slug}`}
								className={cn(
									"block rounded-lg border p-4 transition-colors",
									isOfficeHours
										? "border-primary/20 bg-primary/5 hover:bg-primary/10"
										: isTraining
											? "border-blue-500/20 bg-blue-500/5 hover:bg-blue-500/10"
											: "border-primary/20 bg-primary/5 hover:bg-primary/10",
									announcement.pinned && "border-2 border-primary shadow-sm"
								)}
							>
								<div className="flex items-center justify-between mb-2">
									<div className="flex items-center gap-2">
										{announcement.pinned && (
											<Badge
												variant="default"
												className="bg-primary flex items-center gap-1"
											>
												<Pin className="w-3 h-3" />
												Pinned
											</Badge>
										)}
										<Badge
											variant="outline"
											className={cn(
												"text-xs",
												isOfficeHours
													? "bg-primary/10 text-primary border-primary/20"
													: isTraining
														? "bg-blue-500/10 text-blue-500 border-blue-500/20"
														: "bg-primary/10 text-primary border-primary/20"
											)}
										>
											{isOfficeHours
												? "Office Hours"
												: isTraining
													? "Training"
													: "Announcement"}
										</Badge>
									</div>
									<span className="text-xs text-gray-500">
										{announcement.published_at
											? new Date(announcement.published_at).toLocaleDateString()
											: "Unpublished"}
									</span>
								</div>

								<h3
									className={cn(
										"text-sm font-medium text-gray-900 line-clamp-1",
										announcement.pinned && "text-primary"
									)}
								>
									{announcement.title}
								</h3>

								{announcement.description && (
									<p className="text-xs text-gray-600 line-clamp-1 mt-1">
										{announcement.description}
									</p>
								)}
							</Link>
						);
					})}
				</div>
			</div>
		</div>
	);
}
