import { EmergencyDispatchModal } from "@/components/modals/EmergencyDispatchModal";
import { SupportModal } from "@/components/modals/SupportModal";
import UpgradeModal from "@/components/modals/UpgradeModal";
import VirtualDiagnosticModal from "@/components/modals/VirtualDiagnosticModal";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { ChevronRight, Crown, Lock, Search, Wrench } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function HelpfulResources() {
	const { isPaid, isStandard, isPremium } = useAuth();

	const [supportOpen, setSupportOpen] = useState(false);
	const [virtualDiagnosticOpen, setVirtualDiagnosticOpen] = useState(false);
	const [showUpgradeModal, setShowUpgradeModal] = useState(false);
	const [emergencyDispatchOpen, setEmergencyDispatchOpen] = useState(false);

	if (isPaid) {
		return (
			<div
				className="bg-white border border-gray-200 rounded-2xl p-8 mb-10 shadow-sm"
				style={{ borderLeft: "4px solid #42806c" }}
			>
				<div className="flex items-center justify-between mb-8">
					<div>
						<h2 className="text-2xl font-semibold text-gray-800 mb-2">
							Pro Member Quick Actions
						</h2>
						<p className="text-gray-600">
							Get instant help with your RV issues using your Pro benefits
						</p>
					</div>
					<Link href="/dashboard/pro-tools">
						<Button variant="outline" className="text-[#42806c] border-[#42806c] hover:bg-[#42806c] hover:text-white">
							View All Benefits
							<ChevronRight className="w-4 h-4 ml-2" />
						</Button>
					</Link>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
					<Link
						href="/dashboard/virtual-troubleshooting"
						className="bg-gray-50 border border-gray-200 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
					>
						<div className="flex items-center gap-4 mb-4">
							<div
								className="w-12 h-12 rounded-xl flex items-center justify-center"
								style={{ background: "#e8f4f1" }}
							>
								<svg width="24" height="24" fill="#42806c" viewBox="0 0 24 24">
									<path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607zM13.5 10.5a3.5 3.5 0 11-7 0 3.5 3.5 0 017 0z" />
								</svg>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 mb-1">
									Find Nearby Techs Who Help with Troubleshooting
								</div>
							</div>
						</div>
						<div className="text-gray-600 text-sm leading-relaxed mb-4">
							Connect with local techs in your area who offer 10-minute
							troubleshooting calls to Pro Members.
						</div>
						<button
							className="w-full text-white border-none py-3 px-5 rounded-lg text-sm font-semibold cursor-pointer transition-colors duration-200 hover:opacity-90"
							style={{ background: "#42806c" }}
						>
							Find Local Techs
						</button>
					</Link>

					<div className="bg-gray-50 border border-gray-200 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
						<div className="flex items-center gap-4 mb-4">
							<div
								className="w-12 h-12 rounded-xl flex items-center justify-center"
								style={{ background: "#e8f4f1" }}
							>
								<svg width="24" height="24" fill="#42806c" viewBox="0 0 24 24">
									<path d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
								</svg>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 mb-1">
									Concierge Support
								</div>
							</div>
						</div>
						<div className="text-gray-600 text-sm leading-relaxed mb-4">
							Need help with something? Can&apos;t find a tech or inspector in
							your area? We&apos;re here to help.
						</div>
						<button
							onClick={() => setSupportOpen(true)}
							className="w-full text-white border-none py-3 px-5 rounded-lg text-sm font-semibold cursor-pointer transition-colors duration-200 hover:opacity-90"
							style={{
								background: "linear-gradient(135deg, #42806c 0%, #fea72a 100%)"
							}}
						>
							Contact Support
						</button>
					</div>

					{/* Book RV Help Certified Tech */}
					<div className="bg-gray-50 border border-gray-200 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
						<div className="flex items-center gap-4 mb-4">
							<div
								className="w-12 h-12 rounded-xl flex items-center justify-center"
								style={{ background: "#fef7e6" }}
							>
								<svg width="24" height="24" fill="#fea72a" viewBox="0 0 24 24">
									<path d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
								</svg>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 mb-1">
									Book Virtual Diagnostic Call
								</div>
								{/* premium badge */}
								<span className="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs font-medium flex-shrink-0">
									Premium Feature
								</span>
							</div>
						</div>

						<div className="text-gray-600 text-sm leading-relaxed mb-4">
							Schedule a video call with our on staff certified RV Help
							technicians to get instant help with your RV issues.
						</div>

						<button
							onClick={() =>
								isStandard
									? setShowUpgradeModal(true)
									: setVirtualDiagnosticOpen(true)
							}
							className={`w-full flex items-center justify-center gap-2 text-white border-none py-3 px-5 rounded-lg text-sm font-semibold cursor-pointer transition-colors duration-200 hover:opacity-90 ${isStandard ? "bg-amber-500" : "bg-blue-500"}`}
							style={{ background: "#fea72a" }}
						>
							{isStandard ? <Lock className="w-4 h-4" /> : null}
							{isStandard ? "Upgrade to Premium" : "Book Video Call"}
						</button>
					</div>

					{/* Emergency Dispatch */}
					<div
						className="bg-red-50 border border-red-200 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
						onClick={() => setEmergencyDispatchOpen(true)}
					>
						<div className="flex items-center gap-4 mb-4">
							<div
								className="w-12 h-12 rounded-xl flex items-center justify-center"
								style={{ background: "#fef2f2" }}
							>
								<svg width="24" height="24" fill="#dc2626" viewBox="0 0 24 24">
									<path
										fillRule="evenodd"
										d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
										clipRule="evenodd"
									/>
								</svg>
							</div>
							<div>
								<div className="text-lg font-semibold text-gray-800 mb-1">
									Emergency Dispatch
								</div>
							</div>
						</div>
						<div className="text-gray-600 text-sm leading-relaxed mb-4">
							Critical RV house system failure? Broadcast urgent alerts to all
							available techs in your area. (Service fees apply)
						</div>
						<button className="w-full text-white border-none py-3 px-5 rounded-lg text-sm font-semibold cursor-pointer transition-colors duration-200 hover:opacity-90 bg-red-600 hover:bg-red-700">
							Broadcast Emergency
						</button>
					</div>
				</div>

				<EmergencyDispatchModal
					open={emergencyDispatchOpen}
					onOpenChange={setEmergencyDispatchOpen}
				/>
				<SupportModal open={supportOpen} onOpenChange={setSupportOpen} />
				<VirtualDiagnosticModal
					open={virtualDiagnosticOpen}
					onOpenChange={setVirtualDiagnosticOpen}
				/>
				<UpgradeModal
					open={showUpgradeModal}
					onOpenChange={setShowUpgradeModal}
				/>
			</div>
		);
	}

	return (
		<div className="mb-8 bg-white rounded-xl border border-gray-200 overflow-hidden">
			<div className="p-6 border-b border-gray-100">
				<h2 className="text-lg font-medium text-gray-900">Helpful Resources</h2>
			</div>
			<div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
				<Link
					href="/search"
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-[#43806c]/10 p-3 rounded-lg">
							<Wrench className="w-5 h-5 text-[#43806c]" />
						</div>
						<div>
							<h4 className="font-medium text-gray-900">Find RV Tech</h4>
							<p className="text-sm text-gray-500">
								Get matched with qualified technicians
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Link>

				<Link
					href="/search?category=rv-inspection"
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-[#43806c]/10 p-3 rounded-lg">
							<Search className="w-5 h-5 text-[#43806c]" />
						</div>
						<div>
							<h4 className="font-medium text-gray-900">Find Inspector</h4>
							<p className="text-sm text-gray-500">
								Get your RV professionally inspected
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Link>

				<Link
					href="/pro-membership"
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-amber-500/10 p-3 rounded-lg">
							<Crown className="w-5 h-5 text-amber-500" />
						</div>
						<div className="text-left">
							<div className="flex items-center gap-2">
								<h4 className="font-medium text-gray-900">
									Virtual Troubleshooting
								</h4>
								<span className="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs font-medium">
									Pro Feature
								</span>
							</div>
							<p className="text-sm text-gray-500">
								Get quick help from local techs with troubleshooting calls
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Link>

				<Button
					onClick={() => setSupportOpen(true)}
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-amber-500/10 p-3 rounded-lg">
							<Crown className="w-5 h-5 text-amber-500" />
						</div>
						<div className="text-left">
							<div className="flex items-center gap-2">
								<h4 className="font-medium text-gray-900">Concierge Support</h4>
								<span className="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs font-medium">
									Pro Feature
								</span>
							</div>
							<p className="text-sm text-gray-500">
								Get personalized help finding the right solution
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Button>
			</div>

			<SupportModal open={supportOpen} onOpenChange={setSupportOpen} />
			<VirtualDiagnosticModal
				open={virtualDiagnosticOpen}
				onOpenChange={setVirtualDiagnosticOpen}
			/>
		</div>
	);
}
