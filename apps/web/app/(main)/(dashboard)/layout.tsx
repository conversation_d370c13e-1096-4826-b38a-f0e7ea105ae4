"use client";

import { useAuth } from "@/lib/hooks/useAuth";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import {
	ChevronRight,
	Crown,
	Heart,
	LayoutDashboard,
	ListChecks,
	User,
	Wrench
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useMobileWebView } from "../../../hooks/useMobileWebView";

type Subscription = {
	status: "active" | "canceled" | "none";
	plan: "free" | "pro";
	currentPeriodEnd?: string;
};

function UserLayout({ children }: { children: React.ReactNode }) {
	const pathname = usePathname();
	const router = useRouter();
	const { isPaid, loading } = useAuth();
	const { isMobileWebView } = useMobileWebView();
	const sections = [
		{
			label: "Dashboard",
			icon: LayoutDashboard,
			description: "Dashboard",
			href: "/dashboard"
		},
		...(!isPaid
			? [
				{
					label: "Pro Membership",
					icon: Crown,
					description: "Upgrade to Pro",
					href: "/pro-membership"
				}
			]
			: [
				{
					label: "Pro Tools",
					icon: Crown,
					description: "Member Benefits",
					href: "/dashboard/pro-tools"
				}
			]),

		{
			label: "Service Requests",
			icon: ListChecks,
			description: "Service Requests",
			href: "/service-requests"
		},
		{
			label: "Profile",
			icon: User,
			description: "Profile Settings",
			href: "/profile",
			subsections: [
				{
					label: "Profile Settings",
					href: "/profile"
				},
				{
					label: "RV Details",
					href: "/profile/rv-details"
				},
				// notifications
				{
					label: "Notifications",
					href: "/profile/notifications"
				},
				{
					label: "Security",
					href: "/profile/security"
				},
				...(isPaid
					? [
						{
							label: "Membership",
							href: "/profile/membership"
						}
					]
					: [])
			]
		},

		{
			label: "Favorites",
			icon: Heart,
			description: "Saved Listings",
			href: "/favorites"
		},
		{
			label: "Maintenance Tracker",
			icon: Wrench,
			description: "Track your RV maintenance",
			href: "/maintenance-tracker",
			comingSoon: true
		}
	];


	const isActiveSection = (section: any) => {
		if (section.subsections) {
			return section.subsections.some((sub: any) => pathname === sub.href);
		}
		return pathname === section.href || pathname.includes(section.href);
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
			</div>
		);
	}

	return (
		<div className={`flex md:flex-row min-h-screen bg-gray-50 relative ${isMobileWebView ? "ml-0" : "ml-16"}`}>
			{!isMobileWebView && (<>

				{/* Mobile Icon-only Sidebar */}
				<div className="md:hidden absolute left-0 top-0 w-16 h-full bg-white border-r border-gray-200 z-10">
					<div className="flex flex-col items-center py-4 space-y-2">
						{sections.map((section) => {
							const isActive = isActiveSection(section);
							return (
								<div key={section.label} className="relative group">
									<Link
										href={section.comingSoon ? "#" : section.href}
										className={cn(
											"flex items-center justify-center w-10 h-10 rounded-lg transition-colors",
											isActive
												? "bg-gray-100 text-gray-900"
												: section.label === "Pro Membership"
													? "bg-gradient-to-r from-yellow-400 to-amber-500 text-white hover:from-yellow-500 hover:to-amber-600"
													: "text-gray-600 hover:bg-gray-100"
										)}
										onClick={(e) => {
											if (section.comingSoon) {
												e.preventDefault();
												router.push("/maintenance-tracker/interest");
											}
										}}
										{...(section.label === "Pro Membership"
											? {
												target: "_blank",
												rel: "noopener noreferrer"
											}
											: {})}
									>
										<section.icon
											className={cn(
												"w-5 h-5",
												section.label === "Pro Membership" && "text-white"
											)}
										/>
									</Link>
									{/* Tooltip */}
									<div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 top-1/2 transform -translate-y-1/2">
										{section.label}
										{section.comingSoon && (
											<span className="ml-1 text-emerald-300">(Coming Soon)</span>
										)}
									</div>
								</div>
							);
						})}
					</div>
				</div>

				{/* Tablet Sidebar - Icons + Labels */}
				<div className="hidden md:block lg:hidden w-48 bg-white border-r border-gray-200 p-4">
					<div className="space-y-2">
						{sections.map((section) => {
							const isActive = isActiveSection(section);
							return (
								<Link
									key={section.label}
									href={section.comingSoon ? "#" : section.href}
									className={cn(
										"flex items-center justify-between p-2 rounded-lg transition-colors",
										isActive
											? "bg-gray-100 text-gray-900"
											: section.label === "Pro Membership"
												? "bg-gradient-to-r from-yellow-400 to-amber-500 text-white hover:from-yellow-500 hover:to-amber-600"
												: "text-gray-600 hover:bg-gray-100"
									)}
									onClick={(e) => {
										if (section.comingSoon) {
											e.preventDefault();
											router.push("/maintenance-tracker/interest");
										}
									}}
									{...(section.label === "Pro Membership"
										? {
											target: "_blank",
											rel: "noopener noreferrer"
										}
										: {})}
								>
									<div className="flex items-center">
										<section.icon
											className={cn(
												"w-5 h-5 mr-2 flex-shrink-0",
												section.label === "Pro Membership"
													? "text-white"
													: "text-gray-500"
											)}
										/>
										<span className="font-medium text-sm">{section.label}</span>
									</div>
									{section.comingSoon && (
										<span className="inline-flex items-center justify-center text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded-full whitespace-nowrap">
											Soon
										</span>
									)}
								</Link>
							);
						})}
					</div>
				</div>

				{/* Desktop Sidebar - Full with Subsections */}
				<div className="hidden lg:block w-64 bg-white border-r border-gray-200 p-4">
					<div className="space-y-6">
						<div className="space-y-2">
							{sections.map((section) =>
								section.subsections ? (
									<details
										key={section.label}
										className="group"
										open={
											section.subsections.some((sub) => pathname === sub.href) ||
											pathname.includes(section.label.toLowerCase())
										}
									>
										<summary
											className="flex items-center justify-between p-2 rounded-lg cursor-pointer hover:bg-gray-100"
											onClick={(e) => {
												e.preventDefault();
												const details = e.currentTarget
													.parentElement as HTMLDetailsElement;
												details.open = !details.open;
											}}
										>
											<div className="flex items-center">
												<section.icon className="w-5 h-5 mr-2 text-gray-500 flex-shrink-0" />
												<span className="font-medium text-gray-500">
													{section.label}
												</span>
											</div>
											<ChevronRight
												className={cn(
													"w-4 h-4 transition-transform duration-200 text-gray-500",
													(section.subsections.some(
														(sub) => pathname === sub.href
													) ||
														pathname.includes(section.label.toLowerCase())) &&
													"rotate-90"
												)}
											/>
										</summary>
										<AnimatePresence>
											<motion.div
												initial={{ height: 0, opacity: 0, y: -10 }}
												animate={{ height: "auto", opacity: 1, y: 0 }}
												exit={{ height: 0, opacity: 0, y: -10 }}
												transition={{
													duration: 0.2,
													ease: "easeOut"
												}}
												className="ml-7 mt-2 space-y-1 overflow-hidden"
											>
												{section.subsections.map((sub) => (
													<motion.div
														key={sub.href}
														initial={{ opacity: 0, x: -10 }}
														animate={{ opacity: 1, x: 0 }}
														exit={{ opacity: 0, x: -10 }}
														transition={{ duration: 0.2 }}
													>
														<Link
															href={sub.href}
															className={cn(
																"flex items-center p-2 rounded-lg text-sm text-gray-600 hover:bg-gray-100",
																pathname === sub.href &&
																"bg-gray-100 text-gray-900"
															)}
														>
															{sub.label}
														</Link>
													</motion.div>
												))}
											</motion.div>
										</AnimatePresence>
									</details>
								) : (
									<Link
										key={section.label}
										href={section.comingSoon ? "#" : section.href}
										className={cn(
											"flex items-center justify-between p-2 rounded-lg",
											pathname === section.href || pathname.includes(section.href)
												? "bg-gray-100 text-gray-900"
												: section.label === "Pro Membership"
													? "bg-gradient-to-r from-yellow-400 to-amber-500 text-white hover:from-yellow-500 hover:to-amber-600"
													: "text-gray-600 hover:bg-gray-100"
										)}
										onClick={(e) => {
											if (section.comingSoon) {
												e.preventDefault();
												router.push("/maintenance-tracker/interest");
											}
										}}
										{...(section.label === "Pro Membership"
											? {
												target: "_blank",
												rel: "noopener noreferrer"
											}
											: {})}
									>
										<div className="flex items-center">
											<section.icon
												className={cn(
													"w-5 h-5 mr-2 text-gray-500 flex-shrink-0",
													section.label === "Pro Membership" && "text-white"
												)}
											/>
											<span className="font-medium">{section.label}</span>
										</div>
										{section.comingSoon && (
											<span className="inline-flex items-center justify-center text-xs bg-emerald-100 text-emerald-800 px-2 py-0.5 rounded-full whitespace-nowrap">
												Coming Soon
											</span>
										)}
									</Link>
								)
							)}
						</div>
					</div>
				</div>
			</>


			)}

			{/* Main Content */}
			<div className={`flex-1 md:ml-0 ${isMobileWebView ? "ml-0" : "ml-16"}`}>
				<div className="container mx-auto md:py-6">{children}</div>
			</div>
		</div>
	);
}

export default withAuthorization(UserLayout);
