"use client";

import ProMembership from "@/app/(main)/(dashboard)/components/ProMembership";
import { NewsletterCTA } from "@/components/dashboard/NewsletterCTA";
import ShareQRCode from "@/components/dashboard/ShareQRCode";
import { CenteredPageLoader } from "@/components/Loader";
import { SupportModal } from "@/components/modals/SupportModal";
import { Button } from "@/components/ui/button";
import config from "@/config";
import { useAuth } from "@/lib/hooks/useAuth";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import { Intercom } from "@intercom/messenger-js-sdk";
import {
	ChevronRight,
	HelpCircle,
	Search,
	UserCog,
	Wrench
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import ServiceRequestsAlerts from "../../service-requests/[id]/components/service-requests-alerts";
import HelpfulResources from "../components/HelpfulResources";

const DashboardHeader = ({ userName, rvDetails }) => (
	<div className="mb-8">
		<h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
			Welcome back, {userName}
		</h1>
		<div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-2">
			<p className="text-gray-500 text-lg">
				{rvDetails?.model || "No RV Details"}
			</p>
			<Link href="/profile">
				<Button variant="outline" size="sm" className="w-full sm:w-auto">
					<UserCog className="h-4 w-4 mr-2" />
					{rvDetails?.model ? "Edit RV" : "Add RV"}
				</Button>
			</Link>
		</div>
	</div>
);

const HelpfulLinks = () => {
	const router = useRouter();
	const [supportOpen, setSupportOpen] = useState(false);

	return (
		<div className="mb-8 bg-white rounded-xl border border-gray-200 overflow-hidden">
			<div className="p-6 border-b border-gray-100">
				<h2 className="text-lg font-medium text-gray-900">Helpful Resources</h2>
			</div>
			<div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
				<Link
					href="/search"
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-[#43806c]/10 p-3 rounded-lg">
							<Wrench className="w-5 h-5 text-[#43806c]" />
						</div>
						<div>
							<h4 className="font-medium text-gray-900">Find RV Tech</h4>
							<p className="text-sm text-gray-500">
								Get matched with qualified technicians
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Link>

				<Link
					href="/search?category=rv-inspection"
					className="flex h-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-[#43806c]/10 p-3 rounded-lg">
							<Search className="w-5 h-5 text-[#43806c]" />
						</div>
						<div>
							<h4 className="font-medium text-gray-900">Find Inspector</h4>
							<p className="text-sm text-gray-500">
								Get your RV professionally inspected
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</Link>

				<button
					type="button"
					onClick={() => router.push("/profile")}
					className="flex h-full w-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-purple-500/10 p-3 rounded-lg">
							<HelpCircle className="w-5 h-5 text-purple-500" />
						</div>
						<div className="text-left">
							<h4 className="font-medium text-gray-900">Update Your Profile</h4>
							<p className="text-sm text-gray-500">
								Update your profile and add RV details
							</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</button>

				<button
					type="button"
					onClick={() => setSupportOpen(true)}
					className="flex h-full w-full items-center justify-between p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
				>
					<div className="flex items-center gap-4">
						<div className="bg-purple-500/10 p-3 rounded-lg">
							<HelpCircle className="w-5 h-5 text-purple-500" />
						</div>
						<div className="text-left">
							<h4 className="font-medium text-gray-900">Get Support</h4>
							<p className="text-sm text-gray-500">Message our support team</p>
						</div>
					</div>
					<ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
				</button>
			</div>

			<SupportModal open={supportOpen} onOpenChange={setSupportOpen} />
		</div>
	);
};

const UserDashboard = () => {
	const { user, loading, isPaid } = useAuth();

	useEffect(() => {
		if (!config.intercom.appId) return;

		// Get or create a unique visitor ID from localStorage
		let visitorId = localStorage.getItem("intercom_visitor_id");
		if (!visitorId) {
			visitorId = uuidv4();
			localStorage.setItem("intercom_visitor_id", visitorId);
		}

		Intercom({
			app_id: config.intercom.appId,
			custom_launcher_selector: "#help-button",
			hide_default_launcher: true,
			user_id: visitorId
		});

		return () => {
			// Clean up Intercom when component unmounts
			if (typeof window !== "undefined" && window.Intercom) {
				window.Intercom("shutdown");
			}
		};
	}, []);

	if (loading || !user) {
		return <CenteredPageLoader />;
	}

	return (
		<div className="min-h-screen bg-gray-50/50">
			<div className="container py-12">
				<div className="max-w-7xl mx-auto space-y-8">
					<DashboardHeader
						userName={user.first_name}
						rvDetails={user.rv_details}
					/>
					<ServiceRequestsAlerts />

					{isPaid ? <HelpfulResources /> : <ProMembership />}

					<HelpfulLinks />

					<NewsletterCTA />

					<ShareQRCode />
				</div>
			</div>
		</div>
	);
};

export default withAuthorization(UserDashboard, "USER");
