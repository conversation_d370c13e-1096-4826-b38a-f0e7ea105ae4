import prisma from "@/lib/prisma";
import { url } from "@/lib/url";
import { notFound, redirect } from "next/navigation";

export default async function MRRRedirectPage({
	params
}: {
	params: { slug: string };
}) {
	const listing = await prisma.listing.findFirst({
		where: {
			mrr_member_slug: params.slug
		}
	});

	if (!listing) {
		notFound();
	}

	redirect(
		url("view-listing", { slug: listing.slug, utm_source: "myrvresource.com" })
	);
}
