"use client";

import TroubleshootingModal from "@/components/directory/listing-details/TroubleshootingModal";
import { StepLocation } from "@/components/global-modals/send-message/step-location";
import { messageProviderSchema } from "@/components/global-modals/send-message/types";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAuth } from "@/lib/hooks/useAuth";
import { renderStarRating } from "@/lib/utils/star-rating";
import { ListingWithLocation } from "@/types/global";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle2, Phone } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

import toast from "react-hot-toast";
import type { z } from "zod";

type FormData = z.infer<typeof messageProviderSchema>;

export default function VirtualTroubleshootingPage() {
	const { user } = useAuth();
	const [error, setError] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showVirtualTroubleshootingModal, setShowVirtualTroubleshootingModal] =
		useState(false);
	const [selectedProvider, setSelectedProvider] =
		useState<ListingWithLocation | null>(null);
	const [showExplanation, setShowExplanation] = useState(true);
	const [providersWithTroubleshooting, setProvidersWithTroubleshooting] =
		useState<ListingWithLocation[]>([]);
	const [providerWithTroubleshooting, setProviderWithTroubleshooting] =
		useState<ListingWithLocation>(null);
	const [otherProviders, setOtherProviders] = useState<ListingWithLocation[]>(
		[]
	);
	const [isSuccess, setIsSuccess] = useState(false);
	// Initialize form with default values
	const form = useForm<FormData>({
		resolver: zodResolver(messageProviderSchema),
		defaultValues: {
			location: {
				address: "",
				latitude: user?.latitude || 0,
				longitude: user?.longitude || 0
			},

			contact_phone: user?.phone || "",
			category: "rv-repair",
			first_name: user?.first_name || "",
			last_name: user?.last_name || "",
			email: user?.email || "",
			rv_type: "",
			rv_make: "",
			rv_model: "",
			rv_year: undefined,
			message: "",
			listing_id: ""
		}
	});

	// Watch for location changes
	const location = form.watch("location");

	// Debug logging for location changes
	useEffect(() => {
		console.log("🌍 Location changed:", location);
		console.log("📋 Full form values:", form.getValues());
	}, [location]);

	// Search for providers when location changes
	useEffect(() => {
		console.log("🔍 Checking location for provider search:", location);
		if (!location?.latitude || !location?.longitude) {
			console.log("❌ No valid location, skipping provider search");
			return;
		}

		const searchProviders = async () => {
			try {
				setIsSubmitting(true);
				const searchParams = new URLSearchParams({
					category: "rv-repair",
					lat: location.latitude.toString(),
					lng: location.longitude.toString(),
					showTroubleshootingProviders: "true",
					page: "1",
					limit: "10",
					radius: "200",
					sortBy: "default",
					zoom: "6"
				});

				const response = await fetch(`/api/virtual-troubleshooting`, {
					method: "POST",
					body: JSON.stringify({
						location: {
							latitude: location.latitude,
							longitude: location.longitude,
							address: location.address
						},
						user_id: user?.id,
						message: form.getValues("message")
					})
				});
				const data = await response.json();

				setProvidersWithTroubleshooting(data.providersWithTroubleshooting);
				setOtherProviders(data.otherProviders);
				setProviderWithTroubleshooting(data.providersWithTroubleshooting[0]);
				setIsSubmitting(false);
			} catch (error) {
				console.error("Error searching providers:", error);
				toast.error("Failed to load providers. Please try again.");
				setIsSubmitting(false);
			}
		};

		searchProviders();
	}, [location?.latitude, location?.longitude]);

	// Update form with user data
	useEffect(() => {
		if (!user) return;

		// Update user data
		form.setValue("first_name", user.first_name || "");
		form.setValue("last_name", user.last_name || "");
		form.setValue("email", user.email || "");
		form.setValue("contact_phone", user.phone || "");

		// Update location if available
		if (user.latitude && user.longitude) {
			form.setValue("location", {
				...form.getValues("location"),
				latitude: user.latitude,
				longitude: user.longitude
			});
		}

		// Update RV details if available in user data
		if (user.rv_details) {
			const rvDetails = user.rv_details as {
				year?: string | number;
				make?: string;
				model?: string;
				type?: string;
			};

			if (rvDetails.type) {
				form.setValue("rv_type", rvDetails.type);
			}
			if (rvDetails.make) {
				form.setValue("rv_make", rvDetails.make);
			}
			if (rvDetails.model) {
				form.setValue("rv_model", rvDetails.model);
			}
			if (rvDetails.year) {
				form.setValue(
					"rv_year",
					typeof rvDetails.year === "string"
						? parseInt(rvDetails.year)
						: rvDetails.year
				);
			}
		}
	}, [user, form]);

	if (error) {
		return (
			<div className="container py-12">
				<div className="max-w-3xl mx-auto">
					<div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
						<p className="text-red-600">{error}</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container py-12">
			<div className="max-w-3xl mx-auto">
				{/* Page Title */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">
						Virtual Troubleshooting
					</h1>
					<p className="mt-2 text-gray-600">
						Get quick help from certified RV techs in your area
					</p>
				</div>

				{/* Progress Steps */}
				<div className="mb-8">
					<div className="flex items-center justify-between relative">
						<div className="absolute left-0 right-0 top-1/2 h-0.5 bg-gray-200 -translate-y-1/2" />
						<div className="relative flex items-center justify-center bg-white z-10">
							<div
								className={`w-8 h-8 rounded-full flex items-center justify-center ${showExplanation ? "bg-primary text-white" : "bg-primary/20 text-primary"}`}
							>
								1
							</div>
							<span className="ml-2 text-sm font-medium">About</span>
						</div>
						<div className="relative flex items-center justify-center bg-white z-10">
							<div
								className={`w-8 h-8 rounded-full flex items-center justify-center ${!showExplanation && !providerWithTroubleshooting ? "bg-primary text-white" : "bg-primary/20 text-primary"}`}
							>
								2
							</div>
							<span className="ml-2 text-sm font-medium">Location</span>
						</div>
						<div className="relative flex items-center justify-center bg-white z-10">
							<div
								className={`w-8 h-8 rounded-full flex items-center justify-center ${providerWithTroubleshooting ? "bg-primary text-white" : "bg-primary/20 text-primary"}`}
							>
								3
							</div>
							<span className="ml-2 text-sm font-medium">Select Provider</span>
						</div>
					</div>
				</div>

				{showExplanation ? (
					<div className="bg-white border border-gray-200 rounded-xl p-8 text-center">
						<div className="flex flex-col items-center justify-center">
							<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
								<Phone className="w-8 h-8 text-primary" />
							</div>
							<div className="space-y-3 max-w-lg mx-auto">
								<h3 className="text-2xl font-semibold text-primary">
									Pre-Service Virtual Troubleshooting
								</h3>
								<div className="space-y-4">
									<p className="text-muted-foreground text-base leading-relaxed">
										Get quick help from certified RV techs in your area through
										a 10-15 minute troubleshooting call. This can often save you
										money by:
									</p>
									<ul className="text-left text-muted-foreground space-y-2 list-disc list-inside">
										<li>Identifying simple fixes you can do yourself</li>
										<li>
											Ensuring the right tech with the right parts comes out
										</li>
										<li>Avoiding unnecessary service calls</li>
									</ul>
									<p className="text-sm text-muted-foreground/80 italic">
										After submitting your request, we'll notify techs in your
										area who can help. The first available tech will contact you
										for a quick diagnostic call.
									</p>
								</div>
							</div>
							<Button
								type="button"
								onClick={() => setShowExplanation(false)}
								className="mt-8 min-w-[200px]"
								size="lg"
							>
								Get Started
							</Button>
						</div>
					</div>
				) : (
					<div className="bg-white border border-gray-200 rounded-xl p-8">
						<Form {...form}>
							<form className="space-y-6">
								<StepLocation form={form} disableServiceAreaCheck={true} />

								{isSubmitting && (
									<div className="text-center py-4">
										<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
										<p className="text-sm text-gray-600 mt-2">
											Searching for providers...
										</p>
									</div>
								)}

								{providersWithTroubleshooting && !isSubmitting && (
									<div className="space-y-4">
										{providersWithTroubleshooting.length > 0 ? (
											<div className="border border-green-200 bg-green-50 rounded-lg p-6">
												<h3 className="text-lg font-semibold text-green-800 mb-4">
													Great news! We found{" "}
													{providersWithTroubleshooting.length} tech
													{providersWithTroubleshooting.length > 1
														? "s"
														: ""}{" "}
													who offer
													{providersWithTroubleshooting.length > 1
														? ""
														: "s"}{" "}
													virtual troubleshooting
												</h3>
												<div className="space-y-4">
													{providersWithTroubleshooting
														.slice(0, 5)
														.map((provider) => (
															<div
																key={provider.id}
																className="bg-white rounded-xl overflow-hidden border border-gray-200 hover:border-primary/20 transition-all duration-200"
															>
																<div className="p-6">
																	{/* Header */}
																	<div className="flex items-start justify-between mb-4">
																		<div>
																			<h4 className="text-xl font-semibold text-gray-900">
																				{provider.business_name}
																			</h4>
																			<div className="flex items-center gap-2 mt-1">
																				<div className="flex items-center">
																					<div className="text-yellow-400 flex items-center">
																						{"★".repeat(
																							Math.floor(provider.rating || 0)
																						)}
																						{"☆".repeat(
																							5 -
																								Math.floor(provider.rating || 0)
																						)}
																					</div>
																					<span className="ml-1 text-sm text-gray-600">
																						({provider.rating || 0} reviews)
																					</span>
																				</div>
																			</div>
																		</div>
																		{provider.profile_image && (
																			<img
																				src={provider.profile_image}
																				alt="Provider"
																				className="w-16 h-16 rounded-full object-cover border-2 border-gray-100"
																			/>
																		)}
																	</div>

																	{/* Details */}
																	<div className="space-y-3 mb-6">
																		{provider.short_description && (
																			<p className="text-gray-600 text-sm line-clamp-2">
																				{provider.short_description}
																			</p>
																		)}
																		<div className="flex items-center gap-4 text-sm text-gray-600">
																			<div className="flex items-center gap-1">
																				<svg
																					className="w-4 h-4"
																					fill="none"
																					viewBox="0 0 24 24"
																					stroke="currentColor"
																				>
																					<path
																						strokeLinecap="round"
																						strokeLinejoin="round"
																						strokeWidth={2}
																						d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
																					/>
																					<path
																						strokeLinecap="round"
																						strokeLinejoin="round"
																						strokeWidth={2}
																						d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
																					/>
																				</svg>
																				<span>
																					{provider.location.city},{" "}
																					{provider.location.state}
																				</span>
																			</div>
																			{provider.year_established && (
																				<div className="flex items-center gap-1">
																					<svg
																						className="w-4 h-4"
																						fill="none"
																						viewBox="0 0 24 24"
																						stroke="currentColor"
																					>
																						<path
																							strokeLinecap="round"
																							strokeLinejoin="round"
																							strokeWidth={2}
																							d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
																						/>
																					</svg>
																					<span>
																						{new Date().getFullYear() -
																							Number(
																								provider.year_established
																							)}{" "}
																						years in business
																					</span>
																				</div>
																			)}
																		</div>
																	</div>

																	{/* Virtual Troubleshooting Badge */}
																	<div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg mb-4">
																		<svg
																			className="w-5 h-5 text-green-600"
																			fill="none"
																			viewBox="0 0 24 24"
																			stroke="currentColor"
																		>
																			<path
																				strokeLinecap="round"
																				strokeLinejoin="round"
																				strokeWidth={2}
																				d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
																			/>
																		</svg>
																		<span className="text-sm font-medium text-green-800">
																			Offers Virtual Troubleshooting
																		</span>
																	</div>

																	{/* Action Button */}
																	<Button
																		onClick={() => {
																			setSelectedProvider(provider);
																			setShowVirtualTroubleshootingModal(true);
																		}}
																		className="w-full bg-primary hover:bg-primary/90 text-white"
																		size="lg"
																		type="button"
																	>
																		Request Virtual Troubleshooting Call
																	</Button>
																</div>
															</div>
														))}
												</div>
											</div>
										) : otherProviders?.length > 0 ? (
											<div className="border border-yellow-200 bg-yellow-50 rounded-lg p-6">
												<div className="mb-6">
													<h3 className="text-lg font-semibold text-yellow-800 mb-2">
														No providers currently offer virtual troubleshooting
														in your area
													</h3>
													<p className="text-yellow-700">
														While these providers haven't explicitly opted in
														for virtual troubleshooting yet, we can ask if
														they're willing to help you remotely.
													</p>
												</div>

												<div className="space-y-4">
													{otherProviders
														.slice(0, 5)
														.map((provider: ListingWithLocation) => (
															<div
																key={provider.id}
																className="bg-white rounded-xl overflow-hidden border border-gray-200 hover:border-primary/20 transition-all duration-200"
															>
																<div className="p-6">
																	{/* Header */}
																	<div className="flex items-start justify-between mb-4">
																		<div>
																			<h4 className="text-xl font-semibold text-gray-900">
																				{provider.business_name}
																			</h4>
																			<div className="flex items-center gap-2 mt-1">
																				<div className="flex items-center">
																					{renderStarRating(
																						provider.rating,
																						provider.rating_count
																					)}
																				</div>
																			</div>
																		</div>
																		{provider.profile_image && (
																			<img
																				src={provider.profile_image}
																				alt="Provider"
																				className="w-16 h-16 rounded-full object-cover border-2 border-gray-100"
																			/>
																		)}
																	</div>

																	{/* Details */}
																	<div className="space-y-3 mb-6">
																		{provider.short_description && (
																			<p className="text-gray-600 text-sm line-clamp-2">
																				{provider.short_description}
																			</p>
																		)}
																		<div className="flex items-center gap-4 text-sm text-gray-600">
																			<div className="flex items-center gap-1">
																				<svg
																					className="w-4 h-4"
																					fill="none"
																					viewBox="0 0 24 24"
																					stroke="currentColor"
																				>
																					<path
																						strokeLinecap="round"
																						strokeLinejoin="round"
																						strokeWidth={2}
																						d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
																					/>
																					<path
																						strokeLinecap="round"
																						strokeLinejoin="round"
																						strokeWidth={2}
																						d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
																					/>
																				</svg>
																				<span>
																					{provider.location.city},{" "}
																					{provider.location.state}
																				</span>
																			</div>
																			{provider.year_established && (
																				<div className="flex items-center gap-1">
																					<svg
																						className="w-4 h-4"
																						fill="none"
																						viewBox="0 0 24 24"
																						stroke="currentColor"
																					>
																						<path
																							strokeLinecap="round"
																							strokeLinejoin="round"
																							strokeWidth={2}
																							d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
																						/>
																					</svg>
																					<span>
																						{new Date().getFullYear() -
																							Number(
																								provider.year_established
																							)}{" "}
																						years in business
																					</span>
																				</div>
																			)}
																		</div>
																	</div>

																	{/* Action Button */}
																	<Button
																		onClick={() => {
																			setSelectedProvider(provider);
																			setShowVirtualTroubleshootingModal(true);
																		}}
																		className="w-full bg-yellow-500 hover:bg-yellow-600 text-white"
																		size="lg"
																		type="button"
																	>
																		Ask About Virtual Troubleshooting
																	</Button>
																</div>
															</div>
														))}
												</div>
											</div>
										) : !isSubmitting &&
										  location?.latitude &&
										  location?.longitude ? (
											<div className="border border-red-200 bg-red-50 rounded-lg p-6">
												<h3 className="text-lg font-semibold text-red-800 mb-2">
													No providers available
												</h3>
												<p className="text-red-700 mb-4">
													We couldn't find any providers in your area. Please
													try a different location or contact our support team
													for assistance.
												</p>
											</div>
										) : null}
									</div>
								)}
							</form>
						</Form>
					</div>
				)}
			</div>
			{showVirtualTroubleshootingModal && selectedProvider && (
				<TroubleshootingModal
					isOpen={showVirtualTroubleshootingModal}
					onClose={() => setShowVirtualTroubleshootingModal(false)}
					listing={selectedProvider}
					onSuccess={() => {
						setShowVirtualTroubleshootingModal(false);
						setIsSuccess(true);
					}}
				/>
			)}

			{isSuccess && (
				<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
					<div className="bg-white rounded-xl p-8 max-w-lg w-full mx-4">
						<div className="text-center">
							<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
								<CheckCircle2 className="w-10 h-10 text-green-600" />
							</div>
							<h3 className="text-2xl font-semibold text-gray-900 mb-3">
								Request Sent Successfully!
							</h3>
							<p className="text-gray-600 mb-6">
								Your troubleshooting request has been sent. The tech will review
								it and get back to you soon via your preferred contact method.
							</p>
							<Button
								onClick={() => setIsSuccess(false)}
								className="w-full bg-primary hover:bg-primary/90"
								size="lg"
							>
								Close
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
