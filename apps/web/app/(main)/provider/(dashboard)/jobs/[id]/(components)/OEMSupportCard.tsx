import { CenteredPageLoader } from "@/components/Loader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Company } from "@rvhelp/database";

interface OEMSupportCardProps {
	company: Company;
	representative: string;
}

export function OEMSupportCard({
	company,
	representative
}: OEMSupportCardProps) {
	if (!company) {
		return <Card className="mt-16"></Card>;
	}
	return (
		<Card className="mt-16">
			<CardHeader>
				<CardTitle>{`${company.name}  Support`}</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-2">
					{company ? (
						<>
							<p>{`To order parts or for manufacturer specific questions, please contact ${company.name} support:`}</p>
							<div className="space-y-1">
								<span className="text-sm text-muted-foreground">Phone:</span>
								<div className="text-md">{company.support_phone}</div>
							</div>
							<div className="space-y-1">
								<span className="text-sm text-muted-foreground">Email:</span>
								<div className="text-md">{company.support_email}</div>
							</div>
							<div className="space-y-1">
								<span className="text-sm text-muted-foreground">
									Representative:
								</span>
								<div className="text-md">{representative}</div>
							</div>
							<div className="border-b border-gray-200" />
							<div className="space-y-1">
								<span className="text-md font-semibold italic">
									Any questions regarding payment, please direct <NAME_EMAIL>.
								</span>
							</div>
						</>
					) : (
						<CenteredPageLoader />
					)}
				</div>
			</CardContent>
		</Card>
	);
}
