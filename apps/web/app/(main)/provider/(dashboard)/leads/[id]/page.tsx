"use client";

import MessageThread from "@/components/MessageThread";
import { WarrantyInfoSection } from "@/components/partner-portal/WarrantyInfoSection";
import { WarrantyTermsGate } from "@/components/partner-portal/WarrantyTermsGate";
import { Badge } from "@/components/ui/badge";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger
} from "@/components/ui/tooltip";
import { getCategoryName } from "@/lib/categories";
import { withAuthorization } from "@/lib/hooks/withAuthorization";
import {
	JobWithUserAndLocation,
	QuoteWithJob
} from "@/types/global";
import { QuoteStatus, ResolutionStatus } from "@rvhelp/database";
import { formatDistanceToNow } from "date-fns";
import {
	AlertCircle,
	CheckCircle,
	ExternalLink,
	HelpCircle,
	Mail,
	MapPin,
	MessageSquare,
	Phone,
	Truck,
	User,
	X
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { AcceptLeadModal } from "../../jobs/[id]/(components)/AcceptLeadModal";
import { DeclineLeadModal } from "../../jobs/[id]/(components)/DeclineLeadModal";
import type { QuoteWithMessages } from "../../jobs/[id]/types";
import { UpdateLeadResolutionDialog } from "../components/UpdateLeadResolutionDialog";
import { WarrantyLeadSuccessDialog } from "./components/WarrantyLeadSuccessDialog";

function LeadDetailsPage({ params }: { params: { id: string } }) {
	const router = useRouter();
	const [job, setJob] = useState<JobWithUserAndLocation>(null);
	const [quote, setQuote] = useState<QuoteWithJob | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isResponding, setIsResponding] = useState(false);
	const [showResolutionDialog, setShowResolutionDialog] = useState(false);
	const [showAcceptModal, setShowAcceptModal] = useState(false);
	const [showDeclineModal, setShowDeclineModal] = useState(false);
	const [showWarrantySuccessDialog, setShowWarrantySuccessDialog] =
		useState(false);

	const [resolutionStatus, setResolutionStatus] = useState<ResolutionStatus>(
		ResolutionStatus.COMPLETED
	);
	const [resolutionDetails, setResolutionDetails] = useState("");
	const [validationError, setValidationError] = useState<string | null>(null);

	// Warranty access and terms state
	const [hasWarrantyAccess, setHasWarrantyAccess] = useState(true);
	const [isAcceptingTerms, setIsAcceptingTerms] = useState(false);
	const [showWarrantyTermsGate, setShowWarrantyTermsGate] = useState(false);
	const [warrantyInfo, setWarrantyInfo] = useState<{
		companyName?: string;
		termsAccepted?: boolean;
	}>({});

	const fetchLead = useCallback(async () => {
		try {
			const response = await fetch(`/api/provider/quotes/${params.id}`);
			if (!response.ok) {
				throw new Error("Failed to fetch lead");
			}
			const data = await response.json();
			setQuote(data);
			setJob(data.job);

			// Set warranty access information from API response
			if (data.warranty_access) {
				setHasWarrantyAccess(data.warranty_access.hasAccess);
				setWarrantyInfo({
					companyName: data.warranty_access.companyName,
					termsAccepted: data.warranty_access.termsAccepted
				});
			}
		} catch (error) {
			console.error("Error fetching lead:", error);
			toast.error("Failed to load lead details");
		} finally {
			setIsLoading(false);
		}
	}, [params.id]);

	useEffect(() => {

		fetchLead();
	}, [fetchLead]);

	// Memoized callbacks to prevent modal re-renders
	const handleAcceptLeadResponse = useCallback(() => {
		setShowAcceptModal(false);
		// If this is a warranty lead, show the success dialog
		if (job?.warranty_request_id) {
			setShowWarrantySuccessDialog(true);
		} else {
			// For regular leads, stay on the same page and refresh
			fetchLead();
		}
	}, [job?.warranty_request_id, fetchLead]);

	const handleDeclineLeadResponse = useCallback(() => {
		setShowDeclineModal(false);
		fetchLead();
	}, [fetchLead]);

	// Memoized values to prevent unnecessary re-renders
	const isWarrantyJob = Boolean(job?.warranty_request_id);
	const quoteForModal = quote as unknown as QuoteWithMessages;

	const handleAcceptWarrantyTerms = async () => {
		if (!warrantyInfo.companyName) return;

		setIsAcceptingTerms(true);
		try {
			const response = await fetch(
				`/api/provider/quotes/${params.id}/warranty-terms`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						accepted: true
					})
				}
			);

			if (!response.ok) {
				const error = await response.json();
				console.error("Failed to accept warranty terms:", error);
				toast.error("Failed to accept warranty terms. Please try again.");
				return;
			}

			toast.success("Warranty terms accepted successfully!");
			setShowWarrantyTermsGate(false);

			// Refresh the data to get updated access status
			await fetchLead();
		} catch (error) {
			console.error("Error accepting warranty terms:", error);
			toast.error("Failed to accept warranty terms. Please try again.");
		} finally {
			setIsAcceptingTerms(false);
		}
	};

	const handleResolution = async () => {
		if (!quote) return;
		setValidationError(null);
		setShowResolutionDialog(true);
	};

	const handleResolutionSubmit = async () => {
		if (!quote) return;

		// Validate notes are required for "other" status
		if (
			resolutionStatus === ResolutionStatus.OTHER &&
			!resolutionDetails.trim()
		) {
			setValidationError(
				"Notes are required when selecting 'Other' as the resolution status"
			);
			return;
		}

		setIsResponding(true);
		setValidationError(null);

		try {
			const response = await fetch(
				`/api/provider/quotes/${params.id}/complete`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json"
					},
					body: JSON.stringify({
						resolutionStatus,
						resolutionNotes: resolutionDetails
					})
				}
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to update lead status");
			}

			toast.success("Lead resolution updated successfully");
			const updatedResponse = await fetch(`/api/provider/quotes/${params.id}`);
			if (updatedResponse.ok) {
				const updatedData = await updatedResponse.json();
				setQuote(updatedData);
				setJob(updatedData.job);
			}
			setShowResolutionDialog(false);
		} catch (error) {
			console.error("Error updating lead:", error);
			setValidationError(
				error instanceof Error ? error.message : "Failed to update lead status"
			);
		} finally {
			setIsResponding(false);
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case QuoteStatus.PENDING:
				return (
					<Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
						Pending
					</Badge>
				);
			case QuoteStatus.ACCEPTED:
				return (
					<Badge className="bg-green-100 text-green-800 border-green-200">
						Accepted
					</Badge>
				);
			case QuoteStatus.IN_PROGRESS:
				return (
					<Badge className="bg-blue-100 text-blue-800 border-blue-200">
						In Progress
					</Badge>
				);
			case QuoteStatus.REJECTED:
				return (
					<Badge className="bg-red-100 text-red-800 border-red-200">
						Declined
					</Badge>
				);
			case QuoteStatus.COMPLETED:
				return (
					<Badge className="bg-blue-100 text-blue-800 border-blue-200">
						Completed
					</Badge>
				);
			default:
				return (
					<Badge className="bg-gray-100 text-gray-800 border-gray-200">
						{status}
					</Badge>
				);
		}
	};

	if (isLoading) {
		return (
			<div className="min-h-screen bg-gray-50 animate-pulse">
				<div className="bg-white shadow-sm border-b">
					<div className="max-w-4xl mx-auto px-4 py-4">
						<div className="flex items-center justify-between">
							<div className="h-6 bg-gray-200 rounded w-24"></div>
							<div className="h-6 bg-gray-200 rounded w-16"></div>
						</div>
					</div>
				</div>
				<div className="max-w-4xl mx-auto px-4 py-6">
					<div className="bg-white rounded-xl shadow-sm border mb-6">
						<div className="p-6 space-y-4">
							<div className="h-8 bg-gray-200 rounded w-48"></div>
							<div className="h-32 bg-gray-200 rounded"></div>
						</div>
					</div>
					<div className="bg-white rounded-xl shadow-sm border">
						<div className="h-96 bg-gray-200 rounded"></div>
					</div>
				</div>
			</div>
		);
	}

	if (!quote) {
		return (
			<div className="min-h-screen bg-gray-50">
				<div className="flex items-center justify-between">
					<h1 className="text-xl font-bold text-gray-900">Lead Details</h1>
				</div>

				<Card>
					<CardHeader className="bg-[#43806c] text-white">
						<CardTitle>Lead not found</CardTitle>
					</CardHeader>
					<CardContent className="py-12">
						<div className="text-center space-y-6">
							<p className="text-lg text-gray-600">
								These are not the leads you are looking for.
							</p>
							<Image
								src="/images/not-found.jpg"
								alt="Obi-Wan wave"
								width={400}
								height={300}
								className="rounded-lg object-cover mx-auto shadow-lg"
							/>
							<Button
								onClick={() => router.push("/provider/leads")}
								className="mt-4"
							>
								Back to Leads
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Show warranty terms gate modal for warranty jobs without accepted terms
	if (job?.warranty_request_id && !hasWarrantyAccess) {
		// Show the modal automatically
		if (!showWarrantyTermsGate) {
			setShowWarrantyTermsGate(true);
		}
	}

	return (
		<div className="min-h-screen bg-gray-50 relative">
			{showWarrantyTermsGate && (
				<div className="backdrop-blur-sm bg-white/25 absolute top-0 left-0 w-full h-full z-0" />
			)}

			<Breadcrumbs
				className="my-4"
				items={[
					{ label: "Leads", href: "/provider/leads" },
					{ label: "Lead Details", href: `/provider/leads/${params.id}` }
				]}
				showBackButton={false}
			/>

			<div className="max-w-4xl mx-auto pb-6">
				{/* Warranty Information Section - Show prominently for warranty jobs */}
				{job.warranty_request_id && job.warranty_request && (
					<div className="mb-6">
						<WarrantyInfoSection
							manufacturer={
								job.warranty_request.component?.manufacturer || "Unknown"
							}
							approvedHours={job.warranty_request.approved_hours}
							componentType={
								job.warranty_request.component?.type || "Component"
							}
							companyName={warrantyInfo.companyName || "Partner"}
						/>
					</div>
				)}

				{/* Action Alerts - Desktop Only */}
				<div className="hidden md:block mb-6">

					{/* Standard lead action alert for non-warranty jobs OR warranty jobs with terms accepted */}
					{quote.status === QuoteStatus.PENDING &&
						(!job.warranty_request_id || hasWarrantyAccess) && (
							<Card className="border-l-4 border-l-yellow-400">
								<CardContent className="p-4">
									<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
										<div className="flex items-center gap-2">
											<AlertCircle className="h-5 w-5 text-yellow-600" />
											<p className="text-yellow-800 font-medium">
												This lead needs your attention
											</p>
										</div>
										<div className="flex gap-2">
											<Button
												onClick={() => setShowAcceptModal(true)}
												disabled={isResponding}
												className="bg-green-600 hover:bg-green-700 text-white"
											>
												<CheckCircle className="h-4 w-4 mr-2" />
												Accept Lead
											</Button>
											<Button
												onClick={() => setShowDeclineModal(true)}
												disabled={isResponding}
												className="bg-red-600 hover:bg-red-700 text-white"
											>
												<X className="h-4 w-4 mr-2" />
												Not Available
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						)}

					{/* Success states remain the same */}
					{(quote.status === QuoteStatus.ACCEPTED ||
						quote.status === QuoteStatus.IN_PROGRESS) && (
							<Card className="border-l-4 border-l-green-400">
								<CardContent className="p-4">
									<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
										<div className="flex items-center gap-2">
											<CheckCircle className="h-5 w-5 text-green-600" />
											<p className="text-green-800 font-medium">
												{job?.warranty_request_id
													? "Warranty lead accepted. Manage this work through the workroom."
													: "Lead accepted. Update resolution status when work is complete."}
											</p>
										</div>
										{job?.warranty_request_id ? (
											<Button
												onClick={() => router.push(`/provider/jobs/${params.id}`)}
												className="bg-[#43806c] hover:bg-[#2c5446] text-white"
											>
												<ExternalLink className="h-4 w-4 mr-2" />
												Go to Workroom
											</Button>
										) : (
											<Button
												onClick={handleResolution}
												disabled={isResponding}
												className="bg-[#43806c] hover:bg-[#2c5446] text-white"
											>
												Update Resolution Status
											</Button>
										)}
									</div>
								</CardContent>
							</Card>
						)}

					{quote.status === QuoteStatus.REJECTED && (
						<Card className="border-l-4 border-l-red-400">
							<CardContent className="p-4">
								<div className="flex items-center gap-2">
									<X className="h-5 w-5 text-red-600" />
									<p className="text-red-800 font-medium">
										This lead has been declined
									</p>
								</div>
							</CardContent>
						</Card>
					)}

					{quote.status === QuoteStatus.COMPLETED && (
						<Card className="border-l-4 border-l-blue-400">
							<CardContent className="p-4">
								<div className="flex items-center gap-2">
									<CheckCircle className="h-5 w-5 text-blue-600" />
									<p className="text-blue-800 font-medium">
										Work completed - This lead has been resolved
									</p>
								</div>
							</CardContent>
						</Card>
					)}
				</div>

				{/* Action Buttons - Mobile Only */}
				<div className="md:hidden mb-6">

					{/* Standard lead actions for non-warranty jobs OR warranty jobs with terms accepted */}
					{quote.status === QuoteStatus.PENDING &&
						(!job.warranty_request_id || hasWarrantyAccess) && (
							<div className="flex flex-col sm:flex-row gap-3">
								<Button
									onClick={() => setShowAcceptModal(true)}
									disabled={isResponding}
									className="flex-1 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
								>
									<CheckCircle className="h-5 w-5" />
									Accept Lead
								</Button>
								<Button
									onClick={() => setShowDeclineModal(true)}
									disabled={isResponding}
									className="flex-1 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
								>
									<X className="h-5 w-5" />
									Not Available
								</Button>
							</div>
						)}

					{quote.status === QuoteStatus.ACCEPTED && (
						<div className="space-y-4">
							<div className="bg-green-50 border border-green-200 rounded-lg p-4">
								<div className="flex items-center gap-2 text-green-800">
									<CheckCircle className="h-5 w-5" />
									<span className="font-medium">
										{job?.warranty_request_id
											? "Warranty Lead Accepted"
											: "Lead Accepted"}
									</span>
								</div>
								<p className="text-sm text-green-700 mt-1">
									{job?.warranty_request_id
										? "Manage this warranty work through the workroom."
										: "You can now communicate with the customer. Update the resolution status when work is complete."}
								</p>
							</div>
							{job?.warranty_request_id ? (
								<Button
									onClick={() => router.push(`/provider/jobs/${params.id}`)}
									className="w-full bg-[#43806c] hover:bg-[#2c5446] text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
								>
									<ExternalLink className="h-5 w-5" />
									Go to Workroom
								</Button>
							) : (
								<Button
									onClick={handleResolution}
									disabled={isResponding}
									className="w-full bg-[#43806c] hover:bg-[#2c5446] text-white px-6 py-3 rounded-lg font-medium transition-colors"
								>
									Update Resolution Status
								</Button>
							)}
						</div>
					)}

					{quote.status === QuoteStatus.REJECTED && (
						<div className="bg-red-50 border border-red-200 rounded-lg p-4">
							<div className="flex items-center gap-2 text-red-800">
								<X className="h-5 w-5" />
								<span className="font-medium">Lead Declined</span>
							</div>
							<p className="text-sm text-red-700 mt-1">
								This lead has been declined and will be offered to other service
								providers.
							</p>
						</div>
					)}

					{quote.status === QuoteStatus.COMPLETED && (
						<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
							<div className="flex items-center gap-2 text-blue-800">
								<CheckCircle className="h-5 w-5" />
								<span className="font-medium">Work Completed</span>
							</div>
							<p className="text-sm text-blue-700 mt-1">
								This lead has been resolved and marked as complete.
							</p>
						</div>
					)}
				</div>
				{/* Lead Information Card */}
				<div className="bg-white rounded-xl shadow-sm border mb-6">
					<div className="p-6">
						<div className="flex items-start justify-between mb-4">
							<div className="flex items-center gap-3">
								<div className="w-12 h-12 bg-[#43806c] rounded-full flex items-center justify-center">
									<User className="w-6 h-6 text-white" />
								</div>
								<div>
									<h2 className="text-lg font-semibold text-gray-900">
										{job.first_name} {job.last_name}
									</h2>
									<p className="text-sm text-gray-600">
										{formatDistanceToNow(new Date(quote.created_at), {
											addSuffix: true
										})}
									</p>
								</div>
							</div>
							<div className="text-right">
								<p className="text-sm text-gray-500 uppercase tracking-wide">
									{getStatusBadge(quote.status)}
								</p>
							</div>
						</div>

						{/* Service Details */}
						<div className="mb-6">
							<h3 className="text-sm font-medium text-gray-900 mb-2">
								Service Needed
							</h3>
							{job.warranty_request_id ? (
								<div className="bg-gray-50 rounded-lg p-4">
									<p className="text-sm font-medium text-gray-700 mb-2">
										{getCategoryName(job.category)}
									</p>
									<p className="text-sm text-gray-700 whitespace-pre-wrap mt-2">
										<span className="font-medium">Customer Complaint:</span>{" "}
										{job.warranty_request.complaint}
									</p>
									{job.warranty_request.notes_to_provider && (
										<p className="text-sm text-gray-700 whitespace-pre-wrap mt-2">
											<span className="font-medium">Notes to Technician:</span>{" "}
											{job.warranty_request.notes_to_provider}
										</p>
									)}
								</div>
							) : (
								<div className="bg-gray-50 rounded-lg p-4">
									<p className="text-sm font-medium text-gray-700 mb-2">
										{getCategoryName(job.category)}
									</p>
									<p className="text-sm text-gray-700 whitespace-pre-wrap">
										{job.message}
									</p>
								</div>
							)}
						</div>

						{/* RV Details */}
						{(job.rv_year || job.rv_make || job.rv_model || job.rv_type) && (
							<div className="mb-6">
								<h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
									<Truck className="h-4 w-4" />
									RV Details
								</h3>
								<div className="text-sm">
									{job.rv_year} {job.rv_make} {job.rv_model} {job.rv_type}
								</div>
							</div>
						)}

						{job.warranty_request_id && job.warranty_request.component && (
							<div className="mb-6">
								<h3 className="text-sm font-medium text-gray-900 mb-2">
									Component
								</h3>
								<div className="text-sm">
									{`${job.warranty_request.component.type} - ${job.warranty_request.component.manufacturer}`}
								</div>
							</div>
						)}

						{/* Contact Information */}
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
							<div className="flex items-center gap-3">
								<Mail className="h-5 w-5 text-gray-400" />
								<div>
									<p className="text-sm font-medium text-gray-900">Email</p>
									<p className="text-sm text-gray-600">{job.email}</p>
								</div>
							</div>
							{job.phone && (
								<div className="flex items-center gap-3">
									<Phone className="h-5 w-5 text-gray-400" />
									<div>
										<p className="text-sm font-medium text-gray-900">Phone</p>
										<p className="text-sm text-gray-600">{job.phone}</p>
									</div>
								</div>
							)}
							{job.location && (
								<div className="flex items-center gap-3">
									<MapPin className="h-5 w-5 text-gray-400" />
									<div>
										<p className="text-sm font-medium text-gray-900">
											Location
										</p>
										<p className="text-sm text-gray-600">
											{job.warranty_request_id
												? // For warranty jobs, only show city/state
												job.location.city && job.location.state
													? `${job.location.city}, ${job.location.state}`
													: job.location.address
														? job.location.address
															.split(", ")
															.slice(-2)
															.join(", ")
														: job.location.address
												: // For regular jobs, show full address
												job.location.address}
										</p>
									</div>
								</div>
							)}

							{quote.distance_miles && (
								<div className="flex items-center gap-3">
									<Truck className="h-5 w-5 text-gray-400" />
									<div className="flex-1">
										<div className="flex items-center gap-2">
											<p className="text-sm font-medium text-gray-900">
												Distance
											</p>
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p className="max-w-xs">
															This is an "as the crow flies" distance
															calculation, not actual driving distance. Click
															the map link for real travel time.
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</div>
										<div className="flex items-center gap-2">
											<p className="text-sm text-gray-600">
												{quote.distance_miles.toFixed(1)} miles
											</p>
											{job.location?.address && (
												<a
													href={`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(job.location.address)}`}
													target="_blank"
													rel="noopener noreferrer"
													className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
												>
													<ExternalLink className="h-3 w-3" />
													Get directions
												</a>
											)}
										</div>
									</div>
								</div>
							)}
						</div>

						{/* Resolution Status */}
						{quote.resolution_status && (
							<div className="mb-6">
								<h3 className="text-sm font-medium text-gray-900 mb-2">
									Resolution Status
								</h3>
								<div className="bg-green-50 border border-green-200 rounded-lg p-4">
									<div className="flex items-center gap-2 text-green-800">
										<CheckCircle className="h-5 w-5" />
										<span className="font-medium">
											{quote.resolution_status === ResolutionStatus.COMPLETED
												? "Completed Successfully"
												: quote.resolution_status === ResolutionStatus.CANCELLED
													? "Customer Cancelled"
													: quote.resolution_status ===
														ResolutionStatus.NO_RESPONSE
														? "No Response from Customer"
														: quote.resolution_status ===
															ResolutionStatus.NOT_VIABLE
															? "Not Viable After Contact"
															: quote.resolution_status ===
																ResolutionStatus.REFERRED
																? "Referred to Another Provider"
																: quote.resolution_status ===
																	ResolutionStatus.OTHER
																	? "Other (See Notes)"
																	: "Pending Resolution"}
										</span>
									</div>
									{quote.resolution_notes && (
										<p className="text-sm text-green-700 mt-2">
											{quote.resolution_notes}
										</p>
									)}
								</div>
							</div>
						)}
					</div>
				</div>

				{/* Messaging Area */}
				<div className="bg-white rounded-xl shadow-sm border">
					<div className="p-4 border-b">
						<div className="flex items-center gap-2">
							<MessageSquare className="h-5 w-5 text-[#43806c]" />
							<h3 className="font-semibold text-gray-900">Messages</h3>
						</div>
					</div>
					<div className="h-96">
						<MessageThread
							quoteId={params.id}
							viewerRole="PROVIDER"
							providerId={quote.listing_id}
							onMessageSent={(message) => {
								console.log("Message sent:", message);
							}}
							disableMessages={
								quote.status === QuoteStatus.REJECTED ||
								quote.status === QuoteStatus.COMPLETED ||
								quote.status === QuoteStatus.WITHDRAWN
							}
						/>
					</div>
				</div>
			</div>

			{/* Modals */}
			<UpdateLeadResolutionDialog
				showResolutionDialog={showResolutionDialog}
				setShowResolutionDialog={setShowResolutionDialog}
				resolutionStatus={resolutionStatus}
				setResolutionStatus={setResolutionStatus}
				resolutionDetails={resolutionDetails}
				setResolutionDetails={setResolutionDetails}
				validationError={validationError}
				setValidationError={setValidationError}
				handleResolutionSubmit={handleResolutionSubmit}
				isResponding={isResponding}
			/>
			<AcceptLeadModal
				open={showAcceptModal}
				onOpenChange={setShowAcceptModal}
				quote={quote as unknown as QuoteWithMessages}
				isWarrantyJob={Boolean(job?.warranty_request_id)}
				onResponseSent={handleAcceptLeadResponse}
			/>
			<DeclineLeadModal
				open={showDeclineModal}
				onOpenChange={setShowDeclineModal}
				quote={quote as unknown as QuoteWithMessages}
				onResponseSent={handleDeclineLeadResponse}
			/>
			<WarrantyLeadSuccessDialog
				open={showWarrantySuccessDialog}
				onOpenChange={setShowWarrantySuccessDialog}
				onNavigateToJob={() => {
					setShowWarrantySuccessDialog(false);
					router.push(`/provider/jobs/${params.id}`);
				}}
			/>
			<WarrantyTermsGate
				open={showWarrantyTermsGate}
				onOpenChange={setShowWarrantyTermsGate}
				companyName={warrantyInfo.companyName || "Partner"}
				onAccept={handleAcceptWarrantyTerms}
				isLoading={isAcceptingTerms}
				customerName={
					`${job?.first_name || ""} ${job?.last_name || ""}`.trim() ||
					"Customer"
				}
				location={
					job?.location?.address ||
					`${job?.location?.city || ""}, ${job?.location?.state || ""}`.trim() ||
					"Location Available"
				}
				rvDetails={
					`${job?.rv_year || ""} ${job?.rv_make || ""} ${job?.rv_model || ""} ${job?.rv_type || ""}`.trim() ||
					"RV Details Available"
				}
				issueDescription={
					job?.warranty_request?.complaint ||
					"Issue details will be available after accepting this lead."
				}
				leadId={params.id}
			/>

		</div>
	);
}

export default withAuthorization(LeadDetailsPage, "PROVIDER");
