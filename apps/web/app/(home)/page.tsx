import CoverageMap from "@/components/CoverageMap";
import FeaturedProviderSpotlight from "@/components/FeaturedProviderSpotlight";
import config from "@/config";
import prisma from "@/lib/prisma";
import { url } from "@/lib/url";
import {
	ArrowUpRight,
	Clock,
	MessageSquare,
	Shield,
	Smile
} from "lucide-react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import Script from "next/script";
import CTA from "./cta";
import HeroSection from "./hero";

const assetsUrl = config.assetsUrl;

export const metadata: Metadata = {
	title: "RV Help - Find Mobile RV Technicians & Inspectors Near You",
	description:
		"RV Help connects you with certified mobile RV technicians and inspectors nationwide. Get expert RV repairs, maintenance, and inspections right at your campsite from RVTAA & NRVIA professionals.",
	openGraph: {
		title: "RV Help - Mobile RV Technicians & Inspectors Near You",
		description:
			"Get expert RV service right at your campsite from certified technicians. Pre-purchase inspections, repairs, and maintenance from trusted professionals.",
		images: [
			{
				url: "/images/rvhelp-bg.jpg",
				width: 1200,
				height: 630,
				alt: "RV Help - Mobile RV Service"
			}
		],
		type: "website"
	},
	twitter: {
		card: "summary_large_image",
		title: "RV Help - Mobile RV Technicians & Inspectors",
		description:
			"Find certified RV technicians and inspectors who come to you. Expert RV service, repairs, and inspections from RVTAA & NRVIA professionals.",
		images: ["/images/rvhelp-bg.jpg"]
	},
	alternates: {
		canonical: "https://rvhelp.com"
	},
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true
		}
	},
	keywords:
		"Mobile RV Repair, Mobile RV Technician, RV Inspector, RV Service, RVTAA Certified, NRVIA Certified, Mobile RV Repair, RV Maintenance, Pre-Purchase RV Inspection"
};

async function getFeaturedProviderData() {
	try {
		const article = await prisma.article.findFirst({
			where: {
				type: "blog-post",
				status: "published",
				is_featured_provider: true
			},
			orderBy: {
				published_at: "desc"
			}
		});

		if (!article) {
			return null;
		}

		const provider = await prisma.listing.findFirst({
			where: {
				id: "cm53hg6az06jwfp0y8tx4kz28"
			},
			include: {
				locations: true
			}
		});

		return { article, provider };
	} catch (error) {
		console.error("Error fetching featured provider data:", error);
		return null;
	}
}

export default async function Page() {
	const featuredProviderData = await getFeaturedProviderData();

	return (
		<>
			<Script
				id="schema-rvhelp"
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify({
						"@context": "https://schema.org",
						"@graph": [
							{
								"@type": "WebSite",
								name: "RV Help",
								url: "https://rvhelp.com",
								description:
									"Find certified RV technicians and inspectors who come to you. Expert RV service, repairs, and inspections from RVTAA & NRVIA professionals.",
								potentialAction: {
									"@type": "SearchAction",
									target:
										"https://rvhelp.com/search?category=rv-repair&location={search_term_string}",
									"query-input": "required name=search_term_string"
								}
							},
							{
								"@type": "Organization",
								name: "RV Help",
								alternateName: ["MyRVResource", "My RV Resource", "RVHelp.com"],
								url: "https://rvhelp.com",
								logo: "https://rvhelp-prod.s3.us-east-1.amazonaws.com/public/rvhelp-logo.png",
								sameAs: [
									"https://www.facebook.com/myrvresource",
									"https://www.instagram.com/getrvhelp",
									"https://www.linkedin.com/company/rv-help"
								],
								description:
									"RV Help is North America's trusted directory for finding certified mobile RV technicians and inspectors.",
								contactPoint: {
									"@type": "ContactPoint",
									contactType: "Customer Support",
									areaServed: "US",
									availableLanguage: "English"
								}
							}
						]
					})
				}}
			/>
			<HeroSection />

			<section className="py-16 bg-white text-center">
				<div className="container mx-auto px-4">
					<h2 className="text-4xl font-bold mb-6 text-[#43806c] max-w-4xl mx-auto leading-tight">
						RV Help is your go-to platform for finding trusted RV technicians,
						inspectors, and service providers.
					</h2>
					<p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
						From emergency repairs to pre-purchase inspections, we help RV
						owners travel with peace of mind.
					</p>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-24 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
						{[
							{
								title: "Mobile RV Technicians",
								description:
									"Get expert service right at your campsite from RVTAA certified technicians",
								image: "/public/RV%20Mechanic.jpg",
								href: url("rv-repair")
							},
							{
								title: "Pre-Purchase Inspections",
								description:
									"Make informed buying decisions with thorough inspections from certified NRVIA inspectors",
								image: "/public/RV%20Inspector.jpg",
								href: url("rv-inspection")
							},
							{
								title: "Remote Tech Support",
								description:
									"Get immediate help with technical issues through our virtual support service",
								image: "/public/Working%20Under%20RV.jpg"
							}
						].map((service, index) => (
							<div
								key={index}
								className="bg-gray-50 rounded-xl overflow-hidden shadow-lg transition-transform hover:scale-[1.02]"
							>
								{service.href ? (
									<Link href={service.href}>
										<div className="relative">
											<Image
												src={assetsUrl + service.image}
												alt={service.title}
												width={500} // Adjust width as needed
												height={192} // Adjust height as needed
												className="w-full h-48 object-cover"
											/>
										</div>
										<div className="p-6">
											<h3 className="text-xl font-bold mb-3">
												{service.title}
											</h3>
											<p className="text-gray-600">{service.description}</p>
										</div>
									</Link>
								) : (
									<>
										<div className="relative">
											<img
												src={assetsUrl + service.image}
												alt={service.title}
												className="w-full h-48 object-cover"
											/>
										</div>
										<div className="p-6">
											<h3 className="text-xl font-bold mb-3">
												{service.title}
											</h3>
											<p className="text-gray-600">{service.description}</p>
										</div>
									</>
								)}
							</div>
						))}
					</div>
				</div>
			</section>

			{featuredProviderData && (
				<FeaturedProviderSpotlight
					article={featuredProviderData.article}
					provider={featuredProviderData.provider}
				/>
			)}

			{/* Coverage Map Section */}
			<section className="py-24 bg-gray-50">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
					<h2 className="text-3xl font-bold mb-4">Nationwide Coverage</h2>
					<p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
						Our network of certified RV technicians and inspectors spans across
						North America, ensuring you can find help wherever your journey
						takes you.
					</p>
					<div className="md:bg-white md:p-8 md:rounded-xl md:shadow-lg">
						<CoverageMap />
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section className="py-24 bg-white">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<h2 className="text-3xl font-bold text-center mb-4">
						Why Choose RV Help
					</h2>
					<p className="text-xl text-gray-600 text-center mb-16 max-w-3xl mx-auto">
						RV Help is building the #1 platform for finding trusted RV service
						professionals
					</p>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-12">
						{[
							{
								icon: <Clock className="w-8 h-8 text-[#43806c]" />,
								title: "Faster Service",
								description:
									"Service right at your campsite. Instead of waiting 2-3 months at a service shop, find a nearby mobile tech on RV Help who can come directly to you."
							},
							{
								icon: <Shield className="w-8 h-8 text-[#43806c]" />,
								title: "Trusted Technicians",
								description:
									"Find mobile techs you can trust. Backed by the RV Technicians Association of America. All our techs have been through the National RV training Academy and professionally trained and carry active certification."
							},
							{
								icon: <Smile className="w-8 h-8 text-[#43806c]" />,
								title: "Peace of Mind",
								description:
									"Buy new RVs with confidence. Get them inspected by professionally trained inspectors who know exactly what to look for."
							},
							{
								icon: <MessageSquare className="w-8 h-8 text-[#43806c]" />,
								title: "Easy Communication",
								description:
									"Save time through platform messaging. Instead of having to call multiple techs, message techs directly through RV Help to get fast responses."
							}
						].map((feature, index) => (
							<div key={index} className="text-center">
								<div className="mb-4 flex justify-center">{feature.icon}</div>
								<h3 className="text-xl font-bold mb-3">{feature.title}</h3>
								<p className="text-gray-600">{feature.description}</p>
							</div>
						))}
					</div>
				</div>
			</section>

			<section className="py-24">
				<div className="bg-primary rounded-xl p-12 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="max-w-3xl mx-auto text-center mb-16">
						<h2 className="text-3xl font-bold mb-6 text-white">
							RV Help is Partnered with NRVIA & RVTAA
						</h2>
						<p className="text-gray-50 text-lg">
							We've partnered with the top RV technicians and inspectors
							associations in North America so that you have access to quality
							and professionally trained RV service pros.
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-12">
						<div className="flex flex-col items-center bg-white/5 backdrop-blur-sm rounded-xl p-8">
							<div className="h-24 flex items-start justify-center">
								<Link href="https://rvtaa.org" target="_blank">
									<Image
										src={assetsUrl + "/public/rvtaa-logo.png"}
										alt="RVTAA Logo"
										width={300}
										height={300}
										className="object-contain"
									/>
								</Link>
							</div>

							<div className="text-center">
								<h3 className="text-xl font-bold mb-3 text-white">
									<Link
										href="https://rvtaa.org"
										target="_blank"
										className="hover:underline"
									>
										RV Technicians Association
									</Link>
								</h3>
								<p className="text-gray-100">
									Our technicians are certified by RVTAA.org, ensuring the
									highest standards of RV maintenance and repair expertise.
								</p>
							</div>
						</div>

						<div className="flex flex-col items-center bg-white/5 backdrop-blur-sm rounded-xl p-8">
							<div className="h-24 flex items-start justify-center">
								<Link href="https://nrvia.org" target="_blank">
									<Image
										src={assetsUrl + "/public/nrvia-logo.png"}
										alt="NRVIA Logo"
										width={200}
										height={100}
										className="object-contain"
									/>
								</Link>
							</div>

							<div className="text-center">
								<h3 className="text-xl font-bold mb-3 text-white">
									<Link
										href="https://nrvia.org"
										target="_blank"
										className="hover:underline"
									>
										National RV Inspectors Association
									</Link>
								</h3>
								<p className="text-gray-100">
									Proud partnership with NRVIA.org, guaranteeing professional
									and thorough RV inspections.
								</p>
							</div>
						</div>
					</div>

					<div className="mt-4 text-center">
						<Link
							href={url("rv-repair-certification-levels")}
							className="text-white/90 hover:text-white underline inline-flex items-center gap-1 mt-2"
						>
							Learn about certification levels
							<ArrowUpRight className="w-4 h-4" />
						</Link>
					</div>
				</div>
			</section>

			<CTA />
		</>
	);
}
