import { createHandler } from "@/lib/api/baseHandler";
import { smsService } from "@/lib/services/sms.service";
import { z } from "zod";

async function handler() {
	const { phoneNumber, message } = this.validatedData;

	await smsService.sendToProvider(phoneNumber, message);

	return Response.json({ success: true });
}

export const POST = createHandler(handler, {
	requireAuth: true,
	requiredRole: "ADMIN",
	validateBody: z.object({
		phoneNumber: z.string().min(10),
		message: z.string().min(1).max(160)
	})
});
