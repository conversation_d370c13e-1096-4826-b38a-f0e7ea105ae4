import { createHand<PERSON> } from '@/lib/api/baseHandler';
import { prisma } from '@/lib/prisma';
import { timelineService } from '@/lib/services/timeline.service';
import { ExtendedWarrantyRequest } from '@/types/warranty';
import { JobStatus } from "@rvhelp/database";
import { z } from 'zod';

// Match the expanded schema from the form
const putWarrantyRequestSchema = z.object({
    // Service Request Details
    complaint: z.string().min(10, "Please provide a detailed description of the issue").optional(),
    cause: z.string().optional(),
    correction: z.string().optional(),
    component: z.object({
        type: z.string().min(1, "Component type is required"),
        manufacturer: z.string().min(1, "Manufacturer is required"),
    }).optional().nullable(),

    status: z.enum([
        'REQUEST_CREATED', 'REQUEST_APPROVED', 'REQUEST_REJECTED', 'JOB_REGISTERED',
        'JOB_REQUESTED', 'JOB_ACCEPTED', "JOB_STARTED", "JOB_COMPLETED", "JOB_CANCELLED",
        "AUTHORIZATION_REQUESTED", "AUTHORIZATION_APPROVED", "AUTHORIZATION_REJECTED",
        "AUTHORIZATION_FEEDBACK", "PARTS_ORDERED",
        "INVOICE_CREATED", "INVOICE_PAID"
    ]).optional(), // Optional status field

    // Customer Information
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    email: z.string().email("Valid email is required"),
    phone: z.string().min(10, "Valid phone number is required"),
    sms_opt_in: z.boolean().default(true),
    // contact_preference: z.enum(['email', 'sms', 'phone']).optional(),

    // Location Information
    location: z.object({
        address: z.string().optional(),
        latitude: z.number().optional(),
        longitude: z.number().optional(),
    }).optional(),

    // RV Information
    rv_vin: z.string().min(17, "VIN must be at least 17 characters").max(17, "VIN cannot exceed 17 characters").optional(),
    rv_make: z.string().min(1, "Make is required"),
    rv_model: z.string().min(1, "Model is required"),
    rv_year: z.string().min(1, "Year is required"),
    rv_type: z.string().min(1, "Type is required"),

    onboarding_completed_at: z.string().optional(),

    // Additional Information
    estimated_hours: z.number().min(0).optional(),
    approved_hours: z.number().min(0).optional(),

    event_type: z.enum([
        'CUSTOMER_REGISTERED', 'PREAUTHORIZATION_APPROVED', 'PREAUTHORIZATION_REJECTED', 'AUTHORIZATION_APPROVED', 'AUTHORIZATION_REJECTED', 'INVOICE_PAID'
    ]).optional(),
    update_notes: z.string().optional(),
});

export const PUT = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract createJob query parameter and ID from URL
        const url = new URL(this.req.url);
        const createJob = url.searchParams.get('createJob') === 'true';
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.length - 1];

        const {
            event_type,
            location,
            onboarding_completed_at,
            sms_opt_in,
            ...standardFields
        } = this.validatedData;

        try {
            const existing = await prisma.warrantyRequest.findFirst({
                where: { id },
            });
            if (!existing) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }

            // Prepare update data, handling location separately if it exists
            const updateData: any = {
                ...standardFields,
            };

            if (createJob) {
                updateData.status = "JOB_REGISTERED";
            }

            // Handle location update if provided
            if (location) {
                updateData.location = location;
            }

            if (onboarding_completed_at) {
                updateData.onboarding_completed_at = new Date(onboarding_completed_at);
            }

            // Only create job if createJob parameter is true
            if (createJob) {
                const existingJob = existing.job_id ? await prisma.job.findFirst({
                    where: { id: existing.job_id || null, user_id: this.user.id },
                }) : null;

                if (!existingJob) {
                    const jobData: any = {
                        first_name: updateData.first_name,
                        last_name: updateData.last_name,
                        email: updateData.email,
                        phone: updateData.phone,
                        contact_preference: updateData.contact_preference,
                        sms_opt_in: sms_opt_in, // Use form value or default to true
                        source: "oem",
                        location: updateData.location,
                        status: JobStatus.OPEN,
                        rv_type: updateData.rv_type,
                        rv_make: updateData.rv_make,
                        rv_model: updateData.rv_model,
                        rv_year: updateData.rv_year?.toString() || null,
                        message: `Warranty service request for ${updateData.rv_year} ${updateData.rv_make} ${updateData.rv_model}`,
                        category: "rv-repair",
                        is_premium: true,
                        warranty_request: {
                            connect: {
                                id: existing.id || null,
                            }
                        },
                        user: {
                            connect: {
                                id: this.user.id
                            }
                        }
                    };
                    // Create the job record
                    const job = await prisma.job.create({
                        data: jobData
                    });
                    updateData.job_id = job.id;
                }


                // Create timeline update for customer registration
                await timelineService.createTimelineUpdate({
                    job_id: updateData.job_id,
                    warranty_request_id: existing.id,
                    event_type: "CUSTOMER_REGISTERED",
                    updated_by_id: this.user.id || '',
                    notes: `Customer registered: ${updateData.first_name} ${updateData.last_name}`
                });
            }

            const updatedWarrantyRequest = await prisma.warrantyRequest.update({
                where: { id: existing.id },
                data: updateData,
            }) as ExtendedWarrantyRequest;

            const company = await prisma.company.findFirst({
                where: { id: existing.company_id },
            });
            if (company) {
                updatedWarrantyRequest.company = company;
            }

            return Response.json(updatedWarrantyRequest);
        } catch (error) {
            console.error("Error updating warranty request:", error);
            return Response.json({ error: "Failed to update warranty request" }, { status: 500 });
        }
    }, {
    requireAuth: true,
    validateBody: putWarrantyRequestSchema,
});

export const GET = createHandler(
    async function () {
        if (!this?.user?.email) {
            return Response.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await prisma.user.findFirst({
            where: { email: this.user.email },
        });

        // Extract ID from URL: /api/warranty-requests/[id]
        const urlParts = this.req.url.split('/');
        const id = urlParts[urlParts.length - 1];

        try {
            const warrantyRequest = await prisma.warrantyRequest.findFirst({
                where: { id },
            });
            if (!warrantyRequest) {
                return Response.json({ error: 'Warranty request not found' }, { status: 404 });
            }
            return Response.json(warrantyRequest);
        } catch (error) {
            console.error("Error fetching warranty request:", error);
            return Response.json({ error: "Failed to fetch warranty request" }, { status: 500 });
        }
    },
    { requireAuth: true }
);
