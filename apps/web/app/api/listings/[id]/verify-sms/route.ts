import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { smsService } from "@/lib/services/sms.service";
import { generateVerificationCode } from "@/lib/utils";
import { z } from "zod";

async function handler() {
	const { phone } = this.validatedData;

	const listing = await prisma.listing.findUnique({
		where: { id: this.params.id }
	});

	if (!listing) {
		return this.respond({ error: "Listing not found" }, 404);
	}

	// Generate 6-digit verification code
	const verificationCode = generateVerificationCode();

	// Store verification token
	await prisma.verificationToken.create({
		data: {
			token: verificationCode,
			type: "listing_sms_verification",
			expires: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
			user: { connect: { id: this.user.id } }
		}
	});

	// Send SMS with code
	try {
		await smsService.sendToProvider(
			phone,
			`Your RVHelp verification code is: ${verificationCode}`
		);
	} catch (error) {
		return this.respond(
			{ error: "SMS sending failed", details: error.message },
			500
		);
	}

	return this.respond({ success: true });
}

export const POST = createHandler(handler, {
	requireAuth: true,
	validateBody: z.object({
		phone: z.string().min(10) // Basic phone validation
	})
});
