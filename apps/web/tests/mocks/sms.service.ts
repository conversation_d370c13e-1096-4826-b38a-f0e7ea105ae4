export const mockSMSService = {
	messages: [] as any[],
	reset() {
		this.messages = [];
	},
	send: jest.fn().mockResolvedValue(undefined),
	sendToProvider: jest.fn().mockResolvedValue(undefined),
	sendToUser: jest.fn().mockResolvedValue(undefined),
	validatePhoneNumber: jest.fn().mockResolvedValue(true),
	cleanPhoneNumber: jest.fn().mockImplementation((phone) => phone),
	getLastMessage() {
		return this.messages[this.messages.length - 1];
	},
	getAllMessages() {
		return this.messages;
	}
};
