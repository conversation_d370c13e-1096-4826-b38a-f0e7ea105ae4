// Use the global prisma mock - remove manual override

jest.mock("@/lib/services/sms.service", () => ({
	smsService: {
		send: jest.fn().mockResolvedValue(undefined),
		sendToProvider: jest.fn().mockResolvedValue(undefined),
		sendToUser: jest.fn().mockResolvedValue(undefined)
	}
}));

// Now we can do our imports
import { smsService } from "@/lib/services/sms.service";
import { mockPrisma } from "@/tests/mocks/prisma-mock";
import {
	createMockRequest,
	mockBaseHandler,
	mockUser
} from "@/tests/utils/api-test-utils";

// Import handlers after setting up mocks
import { POST as verifyContact } from "@/app/api/listings/[id]/verify-contact/route";
import { POST as verifySms } from "@/app/api/listings/[id]/verify-sms/route";

describe("Listing Verification API Handlers", () => {
	const mockListing = {
		id: "list123",
		owner_id: mockUser.id,
		phone: "**********",
		email: "<EMAIL>"
	};

	beforeEach(() => {
		jest.clearAllMocks();
		// Set up authentication
		mockBaseHandler.user = mockUser;
		mockBaseHandler.session = { user: mockUser };
		mockBaseHandler.isAuthenticated = true;
		mockBaseHandler.isAdmin = false;
		(mockPrisma.listing.findUnique as jest.Mock).mockResolvedValue(mockListing);
		(mockPrisma.listing.update as jest.Mock).mockResolvedValue(mockListing);
		(smsService.send as jest.Mock).mockClear();
		mockBaseHandler.respond.mockClear();
	});

	describe("SMS Verification Handler", () => {
		it("should send verification code via SMS", async () => {
			const validatedData = {
				phone: "**********"
			};

			const req = createMockRequest({
				method: "POST",
				url: `/api/listings/${mockListing.id}/verify-sms`,
				params: { id: mockListing.id },
				validatedData,
				user: mockUser
			});

			(mockPrisma.verificationToken.create as jest.Mock).mockResolvedValue({
				id: "token123",
				token: "123456"
			});

			await verifySms(req, { params: { id: mockListing.id } });

			expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
				data: expect.objectContaining({
					type: "listing_sms_verification",
					user: { connect: { id: mockUser.id } }
				})
			});

			expect(smsService.sendToProvider).toHaveBeenCalledWith(
				validatedData.phone,
				expect.stringContaining("Your RVHelp verification code is:")
			);

			expect(mockBaseHandler.respond).toHaveBeenCalledWith({ success: true });
		});

		it("should return 404 if listing not found", async () => {
			(mockPrisma.listing.findUnique as jest.Mock).mockResolvedValue(null);

			const req = createMockRequest({
				method: "POST",
				url: `/api/listings/nonexistent/verify-sms`,
				params: { id: "nonexistent" },
				body: { phone: "**********" }
			});

			await verifySms(req, { params: { id: "nonexistent" } });

			expect(mockBaseHandler.respond).toHaveBeenCalledWith(
				{ error: "Listing not found" },
				404
			);
		});
	});

	describe("Contact Verification Handler", () => {
		it("should verify valid SMS code", async () => {
			const validatedData = {
				token: "123456",
				type: "phone"
			};

			const req = createMockRequest({
				method: "POST",
				url: `/api/listings/${mockListing.id}/verify-contact`,
				params: { id: mockListing.id },
				validatedData,
				user: mockUser
			});

			(mockPrisma.verificationToken.findFirst as jest.Mock).mockResolvedValue({
				id: "token123",
				token: "123456",
				expires: new Date(Date.now() + 1000000)
			});

			await verifyContact(req, { params: { id: mockListing.id } });

			expect(mockPrisma.verificationToken.findFirst).toHaveBeenCalledWith({
				where: expect.objectContaining({
					token: "123456",
					type: "listing_sms_verification",
					expires: expect.any(Object)
				})
			});

			expect(mockPrisma.listing.update).toHaveBeenCalledWith({
				where: { id: mockListing.id },
				data: {
					sms_verified_at: expect.any(Date)
				}
			});

			expect(mockBaseHandler.respond).toHaveBeenCalledWith({ success: true });
		});

		it("should verify valid email code", async () => {
			const validatedData = {
				token: "123456",
				type: "email"
			};

			const req = createMockRequest({
				method: "POST",
				url: `/api/listings/${mockListing.id}/verify-contact`,
				params: { id: mockListing.id },
				validatedData
			});

			(mockPrisma.verificationToken.findFirst as jest.Mock).mockResolvedValue({
				id: "token123",
				token: "123456"
			});

			await verifyContact(req, { params: { id: mockListing.id } });

			expect(mockPrisma.verificationToken.findFirst).toHaveBeenCalledWith({
				where: expect.objectContaining({
					token: "123456",
					type: "contact_email_verification"
				})
			});

			expect(mockPrisma.listing.update).toHaveBeenCalledWith({
				where: { id: mockListing.id },
				data: {
					email_verified_at: expect.any(Date)
				}
			});

			expect(mockBaseHandler.respond).toHaveBeenCalledWith({ success: true });
		});

		it("should return 400 for invalid code", async () => {
			const req = createMockRequest({
				method: "POST",
				url: `/api/listings/${mockListing.id}/verify-contact`,
				params: { id: mockListing.id },
				body: {
					token: "123456",
					type: "phone"
				}
			});

			(mockPrisma.verificationToken.findFirst as jest.Mock).mockResolvedValue(null);

			await verifyContact(req, { params: { id: mockListing.id } });

			expect(mockBaseHandler.respond).toHaveBeenCalledWith(
				{ error: "Invalid or expired verification code" },
				400
			);
		});
	});
});
