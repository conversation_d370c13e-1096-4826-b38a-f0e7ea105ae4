import {
    Company,
    Component,
    Listing,
    TimelineUpdate,
    WarrantyRequest,
} from "@rvhelp/database";

export type LocationData = {
    address: string;
    latitude: number;
    longitude: number;
}

export type WarrantyAttachment = {
    id?: string;
    type: string;
    title: string;
    url: string;
    required?: boolean;
    completed?: boolean;
    component_id?: string;
    file?: File;
    status_update?: boolean;
}

export interface FormField {
    name: string;
    type: string;
    index: number;
    value?: string | boolean;
}

export type PricingSettings = {
    dispatch_fee: number;
    hourly_rate: number;
    warranty_rate: number;
    charges_travel_fee: boolean;
    travel_fee: number;
    charges_parts_fee: boolean;
};

export type ExtendedListing = Listing & {
    pricing_settings: PricingSettings
}

export type ReturnDetails = {
    height: number;
    width: number;
    depth: number;
    weight: number;
};

export type ExtendedCompany = Company & {
    components: ExtendedComponent[]
}

export type ExtendedComponent = Component & {
    attachments: WarrantyAttachment[]
}

export type ExtendedWarrantyRequestUpdate = TimelineUpdate & {
    details?: { notes?: string, attachments?: WarrantyAttachment[], return_details?: ReturnDetails } | null;
    updated_by: {
        first_name: string | null;
        last_name: string | null;
    };
};

// Extended type to include metadata and status updates
export type ExtendedWarrantyRequest = WarrantyRequest & {
    location?: LocationData
    company?: Company
    timeline_updates?: ExtendedWarrantyRequestUpdate[]; // Added timeline_updates
    component: Component;
    listing: {
        business_name: string;
        first_name: string;
        last_name: string;
        email: string;
        phone: string;
        pricing_settings?: PricingSettings;
    },
    oem_user: {
        first_name: string;
        last_name: string;
    };
    attachments?: WarrantyAttachment[];
    sms_opt_in?: boolean; // Optional field for API requests, not stored in database
};