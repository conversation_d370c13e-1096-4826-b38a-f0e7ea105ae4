"use client";

import { DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, Phone } from "lucide-react";

import { LoadingDialog } from "@/components/global-modals/send-message/loading-dialog";
import { StepContactInfo } from "@/components/global-modals/send-message/step-contact-info";
import { StepIndicator } from "@/components/global-modals/send-message/step-indicator";
import { StepLocation } from "@/components/global-modals/send-message/step-location";
import { StepMessage } from "@/components/global-modals/send-message/step-message";
import { StepRVDetails } from "@/components/global-modals/send-message/step-rv-details";
import { StepServiceType } from "@/components/global-modals/send-message/step-service-type";
import { SuccessMessage } from "@/components/global-modals/send-message/success-message";
import {
	type MessageProviderFormData,
	messageProviderSchema
} from "@/components/global-modals/send-message/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON>Header
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useModal } from "@/lib/context/modal-context";
import { useAuth } from "@/lib/hooks/useAuth";
import { GoogleAnalyticsService } from "@/lib/services/google-analytics.service";
import { ExistingJob } from "@/lib/services/job.service";
import { zodResolver } from "@hookform/resolvers/zod";
import dynamic from "next/dynamic";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

// Dynamic import for ExistingJobChoiceModal
const ExistingJobChoiceModal = dynamic(
	() => import("@/components/modals/ExistingJobChoiceModal"),
	{ ssr: false }
);

// Add these interfaces at the top of the file
interface StepConfig {
	title: string;
	fields: (keyof MessageProviderFormData)[];
}

// Add this constant after the interfaces
const STEP_CONFIG: Record<number, StepConfig> = {
	1: {
		title: "Your Message",
		fields: ["message"]
	},
	2: {
		title: "Service Type",
		fields: ["category"]
	},
	3: {
		title: "RV Details",
		fields: ["rv_type", "rv_make", "rv_model", "rv_year"]
	},
	4: {
		title: "Service Location",
		fields: ["location"]
	},
	5: {
		title: "Contact Information",
		fields: ["first_name", "last_name", "email", "contact_phone"]
	}
};

function SendMessageModalContent() {
	const {
		selectedListing,
		isSendMessageModalOpen,
		closeSendMessageModal,
		modalOptions
	} = useModal();

	const { user } = useAuth();
	const router = useRouter();
	const searchParams = useSearchParams();
	const searchCategory = searchParams?.get("category");
	const [isLoadingListing, setIsLoadingListing] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isRedirecting, setIsRedirecting] = useState(false);
	const [isSuccess, setIsSuccess] = useState(false);
	const [showExplanation, setShowExplanation] = useState(
		modalOptions?.leadType === "phone" || false
	);
	const [currentStep, setCurrentStep] = useState(1);
	const totalSteps = modalOptions?.leadType === "booking" ? 6 : 5;
	const [stepValidationState, setStepValidationState] = useState<
		Record<number, boolean>
	>({});

	// State for existing job choice (for non-logged-in users)
	const [showExistingJobChoice, setShowExistingJobChoice] = useState(false);
	const [existingJobsData, setExistingJobsData] = useState<{
		existingJobs: ExistingJob[];
		mostRecentActiveJob: ExistingJob | null;
		user: any;
		userStatus: any;
		submittedData: any;
	} | null>(null);

	// Get available categories from the listing
	const availableCategories = useMemo(() => {
		if (!selectedListing?.categories) return [];
		const categories = selectedListing.categories as Record<
			string,
			{ selected: boolean }
		>;
		const available = Object.entries(categories)
			.filter(([_, value]) => value.selected)
			.map(([key]) => key);
		return available;
	}, [selectedListing?.categories]);

	// Initialize form with default values
	const form = useForm<MessageProviderFormData>({
		resolver: zodResolver(messageProviderSchema),
		mode: "onSubmit", // Only validate on submit, not on change or blur
		defaultValues: {
			location: {
				address: "",
				latitude: user?.latitude || 0,
				longitude: user?.longitude || 0
			},

			contact_phone: user?.phone || "",
			category: searchCategory || "",
			first_name: user?.first_name || "",
			last_name: user?.last_name || "",
			email: user?.email || "",
			rv_type: "",
			rv_make: "",
			rv_model: "",
			rv_year: undefined,
			message: "",
			listing_id: "", // Initialize empty, will be set by useEffect
			sms_opt_in: true
		}
	});

	// Add this helper function at the top of the component
	const getCurrentStepConfig = () =>
		STEP_CONFIG[currentStep] || { title: "", fields: [] };

	// Handle form initialization and updates
	useEffect(() => {
		if (!isSendMessageModalOpen) return;

		// Reset interaction state when modal opens
		setIsSuccess(false);

		// Set listing ID immediately when modal opens - this should NEVER be overridden
		if (selectedListing?.id) {
			form.setValue("listing_id", selectedListing.id);
		}

		// Only reset step if we're not already in a step
		if (currentStep === 1) {
			setCurrentStep(1);
		}

		// Note: Removed the immediate validation for first-time users
	}, [isSendMessageModalOpen, form, currentStep]);

	// Handle selectedListing changes - ensure listing_id is always correct
	useEffect(() => {
		if (selectedListing?.id) {
			form.setValue("listing_id", selectedListing.id);
			form.trigger("listing_id");
		}
	}, [selectedListing?.id, form]);

	// Remove automatic validation - only validate when user clicks Next/Save
	useEffect(() => {
		const subscription = form.watch((_, { name, type }) => {
			if (name && type === "change") {
				// Only mark as interacted if it's a user-initiated change, not programmatic
				// We detect this by checking if the change comes from user input
				const activeElement = document.activeElement;
				const isUserInput =
					activeElement &&
					(activeElement.tagName === "INPUT" ||
						activeElement.tagName === "TEXTAREA" ||
						activeElement.tagName === "SELECT" ||
						activeElement.getAttribute("role") === "combobox");
			}
		});
		return () => subscription.unsubscribe();
	}, [form]);

	// Remove automatic validation on step change - only validate when user clicks Next/Save

	// Handle form submission
	const onSubmit = async (data: MessageProviderFormData) => {
		try {
			setIsSubmitting(true);

			// Ensure listing_id is set correctly before submission
			if (selectedListing?.id && data.listing_id !== selectedListing.id) {
				data.listing_id = selectedListing.id;
			}

			const response = await fetch(`/api/jobs`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(data)
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(
					`Failed to submit form: ${response.status} - ${errorText}`
				);
			}

			const result = await response.json();

			// Track lead form conversion
			if (selectedListing?.id) {
				try {
					GoogleAnalyticsService.trackLeadFormSubmission({
						listingId: selectedListing.id,
						listingName:
							selectedListing.business_name || selectedListing.first_name,
						category: data.category,
						userId: user?.id,
						isAnonymous: !user
					});
				} catch (trackingError) {
					console.error("Failed to track lead form conversion:", trackingError);
					// Don't fail the submission if tracking fails
				}
			}

			// Check if existing jobs were found (for non-logged-in users)
			if (result.hasExistingJobs) {
				console.log("🔍 SendMessageModal: Existing jobs found:", {
					jobCount: result.existingJobs?.length,
					mostRecentJob: result.mostRecentActiveJob,
					userStatus: result.userStatus
				});

				// Store the submitted data and show existing job choice modal
				setExistingJobsData({
					existingJobs: result.existingJobs,
					mostRecentActiveJob: result.mostRecentActiveJob,
					user: result.user,
					userStatus: result.userStatus,
					submittedData: result.submittedData
				});
				setShowExistingJobChoice(true);
				return;
			}

			console.log(
				"🔍 SendMessageModal: No existing jobs found, proceeding with normal flow"
			);

			// Set redirecting state and keep modal open briefly
			setIsRedirecting(true);

			// If user is logged in, redirect to service request page
			// Otherwise, redirect to setup-password page for account creation
			if (user) {
				router.push(`/service-requests/${result.data.id}`);
			} else {
				router.push(`/service-requests/${result.data.id}/setup-password`);
			}

			// Close modal after a brief delay to allow redirect to start
			setTimeout(() => {
				closeSendMessageModal();
			}, 500);
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "An error occurred";
			setError(errorMessage);
			setIsRedirecting(false);
			toast.error(errorMessage);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Handle creating a new job when user chooses to do so from existing job choice modal
	const handleCreateNewJob = async () => {
		if (!existingJobsData) return;

		try {
			setIsSubmitting(true);
			setShowExistingJobChoice(false);

			// Submit the original data to create a new job
			const response = await fetch(`/api/jobs?force=true`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify(existingJobsData.submittedData)
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(
					`Failed to submit form: ${response.status} - ${errorText}`
				);
			}

			const result = await response.json();

			// Set redirecting state and keep modal open briefly
			setIsRedirecting(true);

			// Redirect to setup-password page for account creation
			router.push(`/service-requests/${result.data.id}/setup-password`);

			// Close modal after a brief delay to allow redirect to start
			setTimeout(() => {
				closeSendMessageModal();
			}, 500);
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "An error occurred";
			setError(errorMessage);
			setIsRedirecting(false);
			toast.error(errorMessage);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Set default category when available categories change
	useEffect(() => {
		if (!isSendMessageModalOpen || availableCategories.length === 0) return;

		const currentCategory = form.getValues("category");

		// If no category is set or current category is not available
		if (!currentCategory || !availableCategories.includes(currentCategory)) {
			// Update service type based on search category or available categories
			if (searchCategory && availableCategories.includes(searchCategory)) {
				form.setValue("category", searchCategory, { shouldValidate: true });
			} else if (availableCategories.length > 0) {
				const priorityOrder = ["rv-repair", "rv-inspection"];
				const defaultCategory =
					priorityOrder.find((cat) => availableCategories.includes(cat)) ||
					availableCategories[0];
				form.setValue("category", defaultCategory, { shouldValidate: true });
			}
		}
	}, [isSendMessageModalOpen, availableCategories, searchCategory, form]);

	// Update form with user data and search parameters
	useEffect(() => {
		if (!isSendMessageModalOpen || !user) return;

		// Update user data
		form.setValue("first_name", user.first_name || "");
		form.setValue("last_name", user.last_name || "");
		form.setValue("email", user.email || "");
		form.setValue("contact_phone", user.phone || "");

		// Update location if available
		if (user.latitude && user.longitude) {
			form.setValue("location", {
				...form.getValues("location"),
				latitude: user.latitude,
				longitude: user.longitude
			});
		}

		// Update RV details if available in user data
		if (user.rv_details) {
			const rvDetails = user.rv_details as {
				year?: string | number;
				make?: string;
				model?: string;
				type?: string;
			};

			if (rvDetails.type) {
				form.setValue("rv_type", rvDetails.type);
			}
			if (rvDetails.make) {
				form.setValue("rv_make", rvDetails.make);
			}
			if (rvDetails.model) {
				form.setValue("rv_model", rvDetails.model);
			}
			if (rvDetails.year) {
				form.setValue(
					"rv_year",
					typeof rvDetails.year === "string"
						? parseInt(rvDetails.year)
						: rvDetails.year
				);
			}
		}
	}, [isSendMessageModalOpen, user, form]);

	// Handle keyboard events for form submission
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			// Only allow Enter key submission if the user has interacted with the form
			if (
				e.key === "Enter" &&
				currentStep === totalSteps &&
				document.activeElement?.tagName !== "BUTTON"
			) {
				// Check if the active element is an input field
				if (
					document.activeElement?.tagName === "INPUT" ||
					document.activeElement?.tagName === "TEXTAREA"
				) {
					form.handleSubmit(onSubmit)();
				}
			}
		};

		// Add event listener
		document.addEventListener("keydown", handleKeyDown);

		// Clean up
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [currentStep, totalSteps]);

	// Reset explanation state when modal options change
	useEffect(() => {
		setShowExplanation(modalOptions?.leadType === "phone" || false);
	}, [modalOptions]);

	// Handle step validation changes
	const handleStepValidChange = (step: number, isValid: boolean) => {
		setStepValidationState((prev) => ({
			...prev,
			[step]: isValid
		}));
	};

	// Replace the goToNextStep function with this
	const goToNextStep = async () => {
		const { fields } = getCurrentStepConfig();

		// Check if current step has custom validation that failed
		const isStepValid = stepValidationState[currentStep] !== false;

		// Only validate when user clicks Next
		const isFormValid = await form.trigger(fields as any);

		if (isFormValid && isStepValid) {
			// Ensure listing_id is correct before saving
			const currentData = form.getValues();
			if (
				selectedListing?.id &&
				currentData.listing_id !== selectedListing.id
			) {
				form.setValue("listing_id", selectedListing.id);
			}

			// Move to next step
			const nextStep = Math.min(currentStep + 1, totalSteps);
			setCurrentStep(nextStep);
		} else {
			// Mark all fields in current step as touched to show validation errors
			fields.forEach((field) => {
				form.setFocus(field);
				// Explicitly mark the field as touched so validation errors show
				form.setValue(field, form.getValues(field), { shouldTouch: true });
			});

			// Trigger validation again to update touched state
			setTimeout(() => {
				form.trigger(fields as any);
			}, 100);

			// Show appropriate error message
			if (!isStepValid) {
				toast.error("Please confirm your location selection");
			} else {
				toast.error("Please fill in all required fields");
			}
		}
	};

	// Replace the goToPreviousStep function with this
	const goToPreviousStep = () => {
		// Ensure listing_id is correct before saving
		const currentData = form.getValues();
		if (selectedListing?.id && currentData.listing_id !== selectedListing.id) {
			console.warn(
				"⚠️ Fixing listing_id before saving form state (previous step):",
				{
					form_listing_id: currentData.listing_id,
					selectedListing_id: selectedListing.id
				}
			);
			form.setValue("listing_id", selectedListing.id);
		}

		const prevStep = Math.max(currentStep - 1, 1);
		setCurrentStep(prevStep);
	};

	// Replace the getStepTitle function with this
	const getStepTitle = (step: number) => {
		if (step === 0) return modalOptions?.title || "";
		return STEP_CONFIG[step]?.title || "";
	};

	// Check if listing is unverified
	const isUnverified =
		selectedListing &&
		selectedListing.rv_help_verification_level !== "VERIFIED" &&
		selectedListing.rv_help_verification_level !== "CERTIFIED_PRO";

	// If there's an error loading the listing, show error state
	if (error) {
		return (
			<Dialog
				open={isSendMessageModalOpen}
				onOpenChange={closeSendMessageModal}
			>
				<DialogContent className="max-w-xl overflow-visible">
					<DialogHeader>
						<DialogTitle>Error</DialogTitle>
					</DialogHeader>
					<div className="space-y-4 text-center">
						<p className="text-lg text-red-600">
							{process.env.NODE_ENV === "development"
								? error
								: "We're sorry, but something went wrong. Please try again or contact support if the issue persists."}
						</p>
						<div className="flex justify-center gap-3">
							<Button onClick={() => setError(null)} variant="default">
								Try Again
							</Button>
							<Button onClick={closeSendMessageModal} variant="outline">
								Close
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		);
	}

	// If we're loading the listing, show loading state
	if (isLoadingListing) {
		return (
			<LoadingDialog
				isOpen={isSendMessageModalOpen}
				onOpenChange={closeSendMessageModal}
			/>
		);
	}

	return (
		<Suspense
			fallback={
				<LoadingDialog
					isOpen={isSendMessageModalOpen}
					onOpenChange={closeSendMessageModal}
				/>
			}
		>
			<Dialog
				open={isSendMessageModalOpen}
				onOpenChange={(open) => {
					// Prevent closing modal while redirecting
					if (!open && !isRedirecting) {
						closeSendMessageModal();
					}
				}}
			>
				<DialogContent className="sm:max-w-[750px] relative">
					{/* Full-page loading overlay */}
					{(isSubmitting || isRedirecting) && (
						<div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center rounded-lg">
							<div className="text-center space-y-4">
								<Loader2 className="w-8 h-8 animate-spin text-primary mx-auto" />
								<div className="space-y-2">
									<p className="text-lg font-medium text-gray-900">
										{isSubmitting
											? "Sending your message..."
											: "Redirecting..."}
									</p>
									<p className="text-sm text-gray-600">
										{isSubmitting
											? "Please wait while we process your request"
											: "Taking you to your service request"}
									</p>
								</div>
							</div>
						</div>
					)}

					<DialogHeader>
						{/* Step indicator */}
						<div className="">
							<StepIndicator
								currentStep={currentStep}
								totalSteps={totalSteps}
								stepTitle={getStepTitle(currentStep)}
							/>
						</div>
						<DialogTitle className="text-2xl font-semibold text-primary text-center">
							<div className="mt-4">{getStepTitle(currentStep)}</div>
						</DialogTitle>
					</DialogHeader>

					{/* Warning Banner for Unverified Listings */}
					{isUnverified && !isSuccess && !showExplanation && (
						<div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4 flex items-start gap-3">
							<AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
							<div className="text-sm">
								<div className="font-medium text-orange-800 mb-2">
									⚠️ Unverified Provider Warning
								</div>
								<div className="text-orange-700">
									This provider has not completed our verification process. We
									cannot guarantee their contact information is accurate or that
									they will be responsive to your message.
								</div>
							</div>
						</div>
					)}

					{isSuccess ? (
						<SuccessMessage
							onClose={closeSendMessageModal}
							providerPhone={selectedListing?.phone}
							providerName={selectedListing?.business_name}
							leadType={modalOptions?.leadType || "message"}
						/>
					) : showExplanation ? (
						<div className="flex flex-col items-center justify-center py-8 px-4">
							<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
								<Phone className="w-8 h-8 text-primary" />
							</div>
							<div className="text-center space-y-3 max-w-lg mx-auto">
								<h3 className="text-2xl font-semibold text-primary ">
									{modalOptions?.title}
								</h3>
								<div className="space-y-4">
									<p className="text-muted-foreground text-base leading-relaxed">
										{modalOptions?.description}
									</p>
									<p className="text-sm text-muted-foreground/80 italic">
										RV Help never shares your information with third parties. We
										collect this information to help our service providers stay
										organized and respond to you promptly.
									</p>
								</div>
							</div>
							<Button
								type="button"
								onClick={() => setShowExplanation(false)}
								className="mt-8 min-w-[200px]"
								size="lg"
							>
								Continue
							</Button>
						</div>
					) : (
						<Form {...form}>
							<form className="space-y-4 pb-4">
								{/* Hidden field to distract password managers */}
								<input
									type="text"
									name="username"
									style={{ display: "none" }}
									tabIndex={-1}
									aria-hidden="true"
								/>

								{/* Step 1: Message */}
								{currentStep === 1 && <StepMessage form={form} />}

								{/* Step 2: Service Type */}
								{currentStep === 2 && availableCategories.length > 0 && (
									<StepServiceType
										form={form}
										availableCategories={availableCategories}
									/>
								)}

								{/* Step 3: RV Details */}
								{currentStep === 3 && <StepRVDetails form={form} />}

								{/* Step 4: Location */}
								{currentStep === 4 && (
									<div className="min-h-[200px]">
										{(() => {
											const locationErrors = form.formState.errors.location;
											if (locationErrors) {
											}
											return null;
										})()}
										<StepLocation
											form={form}
											onStepValidChange={(isValid) =>
												handleStepValidChange(4, isValid)
											}
										/>
									</div>
								)}

								{/* Step 5: Contact Information */}
								{currentStep === 5 && (
									<StepContactInfo
										form={form}
										user={user}
										leadType={modalOptions?.leadType}
									/>
								)}

								<DialogFooter className="flex gap-3 pt-4 ">
									{currentStep > 1 && (
										<Button
											type="button"
											variant="outline"
											onClick={goToPreviousStep}
											disabled={isSubmitting || isRedirecting}
											className="min-w-[100px] transition-all duration-200"
										>
											Back
										</Button>
									)}
									{currentStep < totalSteps ? (
										<Button
											type="button"
											onClick={() => {
												goToNextStep();
											}}
											disabled={isSubmitting || isRedirecting}
											className="min-w-[100px] transition-all duration-200"
										>
											Next
										</Button>
									) : (
										<Button
											type="button"
											onClick={async () => {
												// Final safety check: ensure listing_id is correct
												const currentData = form.getValues();
												if (
													selectedListing?.id &&
													currentData.listing_id !== selectedListing.id
												) {
													form.setValue("listing_id", selectedListing.id);
												}

												// Validate the current step before submitting
												const { fields } = getCurrentStepConfig();
												const isValid = await form.trigger(fields as any);

												if (isValid) {
													form.handleSubmit(onSubmit)();
												} else {
													// Mark all fields as touched to show validation errors
													fields.forEach((field) => {
														form.setFocus(field);
														// Explicitly mark the field as touched so validation errors show
														form.setValue(field, form.getValues(field), {
															shouldTouch: true
														});
													});

													// Trigger validation again to update touched state
													setTimeout(() => {
														form.trigger(fields as any);
													}, 100);

													toast.error("Please fill in all required fields");
												}
											}}
											disabled={isSubmitting || isRedirecting}
											className="min-w-[140px] transition-all duration-200"
										>
											{isSubmitting ? (
												<div className="flex items-center gap-2">
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
													<span className="text-white">Sending...</span>
												</div>
											) : isRedirecting ? (
												<div className="flex items-center gap-2">
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
													<span className="text-white">Redirecting...</span>
												</div>
											) : (
												<>
													{modalOptions?.leadType === "phone"
														? "View Phone Number"
														: modalOptions?.leadType === "booking"
															? "Complete Booking"
															: "Send Message"}
												</>
											)}
										</Button>
									)}
								</DialogFooter>
							</form>
						</Form>
					)}
				</DialogContent>
			</Dialog>

			{/* Existing Job Choice Modal for non-logged-in users */}
			{existingJobsData && (
				<ExistingJobChoiceModal
					isOpen={showExistingJobChoice}
					onClose={() => setShowExistingJobChoice(false)}
					mostRecentActiveJob={existingJobsData.mostRecentActiveJob}
					onCreateNewJob={handleCreateNewJob}
					listing={selectedListing}
				/>
			)}
		</Suspense>
	);
}

// Create the dynamic component
const SendMessageModal = dynamic(
	() => Promise.resolve(SendMessageModalContent),
	{
		ssr: false
	}
);

export default SendMessageModal;
