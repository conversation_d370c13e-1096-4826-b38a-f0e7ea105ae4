import { z } from "zod";

export const messageProviderSchema = z.object({
    listing_id: z.string().min(1, "Please enter a listing id"),
    location: z.object({
        address: z.string().min(1, "Please enter a location"),
        latitude: z.number().refine((val) => val !== 0, "Please select a location from the suggestions"),
        longitude: z.number().refine((val) => val !== 0, "Please select a location from the suggestions"),
        city: z.string().optional(),
        state: z.string().optional(),
        zip: z.string().optional(),
        country: z.string().optional()
    }).refine((data) => {
        // Ensure both latitude and longitude are valid coordinates (not 0,0)
        return data.latitude !== 0 && data.longitude !== 0;
    }, {
        message: "Please select a location from the suggestions",
        path: ["address"] // Show error on the address field
    }),
    contact_preference: z.enum(["sms", "phone"]).optional(),
    contact_phone: z.string().optional(),
    phone: z.string().optional(),
    sms_opt_in: z.boolean().default(true),
    category: z.string({
        required_error: "Please select a service type"
    }),
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    email: z.string().email("Invalid email format"),
    // RV Details fields - now required
    rv_type: z.string().min(1, "RV type is required"),
    rv_make: z.string().min(1, "RV make is required"),
    rv_model: z.string().min(1, "RV model is required"),
    rv_year: z.number().min(1900, "Please enter a valid year").max(new Date().getFullYear() + 1, "Year cannot be in the future"),
    // Booking fields
    preferred_date: z.string().optional(),
    preferred_time_slot: z.enum(["8am-12pm", "12pm-5pm", "5pm-9pm"]).optional(),
    alternative_dates: z.array(z.string()).optional(),
    alternative_time_slots: z
        .array(z.enum(["8am-12pm", "12pm-5pm", "5pm-9pm"]))
        .optional(),
    warranty_request_id: z.string().optional(),
    message: z
        .string()
        .min(25, "Message must be at least 25 characters")
        .max(1000, "Message cannot exceed 1000 characters")
}).refine((data) => {
    return !!(data.contact_phone && data.contact_phone.length > 0) || !!(data.phone && data.phone.length > 0);
}, {
    message: "Phone number is required",
    path: ["contact_phone"]
});

export type MessageProviderFormData = z.infer<typeof messageProviderSchema>;
