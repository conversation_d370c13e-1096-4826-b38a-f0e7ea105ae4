"use client";

import PhoneInput from "@/components/PhoneInput";
import { Checkbox } from "@/components/ui/checkbox";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel
} from "@/components/ui/form";
import type { UseFormReturn } from "react-hook-form";
import type { MessageProviderFormData } from "./types";

interface StepContactInfoProps {
	form: UseFormReturn<MessageProviderFormData>;
	user: any;
	leadType?: "message" | "phone" | "booking";
}

export function StepContactInfo({
	form,
	user,
	leadType
}: StepContactInfoProps) {
	return (
		<div className="space-y-4">
			<div>
				<div className="flex items-center gap-2">
					<h3 className="text-lg font-medium">Contact Information</h3>
				</div>
				<p className="text-sm text-gray-600 mt-1">
					{leadType === "phone"
						? "How would you like the service provider to contact you if they're not immediately available to answer the phone?"
						: "How would you like the service provider to contact you?"}
				</p>
			</div>

			{/* First Name Field */}
			<FormField
				control={form.control}
				name="first_name"
				render={({ field }) => (
					<FormItem>
						<FormLabel required={true}>First Name</FormLabel>
						<FormControl>
							<input
								type="text"
								className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
								placeholder={user?.first_name || "Enter your first name"}
								{...field}
							/>
						</FormControl>
						{form.formState.errors.first_name &&
							form.formState.touchedFields.first_name && (
								<p className="text-sm text-red-500">
									{form.formState.errors.first_name.message}
								</p>
							)}
					</FormItem>
				)}
			/>

			{/* Last Name Field */}
			<FormField
				control={form.control}
				name="last_name"
				render={({ field }) => (
					<FormItem>
						<FormLabel required={true}>Last Name</FormLabel>
						<FormControl>
							<input
								type="text"
								className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
								placeholder={user?.last_name || "Enter your last name"}
								{...field}
							/>
						</FormControl>
						{form.formState.errors.last_name &&
							form.formState.touchedFields.last_name && (
								<p className="text-sm text-red-500">
									{form.formState.errors.last_name.message}
								</p>
							)}
					</FormItem>
				)}
			/>

			{/* Email Field */}
			<FormField
				control={form.control}
				name="email"
				render={({ field }) => (
					<FormItem>
						<FormLabel required={true}>Email</FormLabel>
						<FormControl>
							<input
								type="email"
								className="flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50"
								placeholder={user?.email || "Enter your email"}
								{...field}
							/>
						</FormControl>
						{form.formState.errors.email &&
							form.formState.touchedFields.email && (
								<p className="text-sm text-red-500">
									{form.formState.errors.email.message}
								</p>
							)}
					</FormItem>
				)}
			/>

			{/* Contact Phone Field */}
			<PhoneInput
				name="contact_phone"
				label="Phone Number"
				required={true}
				value={form.getValues("contact_phone")}
				onChange={(value) => form.setValue("contact_phone", value)}
				error={form.formState.errors.contact_phone?.message}
				warning={form.formState.errors.contact_phone?.message}
				className="w-full"
			/>

			{/* SMS Opt-in Field */}
			<FormField
				control={form.control}
				name="sms_opt_in"
				render={({ field }) => (
					<FormItem className="flex flex-row items-start space-x-3 space-y-0">
						<FormControl>
							<Checkbox
								checked={field.value}
								onCheckedChange={field.onChange}
							/>
						</FormControl>
						<div className="space-y-1 leading-none">
							<FormLabel className="text-sm font-normal">
								I agree to receive SMS messages from RV Help regarding my service request.
							</FormLabel>
							<p className="text-xs text-gray-500">
								Message & data rates may apply. Reply STOP to unsubscribe. By checking this box, you consent to receive automated text messages regarding your service request.
							</p>
						</div>
					</FormItem>
				)}
			/>
		</div>
	);
}
