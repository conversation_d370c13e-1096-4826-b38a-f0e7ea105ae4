const isDevelopment =
	process.env.NEXT_PUBLIC_VERCEL_ENV !== "production" ||
	process.env.NODE_ENV !== "production" ||
	process.env.NEXT_PUBLIC_APP_URL?.includes("dev.") ||
	false;

interface Config {
	appUrl: string;
	assetsUrl: string;
	apps: {
		appStore: {
			id: string;
			url: string;
		};
		googlePlay: {
			id: string;
			url: string;
		};
	};
	activeCampaign: {
		apiKey: string;
	};
	aws: {
		accessKeyId: string;
		bucket: string;
		region: string;
		secretAccessKey: string;
	};
	cronSecret: string;
	email: {
		allowedDomains: string[];
		allowedEmails: string[];
		from: string;
		fromName: string;
	};
	firstPromoter: {
		apiKey: string;
		companyId: string;
	};
	google: {
		apiKey: string;
		serverApiKey: string;
		iosApiKey: string;
	};
	intercom: {
		accessToken: string;
		appId: string;
		bugTicketTypeId: string;
		featureTicketTypeId: string;
	};
	mailcoach: {
		apiToken: string;
	};
	isDevelopment: boolean;
	isProMembershipAvailable: boolean;
	MAPBOX_PUBLIC_TOKEN: string;
	mysql: {
		host: string;
		user: string;
		password: string;
		database: string;
	};
	posthog: {
		apiKey: string;
		apiHost: string;
	};
	qstash: {
		currentSigningKey: string;
		nextSigningKey: string;
		token: string;
	};
	RESEND_API_KEY: string;
	rvsg: {
		username: string;
		password: string;
		webhookApiKey: string;
	};
	redis: {
		url: string;
	};
	server: string;
	stripe: {
		publishableKey: string;
		secretKey: string;
		membership: {
			standard: {
				id: string;
				priceId: string;
			};
			premium: {
				id: string;
				priceId: string;
			};
		};
	};
	twilio: {
		accountSid: string;
		authToken: string;
		providerFromNumber: string;
		userFromNumber: string;
		allowedNumbers: string[];
	};
}

const config: Config = {
	appUrl: process.env.NEXT_PUBLIC_APP_URL as string,
	apps: {
		appStore: {
			id: "**********",
			url: "https://apps.apple.com/app/rv-help/id**********"
		},
		googlePlay: {
			id: "4973418377716260612",
			url: "https://play.google.com/store/apps/details?id=com.rvhelp.app"
		}
	},
	activeCampaign: {
		apiKey: process.env.ACTIVE_CAMPAIGN_API_KEY as string
	},
	assetsUrl: process.env.NEXT_PUBLIC_ASSET_URL as string,
	aws: {
		accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
		bucket: process.env.AWS_BUCKET as string,
		region: process.env.AWS_REGION as string,
		secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string
	},
	cronSecret: process.env.CRON_SECRET as string,
	mysql: {
		host: process.env.MYSQL_HOST as string,
		user: process.env.MYSQL_USER as string,
		password: process.env.MYSQL_PASSWORD as string,
		database: process.env.MYSQL_DATABASE as string
	},
	email: {
		allowedDomains: ["rvhelp.com"],
		allowedEmails: [
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>"
		],
		from: (process.env.EMAIL_FROM as string) || "RV Help <<EMAIL>>",
		fromName: (process.env.EMAIL_FROM_NAME as string) || "RV Help"
	},
	firstPromoter: {
		apiKey: process.env.FIRST_PROMOTER_API_KEY as string,
		companyId: "trd9cish"
	},
	google: {
		apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string, // Client-side (restricted)
		serverApiKey: process.env.GOOGLE_MAPS_SERVER_API_KEY as string, // Server-side (unrestricted)
		iosApiKey: process.env.IOS_GOOGLE_MAPS_KEY as string
	},
	intercom: {
		accessToken: process.env.INTERCOM_ACCESS_TOKEN as string,
		appId: process.env.NEXT_PUBLIC_INTERCOM_APP_ID as string,
		bugTicketTypeId: process.env.INTERCOM_BUG_TICKET_TYPE_ID as string,
		featureTicketTypeId: process.env.INTERCOM_FEATURE_TICKET_TYPE_ID as string
	},
	mailcoach: {
		apiToken: process.env.MAILCOACH_TOKEN as string
	},
	isDevelopment,
	isProMembershipAvailable: false,
	MAPBOX_PUBLIC_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN as string,
	posthog: {
		apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY as string,
		apiHost: process.env.NEXT_PUBLIC_POSTHOG_HOST as string
	},
	qstash: {
		currentSigningKey: process.env.QSTASH_CURRENT_SIGNING_KEY as string,
		nextSigningKey: process.env.QSTASH_NEXT_SIGNING_KEY as string,
		token: process.env.QSTASH_TOKEN as string
	},
	redis: {
		url: process.env.REDIS_URL as string
	},
	RESEND_API_KEY: process.env.RESEND_API_KEY as string,
	rvsg: {
		username: process.env.RVSG_USERNAME as string,
		password: process.env.RVSG_PASSWORD as string,
		webhookApiKey: process.env.RVSG_WEBHOOK_API_KEY as string
	},
	server: process.env.NEXT_PUBLIC_APP_URL as string,
	stripe: {
		publishableKey: process.env.STRIPE_PUBLISHABLE_KEY as string,
		secretKey: process.env.STRIPE_SECRET_KEY as string,
		membership: {
			standard: {
				id: process.env.STRIPE_STANDARD_MEMBERSHIP_ID as string,
				priceId: process.env.STRIPE_STANDARD_MEMBERSHIP_PRICE_ID as string
			},
			premium: {
				id: process.env.STRIPE_PREMIUM_MEMBERSHIP_ID as string,
				priceId: process.env.STRIPE_PREMIUM_MEMBERSHIP_PRICE_ID as string
			}
		}
	},
	twilio: {
		accountSid: process.env.TWILIO_ACCOUNT_SID as string,
		authToken: process.env.TWILIO_AUTH_TOKEN as string,
		providerFromNumber: process.env.TWILIO_PROVIDER_FROM_NUMBER as string,
		userFromNumber: process.env.TWILIO_USER_FROM_NUMBER as string,
		allowedNumbers: ["+***********"]
	}
};

export const defaultAvatarImage = config.assetsUrl + "/images/avatar.png";

export default config;
