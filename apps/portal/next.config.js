const path = require("path");
/** @type {import('next').NextConfig} */
const nextConfig = {
	reactStrictMode: true,
	images: {
		remotePatterns: [
			{ hostname: "rvservicegroup.com" },
			{ hostname: "*.amazonaws.com" },
			{ hostname: "avatars.githubusercontent.com" },
			{ hostname: "localhost", port: "4000" },
			{ hostname: "secure.gravatar.com" }
		]
	},
	// Allow transpiling monorepo packages and resolving outside the app dir
	transpilePackages: ["@rvhelp/database", "@rvhelp/services"],
	experimental: {
		externalDir: true
	},

	webpack: (config, { isServer }) => {
		config.module.rules.push({
			test: /\.md$/,
			use: "raw-loader"
		});

		// Resolve workspace packages to source during app build to avoid requiring built dist
		const dbSrc = path.resolve(
			__dirname,
			"../../packages/database/src/index.ts"
		);
		const servicesSrc = path.resolve(
			__dirname,
			"../../packages/services/src/index.ts"
		);
		config.resolve.alias = {
			...(config.resolve.alias || {}),
			"@rvhelp/database": dbSrc,
			"@rvhelp/services": servicesSrc
		};

		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				tls: false,
				child_process: false,
				"fs/promises": false,
				async_hooks: false
			};
		}

		return config;
	},
	async rewrites() {
		return [
			{
				source: "/sitemap.xml",
				destination: "/public/sitemap.xml"
			},
			{
				source: "/sitemap-:path*.xml",
				destination: "/public/sitemap-:path*.xml"
			}
		];
	},
	// Skip TypeScript errors in production build when SKIP_TYPE_CHECK env var is set
	typescript: {
		// !! ONLY USE THIS FOR TEST BRANCHES !!
		// This will skip TypeScript errors during build
		ignoreBuildErrors: process.env.SKIP_TYPE_CHECK
	},
	// Also skip ESLint errors if needed
	eslint: {
		// Skip ESLint during build when SKIP_TYPE_CHECK is set
		ignoreDuringBuilds: process.env.SKIP_TYPE_CHECK
	}
};

module.exports = nextConfig;
