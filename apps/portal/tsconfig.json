{
	"compilerOptions": {
		"lib": ["dom", "dom.iterable", "esnext"],
		"allowJs": true,
		"skipLibCheck": true,
		"strict": false,
		"noEmit": true,
		"incremental": true,
		"module": "esnext",
		"esModuleInterop": true,
		"moduleResolution": "node",
		"resolveJsonModule": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"plugins": [
			{
				"name": "next"
			}
		],
		"baseUrl": ".",
		"paths": {
			"@/*": ["./*"],
			"@rvhelp/database": ["../../packages/database/src/index.ts"],
			"@rvhelp/*": ["../../packages/*"],
			"@lib/*": ["lib/*"]
		}
	},
	"include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx"],
	"exclude": [
		"node_modules",
		"dist",
		".next",
		".next/**/*",
		"tests/**/*",
		"scripts/**/*",
		"prisma/seed/**/*",
		"prisma/seed.js",
		// Exclude shared package prisma seeds (handles both copied and external paths)
		"packages/**/prisma/**",
		"../../packages/database/prisma/seed.ts",
		"../../packages/**/prisma/**",
		"**/*.local.*"
	]
}
