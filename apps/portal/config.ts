const isDevelopment =
    process.env.NEXT_PUBLIC_VERCEL_ENV !== 'production' ||
    process.env.NODE_ENV !== 'production' ||
    process.env.NEXT_PUBLIC_APP_URL?.includes('dev.') ||
    false;

interface Config {
    appUrl: string;
    assetsUrl: string;
    aws: {
        accessKeyId: string;
        bucket: string;
        region: string;
        secretAccessKey: string;
    };
    cronSecret: string;
    email: {
        allowedDomains: string[];
        allowedEmails: string[];
        from: string;
        fromName: string;
    };
    google: {
        apiKey: string;
    };
    stripe: {
        publishableKey: string;
        secretKey: string;
    };
    isDevelopment: boolean;
    MAPBOX_PUBLIC_TOKEN: string;
    server: string;
    rvhelpUrl: string;
    mailcoach: {
        apiToken: string;
    };
}

const config: Config = {
    appUrl: process.env.NEXT_PUBLIC_APP_URL as string,
    assetsUrl: process.env.NEXT_PUBLIC_ASSET_URL as string,
    aws: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
        bucket: process.env.AWS_BUCKET as string,
        region: process.env.AWS_REGION as string,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
    },
    cronSecret: process.env.CRON_SECRET as string,
    email: {
        allowedDomains: ['rvhelp.com'],
        allowedEmails: [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ],
        from: (process.env.EMAIL_FROM as string) || 'RV Help <<EMAIL>>',
        fromName: (process.env.EMAIL_FROM_NAME as string) || 'RV Help',
    },
    google: {
        apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string,
    },
    stripe: {
        publishableKey: process.env.STRIPE_PUBLISHABLE_KEY as string,
        secretKey: process.env.STRIPE_SECRET_KEY as string,
    },
    isDevelopment,
    MAPBOX_PUBLIC_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN as string,
    server: process.env.NEXT_PUBLIC_APP_URL as string,
    rvhelpUrl: process.env.RVHELP_PREVIEW_URL || ('https://dev.rvhelp.com' as string),
    mailcoach: {
        apiToken: process.env.MAILCOACH_TOKEN as string
    },
};

export const defaultAvatarImage = config.assetsUrl + '/images/avatar.png';

export default config;
