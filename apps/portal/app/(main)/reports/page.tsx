import { AnalyticsSection } from "@/components/dashboard/analytics-section";
import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function ReportsPage() {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
        redirect("/auth/login");
    }

    // Only allow admins to access reports
    if (session.user.role !== "ADMIN") {
        redirect("/dashboard");
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Warranty Reports</h1>
                <p className="text-gray-600 mt-2">
                    Analytics and performance metrics for warranty requests
                </p>
            </div>

            <AnalyticsSection />
        </div>
    );
}
