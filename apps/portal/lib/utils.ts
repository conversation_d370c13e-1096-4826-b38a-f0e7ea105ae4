// @ts-nocheck
import prisma from '@/lib/prisma';
import { clsx } from 'clsx';
import he from 'he';
import createDOMPurifier from 'isomorphic-dompurify';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs) {
    return twMerge(clsx(inputs));
}

export async function generateSlug(name: string): Promise<string> {
    const slug = name
        .toLowerCase()
        .replace(/[^\w ]+/g, '')
        .replace(/ +/g, '-');

    // check if slug already exists
    const existing = await prisma.listing.findUnique({ where: { slug } });
    if (existing) {
        return `${slug}-${Math.random().toString(36).substring(2, 5)}`;
    }
    return slug;
}

export function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
    }).format(amount / 100);
}

// get full address from any prisma model
export function getFullAddress(model: any) {
    return `${model.address_line_1} ${model.address_line_2}, ${model.city}, ${model.state}, ${model.postcode}, ${model.country}`;
}

// decode html entities
export function decodeHtmlEntities(str: string) {
    return str.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');
}

// validate phone number
export function validatePhoneNumber(phone: string) {
    // minimum 7 digits, can include country code, spaces, and dashes
    return phone.length >= 7 && /^\+?[\d\s-()]+$/.test(phone);
}

// strip html tags from a string
export function stripHtmlTags(str: string) {
    return str.replace(/<[^>]*>?/g, '');
}

export function formatMessageTime(timestamp: string | Date) {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;

    // Check if date is valid
    if (isNaN(date.getTime())) {
        console.error('Invalid date:', timestamp);
        return '';
    }

    return new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true,
    }).format(date);
}
export function generateRandomPassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    // Ensure at least one uppercase, one lowercase, and one number
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
    password += '0123456789'[Math.floor(Math.random() * 10)];

    // Fill the rest randomly
    for (let i = password.length; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password
        .split('')
        .sort(() => Math.random() - 0.5)
        .join('');
}

export const formatToE164 = (phone: string | undefined) => {
    if (!phone) return '';

    // Remove any non-digit characters except + at the start
    const cleaned = phone.replace(/[^\d+]/g, '');

    // Ensure it starts with +
    if (!cleaned.startsWith('+')) {
        return `+${cleaned}`;
    }

    // if it doesn't have a country code, add it
    if (cleaned.length === 10) {
        return `+1${cleaned}`;
    }

    return cleaned;
};

export function sanitizeShortDescription(description: string) {
    // First decode all HTML entities to their corresponding characters
    const decodedDescription = he.decode(description);

    // Strip all HTML tags while preserving the text content
    const strippedDescription = createDOMPurifier.sanitize(decodedDescription, {
        ALLOWED_TAGS: [], // Empty array means no tags allowed
        ALLOWED_ATTR: [], // No attributes allowed
    });

    // Normalize whitespace: replace multiple spaces with single space
    const normalizedDescription = strippedDescription.replace(/\s+/g, ' ').trim();

    return normalizedDescription;
}

export function generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

export const generateBreadcrumbSchema = (items: { label: string; href?: string }[]) => {
    return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: items.map((item, index) => ({
            '@type': 'ListItem',
            position: index + 1,
            item: {
                '@id': item.href ? `${process.env.NEXT_PUBLIC_SITE_URL}${item.href}` : '',
                name: item.label,
            },
        })),
    };
};

// Generate or get existing session ID
export const getSessionId = () => {
    if (typeof window === 'undefined') return '';

    let sessionId = localStorage.getItem('visitor_session_id');
    if (!sessionId) {
        sessionId = Math.random().toString(36).substring(2);
        localStorage.setItem('visitor_session_id', sessionId);
    }
    return sessionId;
};

export function generateToken(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Lighten a hex color by a given amount (0-1)
 * @param hex string, e.g. #43806c
 * @param amount number, 0 = no change, 1 = white
 * @returns string, lightened hex color
 */
export function lightenColor(hex: string, amount: number = 0.5): string {
    if (!hex) return hex;
    let c = hex.replace('#', '');
    if (c.length === 3) {
        c = c[0] + c[0] + c[1] + c[1] + c[2] + c[2];
    }
    if (c.length !== 6) return hex;
    const num = parseInt(c, 16);
    let r = (num >> 16) + Math.round((255 - (num >> 16)) * amount);
    let g = ((num >> 8) & 0x00ff) + Math.round((255 - ((num >> 8) & 0x00ff)) * amount);
    let b = (num & 0x0000ff) + Math.round((255 - (num & 0x0000ff)) * amount);
    r = Math.min(255, r);
    g = Math.min(255, g);
    b = Math.min(255, b);
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
}
