import { prisma } from './prisma';
import { PrismaAdapter } from '@auth/prisma-adapter';
import bcrypt from 'bcryptjs';
import { AuthOptions } from 'next-auth';
import { Adapter } from 'next-auth/adapters';
import CredentialsProvider from 'next-auth/providers/credentials';
import { signOut as nextAuthSignOut } from 'next-auth/react';

export const authOptions: AuthOptions = {
    adapter: PrismaAdapter(prisma) as Adapter,
    providers: [
        CredentialsProvider({
            name: 'credentials',
            credentials: {
                email: { label: 'Email', type: 'email' },
                password: { label: 'Password', type: 'password' },
                signInToken: { label: 'Sign In Token', type: 'text' },
                impersonationToken: { label: 'Impersonation Token', type: 'text' },
            },

            async authorize(credentials) {
                if (!credentials?.email) {
                    throw new Error('Invalid credentials');
                }

                // Check for impersonation token first
                if (credentials.impersonationToken) {
                    const token = await prisma.verificationToken.findFirst({
                        where: {
                            token: credentials.impersonationToken,
                            type: 'impersonation',
                            expires: { gt: new Date() },
                        },
                        include: { user: true },
                    });

                    if (token?.user) {
                        return {
                            ...token.user,
                            isImpersonating: true,
                            originalAdminId: token.admin_id,
                        };
                    }
                }

                // Use case-insensitive email lookup
                const user = await prisma.user.findFirst({
                    where: {
                        email: {
                            equals: credentials.email,
                            mode: 'insensitive',
                        },
                    },
                });

                if (!user) {
                    throw new Error('Invalid credentials');
                }

                // Check for auto sign-in token
                if (credentials.signInToken) {
                    const validToken = await prisma.verificationToken.findFirst({
                        where: {
                            token: credentials.signInToken,
                            user_id: user.id,
                            type: 'auto_signin',
                            expires: { gt: new Date() },
                        },
                    });

                    if (validToken) {
                        await prisma.verificationToken.delete({
                            where: { id: validToken.id },
                        });
                        return user;
                    }
                }

                // Regular password authentication
                if (credentials.password) {
                    const isValidPassword = await bcrypt.compare(
                        credentials.password,
                        user.password ?? ''
                    );
                    if (!isValidPassword) {
                        throw new Error('Invalid credentials');
                    }
                    return user;
                }

                throw new Error('Invalid credentials');
            },
        }),
    ],
    callbacks: {
        async session({ session, token }: { session: any; token: any }) {
            if (session?.user) {
                session.user.isEmailVerified = token.isEmailVerified;
                session.user.id = token.sub as string;
                session.user.role = token.role as string;
                session.user.isImpersonating = token.isImpersonating;
                session.user.originalAdminId = token.originalAdminId;
            }
            return session;
        },
        async jwt({ token, user }: { token: any; user: any }) {
            if (user) {
                await prisma.user.update({
                    where: { id: user.id },
                    data: { last_login: new Date() },
                });

                token.isEmailVerified = !!user.email_verified_at;
                token.role = user.role;
                token.id = user.id;
                token.isImpersonating = user.isImpersonating;
                token.originalAdminId = user.originalAdminId;
            }
            return token;
        },
    },
    pages: {
        signIn: '/login',
        error: '/auth/error',
    },
    session: {
        strategy: 'jwt',
    },
    cookies: {
        sessionToken: {
            name: `portal.next-auth.session-token`,
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
            },
        },
        callbackUrl: {
            name: `portal.next-auth.callback-url`,
            options: {
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
            },
        },
        csrfToken: {
            name: `portal.next-auth.csrf-token`,
            options: {
                httpOnly: true,
                sameSite: 'lax',
                path: '/',
                secure: process.env.NODE_ENV === 'production',
            },
        },
    },
    secret: process.env.NEXTAUTH_SECRET_PORTAL || process.env.NEXTAUTH_SECRET,
};

export async function signOut() {
    await nextAuthSignOut({
        redirect: true,
        callbackUrl: '/login',
    });
}
