import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { Prisma, User } from '@rvhelp/database';
import jwt from 'jsonwebtoken';
import { getServerSession } from 'next-auth';
import { headers } from 'next/headers';

export class UserService {

    static async user(req?: Request): Promise<User | null> {
        // For web requests, try to get the session
        const session = await getServerSession(authOptions);
        if (session?.user?.email) {
            return await prisma.user.findFirst({
                where: { email: session.user.email },
                include: {
                    company: true,
                },
            });
        }

        // Try to get authorization header either from request or headers()
        let authHeader: string | null = null;
        try {
            authHeader = req?.headers?.get('Authorization') || headers().get('Authorization');
        } catch (e) {
            console.error(e);
            // headers() might throw if not in a Server Component context
            // we'll just continue with null in that case
        }

        if (authHeader) {
            const token = authHeader.replace('Bearer ', '');
            // You'll need to implement the token verification logic here
            // and find the user based on the token
            const decoded = jwt.verify(token, process.env.JWT_SECRET!);

            return await prisma.user.findFirst({
                where: { id: decoded.userId },
            });
        }

        return null;
    }

    static async findById(userId: string): Promise<User | null> {
        return await prisma.user.findUnique({ where: { id: userId } });
    }

    static async get(): Promise<User[]> {
        return await prisma.user.findMany();
    }

    static async searchUsers(search: string): Promise<User[]> {
        return await prisma.user.findMany({
            where: {
                OR: [
                    { email: { contains: search, mode: 'insensitive' } },
                    { first_name: { contains: search, mode: 'insensitive' } },
                    { last_name: { contains: search, mode: 'insensitive' } },
                ],
            },
            orderBy: { created_at: 'desc' },
        });
    }

    static async createUser(data: {
        email: string;
        firstName: string;
        lastName: string;
        role: string;
        password?: string;
    }): Promise<User> {
        // if this email already exists, return the existing user
        const existingUser = await prisma.user.findFirst({
            where: {
                email: {
                    equals: data.email,
                    mode: 'insensitive',
                },
            },
        });
        if (existingUser) return existingUser;

        return await prisma.user.create({
            data: {
                email: data.email,
                first_name: data.firstName,
                last_name: data.lastName,
                role: data.role,
                password: data.password,
                notification_preferences: {
                    create: {
                        messages: { email: true, sms: false },
                        requests: { email: true, sms: false },
                        quotes: { email: true, sms: false },
                    },
                },
            },
        });
    }

    static async isAdmin(user: User | null): Promise<boolean> {
        return !!user?.role && user.role === 'ADMIN';
    }

    static async update(userId: string, data: Prisma.UserUpdateInput) {
        try {
            if (data.email) {
                data.email = data.email.toString().toLowerCase();
            }
            return await prisma.user.update({
                where: { id: userId },
                data,
            });
        } finally {
            await prisma.$disconnect();
        }
    }

    static async signUp(email: string, firstName: string, lastName: string): Promise<User> {
        const result = await this.createUser({
            email: email.toLowerCase(),
            firstName,
            lastName,
            role: 'USER',
        });

        return result;
    }
}
