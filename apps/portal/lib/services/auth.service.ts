import prisma from '@/lib/prisma';
import { generateToken } from '@/lib/utils/token';
import { PrismaClient, User } from '@rvhelp/database';

import PasswordChangeEmail from '@/components/email-templates/PasswordChangeEmail';

import { Client } from '@upstash/qstash';
import bcrypt from 'bcryptjs';

import { AuthError } from '@/lib/errors/AppError';
import { emailService } from '@lib/services';
import { z } from 'zod';

// Validation schemas
const loginSchema = z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(1, 'Password is required'),
    redirectUrl: z.string().optional(),
});

type LoginCredentials = z.infer<typeof loginSchema>;

export class AuthService {
    private prisma: PrismaClient;
    private qstash: Client;

    constructor() {
        this.prisma = prisma;
        this.qstash = new Client({
            token: process.env.QSTASH_TOKEN!,
        });
    }

    async login(credentials: LoginCredentials) {
        const validated = loginSchema.parse(credentials);
        const user = await this.prisma.user.findUnique({
            where: { email: validated.email },
        });

        if (!user?.password) {
            throw new Error('Invalid credentials');
        }

        const isValidPassword = await bcrypt.compare(validated.password, user.password);

        if (!isValidPassword) {
            throw new Error('Invalid credentials');
        }

        // Update last login timestamp
        await this.prisma.user.update({
            where: { id: user.id },
            data: { last_login: new Date() },
        });

        return {
            ...user,
            redirectUrl: validated.redirectUrl,
        };
    }

    async resetPassword(token: string, newPassword: string): Promise<void> {
        const resetToken = await this.prisma.verificationToken.findFirst({
            where: {
                token,
                expires: { gt: new Date() },
                type: 'password_reset',
            },
            include: { user: true },
        });

        if (!resetToken?.user) {
            throw new AuthError('Invalid or expired reset token');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);

        await this.prisma.$transaction([
            this.prisma.user.update({
                where: { id: resetToken.user.id },
                data: {
                    password: hashedPassword,
                    // Verify email if it's not already verified
                    email_verified_at: resetToken.user.email_verified_at || new Date(),
                },
            }),
            this.prisma.verificationToken.delete({
                where: { id: resetToken.id },
            }),
        ]);
    }

    async forgotPassword(user: User, isMobile: boolean = false): Promise<void> {
        if (!user) {
            throw new Error('User not found');
        }

        // Delete existing reset tokens
        await this.prisma.verificationToken.deleteMany({
            where: {
                user_id: user.id,
                type: 'password_reset',
            },
        });

        // Create new reset token
        const resetToken = generateToken();
        const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);

        await this.prisma.verificationToken.create({
            data: {
                token: resetToken,
                expires: tokenExpiry,
                user_id: user.id,
                type: 'password_reset',
            },
        });

        await emailService.sendPasswordResetEmail(user.email, resetToken, isMobile);
    }

    async changePassword(
        userId: string,
        currentPassword: string,
        newPassword: string
    ): Promise<void> {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });

        if (!user?.password) {
            throw new Error('User not found');
        }

        const isValidPassword = await bcrypt.compare(currentPassword, user.password);
        if (!isValidPassword) {
            throw new Error('Current password is incorrect');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);

        await this.prisma.user.update({
            where: { id: userId },
            data: { password: hashedPassword },
        });

        // Send password change notification email
        await emailService.send({
            to: user.email,
            subject: 'Your password has been changed',
            react: PasswordChangeEmail({
                firstName: user.first_name,
                timestamp: new Date().toLocaleString(),
            }),
        });
    }
}

// Export singleton instance
export const authService = new AuthService();
