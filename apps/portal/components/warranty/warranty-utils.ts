import { ExtendedWarrantyRequest } from '@/types/warranty';

export function formatDate(date: Date | string) {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
}

export function getStatusColor(status: string) {
    switch (status) {
        case 'REQUEST_CREATED':
            return 'bg-amber-100 text-amber-800 border border-amber-200';
        case 'REQUEST_APPROVED':
            return 'bg-green-100 text-emerald-800 border border-emerald-200';
        case 'REQUEST_REJECTED':
            return 'bg-red-100 text-red-800 border border-red-200';
        case 'JOB_REGISTERED':
            return 'bg-blue-100 text-blue-800 border border-blue-200';
        case 'JOB_REQUESTED':
            return 'bg-blue-100 text-blue-800 border border-blue-200';
        case 'JOB_ACCEPTED':
            return 'bg-blue-100 text-blue-800 border border-blue-200';
        case 'JOB_STARTED':
            return 'bg-green-100 text-green-800 border border-green-200';
        case 'JOB_COMPLETED':
            return 'bg-gray-100 text-gray-800 border border-gray-200';
        case 'JOB_CANCELLED':
            return 'bg-red-100 text-red-800 border border-red-200';
        case 'AUTHORIZATION_REQUESTED':
            return 'bg-amber-100 text-amber-800 border border-amber-200';
        case 'AUTHORIZATION_APPROVED':
            return 'bg-green-100 text-green-800 border border-green-200';
        case 'AUTHORIZATION_REJECTED':
            return 'bg-red-100 text-red-800 border border-red-200';
        case 'AUTHORIZATION_FEEDBACK':
            return 'bg-blue-100 text-amber-800 border border-amber-200';
        case 'PARTS_ORDERED':
            return 'bg-blue-100 text-emerald-800 border border-emerald-200';
        case 'INVOICE_CREATED':
            return 'bg-amber-100 text-amber-800 border border-amber-200';
        case 'INVOICE_PAID':
            return 'bg-emerald-100 text-emerald-800 border border-emerald-200';
        default:
            return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

export function getEventTypeColor(eventType: string) {
    switch (eventType) {
        case 'REQUEST_CREATED':
            return 'bg-amber-100 text-amber-800 border border-amber-200';
        default:
            return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

// Add helper function to check if status is amber
export function isPendingApproval(status: string, role: string = "OEM") {
    if (role === 'ADMIN') {
        return ['REQUEST_CREATED', 'AUTHORIZATION_REQUESTED', 'INVOICE_CREATED'].includes(status);
    }
    return ['REQUEST_CREATED', 'AUTHORIZATION_REQUESTED'].includes(status);
}

export enum WarrantyRequestState {
    CREATED = 'CREATED',
    // PREAUTHORIZED = 'PREAUTHORIZED', // Request Created and Pre-Authorized for x hours

    REGISTERED = 'REGISTERED', // Customer Registered with RV Help
    INVITED = 'INVITED', // Customer Invited providers to perform work
    ACCEPTED = 'ACCEPTED', // Customer Accepted a provider
    STARTED = 'STARTED', // Customer and Provider have agreed and a quote has been accepted

    PENDING = 'PENDING', // OEM is reviewing the request
    AUTHORIZED = 'AUTHORIZED', // OEM Authorized the request
    FEEDBACK = "FEEDBACK",
    REJECTED = 'REJECTED', // OEM Rejected the request
    PARTS = 'PARTS', // Parts Ordered

    INVOICED = 'INVOICED', // Provider has invoiced the OEM
    PAID = 'PAID', // OEM has paid the invoice
    COMPLETED = 'COMPLETED', // Customer has received the work
    CANCELLED = 'CANCELLED', // Customer or Provider has cancelled the request
}

export function getWarrantyRequestState(request: ExtendedWarrantyRequest) {

    if (request.status === 'REQUEST_CREATED') {
        return { state: 'PENDING', color: getStatusColor(request.status) };
    } else if (request.status === 'REQUEST_REJECTED') {
        return { state: 'REJECTED', color: getStatusColor(request.status) };
    } else if (request.status === 'REQUEST_APPROVED') {
        return { state: 'CREATED', color: getStatusColor(request.status) };
    } else if (request.status === 'JOB_REGISTERED') {
        return { state: 'REGISTERED', color: getStatusColor(request.status) };
    } else if (request.status === 'JOB_REQUESTED') {
        return { state: 'INVITED', color: getStatusColor(request.status) };
    } else if (request.status === 'JOB_ACCEPTED') {
        return { state: 'ACCEPTED', color: getStatusColor(request.status) };
    } else if (request.status === 'JOB_STARTED') {
        return { state: 'STARTED', color: getStatusColor(request.status) };

    } else if (request.status === 'AUTHORIZATION_REQUESTED') {
        return { state: 'PENDING', color: getStatusColor(request.status) };
    } else if (request.status === 'AUTHORIZATION_APPROVED') {
        return { state: 'APPROVED', color: getStatusColor(request.status) };
    } else if (request.status === 'AUTHORIZATION_FEEDBACK') {
        return { state: 'FEEDBACK', color: getStatusColor(request.status) };
    } else if (request.status === 'AUTHORIZATION_REJECTED') {
        return { state: 'REJECTED', color: getStatusColor(request.status) };

    } else if (request.status === 'PARTS_ORDERED') {
        return { state: 'PARTS', color: getStatusColor(request.status) };

    } else if (request.status === 'INVOICE_CREATED') {
        return { state: 'INVOICED', color: getStatusColor(request.status) };
    } else if (request.status === 'INVOICE_PAID') {
        return { state: 'PAID', color: getStatusColor(request.status) };
    }

    else if (request.status === 'JOB_CANCELLED') {
        return { state: 'CANCELLED', color: getStatusColor(request.status) };
    } else if (request.status === 'JOB_COMPLETED') {
        return { state: 'DONE', color: getStatusColor(request.status) };
    }
    return { state: 'UNKNOWN', color: 'bg-gray-100 text-gray-800 border border-gray-200' };
}
