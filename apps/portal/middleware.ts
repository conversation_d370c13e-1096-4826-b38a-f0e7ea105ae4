import { getToken } from 'next-auth/jwt';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const protectedRoutes = ['/dashboard', '/profile'];

const routeIsProtected = (pathname: string) => {
    return protectedRoutes.some((route) => pathname.startsWith(route));
};

export async function middleware(request: NextRequest) {
    try {
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set('x-pathname', request.nextUrl.pathname);
        // Get IP address
        const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
        requestHeaders.set('x-real-ip', ip);

        const token = await getToken({ req: request });

        if (!token?.sub) {
            return NextResponse.next({
                request: { headers: requestHeaders },
            });
        }

        return NextResponse.next({
            request: { headers: requestHeaders },
        });
    } catch (error) {
        console.error('Middleware Error:', error);
        throw error;
    }
}

// Configure middleware matching
export const config = {
    matcher: [
        // Protected paths that require auth and user state validation
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
    ],
};
