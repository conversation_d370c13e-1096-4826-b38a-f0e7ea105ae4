import { Company, Component, TimelineUpdate, WarrantyRequest } from '@rvhelp/database';

export type LocationData = {
    address: string;
    latitude: number;
    longitude: number;
};

export type WarrantyAttachment = {
    id?: string;
    type: string;
    title: string;
    url: string;
    required?: boolean;
    completed?: boolean;
    component_id?: string;
    file?: File;
};

export type PricingSettings = {
    dispatch_fee: number;
    hourly_rate: number;
    warranty_rate: number;
    charges_travel_fee: boolean;
    travel_fee: number;
    charges_parts_fee: boolean;
};

export type ReturnDetails = {
    height: number;
    width: number;
    depth: number;
    weight: number;
};

export type ExtendedCompany = Company & {
    components: ExtendedComponent[];
};

export type ExtendedComponent = Component & {
    attachments: WarrantyAttachment[];
};

// Define type for status update with user details
export type ExtendedWarrantyRequestUpdate = TimelineUpdate & {
    details?: { notes?: string; attachments?: WarrantyAttachment[], return_details?: ReturnDetails } | null;
    updated_by: {
        first_name: string | null;
        last_name: string | null;
    };
};

// Extended type to include metadata and status updates
export type ExtendedWarrantyRequest = WarrantyRequest & {
    location?: LocationData;
    company?: ExtendedCompany;
    component?: ExtendedComponent;
    oem_user?: {
        first_name: string | null;
        last_name: string | null;
        email: string | null;
        role: string | null;
    };
    listing?: {
        first_name: string | null;
        last_name: string | null;
        email: string | null;
        phone: string | null;
        business_name: string | null;
        pricing_settings: PricingSettings;
    };
    attachments?: WarrantyAttachment[];
    timeline_updates?: ExtendedWarrantyRequestUpdate[];
};
