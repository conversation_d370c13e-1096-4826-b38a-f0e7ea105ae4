import { PrismaClient, User as PrismaUser } from '@rvhelp/database';
declare global {
    var prisma: PrismaClient | undefined;
}

export interface RankingFactors {
    score: number;
    distance_score: number;
    certification_score: number;
    seniority_score: number;
    reviews_score: number;
    probability?: number;
    daily_seed?: number;
}

export type ListingWithLocation = Listing & {
    location: Location;
    ranking_factors?: RankingFactors;
};

// listing with reviews
type ListingWithReviews = Listing & {
    reviews: Review[];
};

type ListingWithReviewsAndLocation = ListingWithReviews & {
    location: Location;
};

interface Global {
    TextDecoder: typeof TextDecoder;
    TextEncoder: typeof TextEncoder;
}

// user with rv details
type User = PrismaUser & {
    rv_details: {
        type: string;
        year: number;
        make: string;
        model: string;
    };
};
