import NextAuth, { DefaultSession } from 'next-auth';
import { JWT } from 'next-auth/jwt';

declare module 'next-auth' {
    interface Session extends DefaultSession {
        user: {
            id: string;
            role: string;
            company_id?: string;
        } & DefaultSession['user'];
    }

    interface User {
        role: string;
        company_id?: string;
    }
}

declare module 'next-auth/jwt' {
    interface JWT {
        role: string;
        company_id?: string;
    }
}
