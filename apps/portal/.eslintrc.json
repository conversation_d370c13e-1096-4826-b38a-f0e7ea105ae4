{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "plugins": ["react", "@typescript-eslint", "unused-imports"], "rules": {"prefer-const": "error", "react/no-unescaped-entities": ["error", {"forbid": [">", "\"", "}"]}], "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}]}, "parser": "@typescript-eslint/parser"}